<?php
/**
 * Simple test script to verify session management implementation
 * Run with: php test_session_management.php
 */

// Configuration
$baseUrl = 'http://localhost:8000/api/user';
$testEmail = '<EMAIL>';
$testPassword = 'password123';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers),
        CURLOPT_POSTFIELDS => $data ? json_encode($data) : null,
        CURLOPT_HEADER => true,
        CURLOPT_NOBODY => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    curl_close($ch);
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    return [
        'status' => $httpCode,
        'headers' => $headers,
        'body' => json_decode($body, true),
        'raw_body' => $body
    ];
}

function testStep($description, $callback) {
    echo "\n" . str_repeat('=', 60) . "\n";
    echo "TEST: $description\n";
    echo str_repeat('=', 60) . "\n";
    
    try {
        $result = $callback();
        echo "✅ PASSED\n";
        return $result;
    } catch (Exception $e) {
        echo "❌ FAILED: " . $e->getMessage() . "\n";
        return null;
    }
}

// Test 1: First Login (should succeed)
$firstLoginToken = testStep("First Login - Should Succeed", function() use ($baseUrl, $testEmail, $testPassword) {
    $response = makeRequest("$baseUrl/login", 'POST', [
        'email' => $testEmail,
        'password' => $testPassword
    ]);
    
    if ($response['status'] !== 200) {
        throw new Exception("Expected 200, got {$response['status']}. Response: " . json_encode($response['body']));
    }
    
    if (!isset($response['body']['access_token'])) {
        throw new Exception("No access token in response");
    }
    
    if (!isset($response['body']['session_id'])) {
        throw new Exception("No session_id in response");
    }
    
    echo "Token: " . substr($response['body']['access_token'], 0, 20) . "...\n";
    echo "Session ID: " . $response['body']['session_id'] . "\n";
    
    return $response['body']['access_token'];
});

// Test 2: Second Login (should return session conflict)
testStep("Second Login - Should Return Session Conflict", function() use ($baseUrl, $testEmail, $testPassword) {
    $response = makeRequest("$baseUrl/login", 'POST', [
        'email' => $testEmail,
        'password' => $testPassword
    ]);
    
    if ($response['status'] !== 409) {
        throw new Exception("Expected 409 (Conflict), got {$response['status']}. Response: " . json_encode($response['body']));
    }
    
    if ($response['body']['code'] !== 'SESSION_CONFLICT') {
        throw new Exception("Expected SESSION_CONFLICT code, got: " . ($response['body']['code'] ?? 'none'));
    }
    
    echo "Conflict Message: " . $response['body']['message'] . "\n";
});

// Test 3: Force Login (should succeed and invalidate previous session)
$secondLoginToken = testStep("Force Login - Should Succeed", function() use ($baseUrl, $testEmail, $testPassword) {
    $response = makeRequest("$baseUrl/login", 'POST', [
        'email' => $testEmail,
        'password' => $testPassword,
        'force_login' => true
    ]);
    
    if ($response['status'] !== 200) {
        throw new Exception("Expected 200, got {$response['status']}. Response: " . json_encode($response['body']));
    }
    
    if (!isset($response['body']['access_token'])) {
        throw new Exception("No access token in response");
    }
    
    echo "New Token: " . substr($response['body']['access_token'], 0, 20) . "...\n";
    echo "New Session ID: " . $response['body']['session_id'] . "\n";
    
    return $response['body']['access_token'];
});

// Test 4: First token should now be invalid
if ($firstLoginToken) {
    testStep("First Token Should Be Invalid", function() use ($baseUrl, $firstLoginToken) {
        $response = makeRequest("$baseUrl/me", 'GET', null, [
            "Authorization: Bearer $firstLoginToken"
        ]);
        
        if ($response['status'] !== 401) {
            throw new Exception("Expected 401 (Unauthorized), got {$response['status']}. First token should be invalid now.");
        }
        
        if ($response['body']['code'] !== 'SESSION_EXPIRED_NEW_LOGIN') {
            echo "Warning: Expected SESSION_EXPIRED_NEW_LOGIN code, got: " . ($response['body']['code'] ?? 'none') . "\n";
        }
        
        echo "First token correctly invalidated\n";
    });
}

// Test 5: Second token should work
if ($secondLoginToken) {
    testStep("Second Token Should Work", function() use ($baseUrl, $secondLoginToken) {
        $response = makeRequest("$baseUrl/me", 'GET', null, [
            "Authorization: Bearer $secondLoginToken"
        ]);
        
        if ($response['status'] !== 200) {
            throw new Exception("Expected 200, got {$response['status']}. Response: " . json_encode($response['body']));
        }
        
        echo "User: " . ($response['body']['data']['name'] ?? 'Unknown') . "\n";
        echo "Email: " . ($response['body']['data']['email'] ?? 'Unknown') . "\n";
    });
}

// Test 6: List active sessions
if ($secondLoginToken) {
    testStep("List Active Sessions", function() use ($baseUrl, $secondLoginToken) {
        $response = makeRequest("$baseUrl/sessions", 'GET', null, [
            "Authorization: Bearer $secondLoginToken"
        ]);
        
        if ($response['status'] !== 200) {
            throw new Exception("Expected 200, got {$response['status']}. Response: " . json_encode($response['body']));
        }
        
        $sessionCount = $response['body']['total'] ?? 0;
        echo "Active sessions: $sessionCount\n";
        
        if (isset($response['body']['sessions'])) {
            foreach ($response['body']['sessions'] as $session) {
                echo "- Session: " . $session['session_id'] . " (" . $session['device_info'] . ")" . 
                     ($session['is_current'] ? " [CURRENT]" : "") . "\n";
            }
        }
    });
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "SESSION MANAGEMENT TESTS COMPLETED\n";
echo str_repeat('=', 60) . "\n";
echo "\nNOTE: Make sure you have a test user with email '$testEmail' and password '$testPassword'\n";
echo "You can create one using: php artisan tinker\n";
echo "Then run: User::create(['name' => 'Test User', 'email' => '$testEmail', 'password' => md5('$testPassword'), 'is_active' => true]);\n";
?>