# Backend Implementation Guide for Session Management

## Overview
This guide explains how to implement single-device login with session conflict handling on your Laravel backend.

## Database Changes

### 1. Add Session Tracking to Users Table
```sql
ALTER TABLE users ADD COLUMN current_session_id VARCHAR(255) NULL;
ALTER TABLE users ADD COLUMN last_login_device VARCHAR(255) NULL;
ALTER TABLE users ADD COLUMN last_login_ip VARCHAR(45) NULL;
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL;
```

### 2. Create Sessions Tracking Table (Optional)
```sql
CREATE TABLE user_sessions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    device_info TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    <PERSON>OREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_session (user_id, session_id),
    INDEX idx_active_sessions (user_id, is_active)
);
```

## Backend API Changes

### 1. Update Login Endpoint (`/api/user/login`)

```php
// In your UserController or AuthController
public function login(Request $request)
{
    $credentials = $request->validate([
        'email' => 'required|email',
        'password' => 'required',
        'force_login' => 'boolean' // New field for force login
    ]);

    if (!Auth::attempt(['email' => $credentials['email'], 'password' => $credentials['password']])) {
        return response()->json(['error' => 'Invalid credentials'], 401);
    }

    $user = Auth::user();
    
    // Check if user already has an active session
    if ($user->current_session_id && !$request->input('force_login', false)) {
        // Check if the current session is still valid
        if ($this->isSessionActive($user->current_session_id)) {
            return response()->json([
                'error' => 'Session Conflict',
                'message' => 'You are already logged in on another device. Do you want to log out from that device and continue here?',
                'code' => 'SESSION_CONFLICT'
            ], 409); // 409 Conflict status
        }
    }

    // Generate new token and session
    $token = $user->createToken('auth-token')->plainTextToken;
    $sessionId = Str::uuid();
    
    // Update user session info
    $user->update([
        'current_session_id' => $sessionId,
        'last_login_device' => $this->getDeviceInfo($request),
        'last_login_ip' => $request->ip(),
        'last_login_at' => now()
    ]);

    // Optional: Store in sessions table
    UserSession::create([
        'user_id' => $user->id,
        'session_id' => $sessionId,
        'device_info' => $this->getDeviceInfo($request),
        'ip_address' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'expires_at' => now()->addDays(30) // Adjust as needed
    ]);

    // If force login, invalidate previous sessions
    if ($request->input('force_login', false)) {
        $this->invalidatePreviousSessions($user->id, $sessionId);
    }

    return response()->json([
        'access_token' => $token,
        'user' => $user,
        'session_id' => $sessionId
    ]);
}

private function isSessionActive($sessionId)
{
    // Check if session exists and is not expired
    return UserSession::where('session_id', $sessionId)
        ->where('is_active', true)
        ->where('expires_at', '>', now())
        ->exists();
}

private function invalidatePreviousSessions($userId, $currentSessionId)
{
    // Mark all other sessions as inactive
    UserSession::where('user_id', $userId)
        ->where('session_id', '!=', $currentSessionId)
        ->update(['is_active' => false]);
}

private function getDeviceInfo($request)
{
    $userAgent = $request->userAgent();
    // Simple device detection - you can use a library like jenssegers/agent for better detection
    if (strpos($userAgent, 'Mobile') !== false) {
        return 'Mobile Device';
    } elseif (strpos($userAgent, 'Tablet') !== false) {
        return 'Tablet';
    } else {
        return 'Desktop';
    }
}
```

### 2. Update Token Validation Middleware

```php
// In your API middleware or token validation
public function handle($request, Closure $next)
{
    $token = $request->bearerToken();
    
    if (!$token) {
        return response()->json(['error' => 'Token required'], 401);
    }

    $user = Auth::guard('sanctum')->user();
    
    if (!$user) {
        return response()->json(['error' => 'Invalid token'], 401);
    }

    // Check if current session is still active
    if ($user->current_session_id) {
        $activeSession = UserSession::where('user_id', $user->id)
            ->where('session_id', $user->current_session_id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();

        if (!$activeSession) {
            // Session was invalidated (user logged in elsewhere)
            return response()->json([
                'error' => 'Session Expired',
                'message' => 'Your session has expired because you logged in elsewhere.',
                'code' => 'SESSION_EXPIRED_NEW_LOGIN'
            ], 401);
        }
    }

    return $next($request);
}
```

### 3. Add Logout Endpoint

```php
public function logout(Request $request)
{
    $user = Auth::user();
    
    // Mark current session as inactive
    if ($user->current_session_id) {
        UserSession::where('user_id', $user->id)
            ->where('session_id', $user->current_session_id)
            ->update(['is_active' => false]);
    }

    // Clear session info from user
    $user->update([
        'current_session_id' => null
    ]);

    // Revoke current token
    $request->user()->currentAccessToken()->delete();

    return response()->json(['message' => 'Logged out successfully']);
}
```

## Frontend Integration

The frontend is already set up to handle:
- 409 status code with `SESSION_CONFLICT` code → Shows confirmation dialog
- 401 status code with `SESSION_EXPIRED_NEW_LOGIN` code → Shows session expired message

## Testing the Implementation

1. Login with user A on device/browser 1
2. Try to login with same user A on device/browser 2
3. Should see the session conflict dialog
4. Click "Yes, Log Out Other Device" 
5. Should successfully login on device 2 and logout device 1

## Security Considerations

1. **Session Expiration**: Set appropriate token expiration times
2. **Rate Limiting**: Implement rate limiting on login attempts
3. **Device Fingerprinting**: Consider more sophisticated device detection
4. **Audit Logging**: Log all session activities for security monitoring
5. **Cleanup**: Regularly clean up expired sessions from the database

## Optional Enhancements

1. **Multiple Device Support**: Allow X number of concurrent sessions
2. **Device Management**: Let users see and manage their active sessions
3. **Push Notifications**: Notify users when someone logs into their account
4. **Suspicious Activity Detection**: Flag logins from unusual locations/devices