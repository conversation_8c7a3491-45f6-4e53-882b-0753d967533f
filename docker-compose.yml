version: '3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: speech-to-text-app
    restart: unless-stopped
    volumes:
      - ./:/var/www/html
      - ./storage:/var/www/html/storage
    ports:
      - "9000:9000"
    environment:
      - APP_ENV=local
      - APP_KEY=base64:xg+WLDYQSyuYCvOW75iNRRcXaM2dFKCRHH7+Kc1NZMQ=
      - APP_DEBUG=true
      - APP_URL=http://localhost
      - DB_CONNECTION=mysql
      - DB_HOST=host.docker.internal
      - DB_PORT=3306
      - DB_DATABASE=speech
      - DB_USERNAME=root
      - DB_PASSWORD=NVsebJoPzPJNW7
      - REDIS_HOST=host.docker.internal
      - REDIS_PORT=6379
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - speech-network

networks:
  speech-network:
    driver: bridge
