# Speech-to-Text API with Apache Reverse Proxy

This document explains how to set up the Speech-to-Text API Laravel application in a Docker container with your local Apache server as a reverse proxy.

## Project Structure

- Laravel application containerized with Dock<PERSON> using PHP-FPM 8.2
- PHP-FPM running on port 9000
- Local MySQL database (already installed on your system)
- Local Apache as a reverse proxy

## Requirements

- <PERSON>er and Docker Compose
- PHP 8.2+ (for local development)
- Apache with mod_proxy, mod_proxy_fcgi, mod_rewrite enabled
- MySQL (already installed on your system)

## Setup Instructions

### 1. Build and Start the Laravel Docker Container

```bash
# Build and start the container
docker-compose up -d
```

### 2. Configure Apache as Reverse Proxy

1. Copy the Apache configuration file to your Apache configuration directory:

```bash
# For macOS (using Homebrew)
sudo cp apache-reverse-proxy.conf /usr/local/etc/httpd/extra/

# For Ubuntu/Debian
sudo cp apache-reverse-proxy.conf /etc/apache2/sites-available/speech-to-text.conf

# For CentOS/RHEL
sudo cp apache-reverse-proxy.conf /etc/httpd/conf.d/speech-to-text.conf
```

2. Enable the site (Ubuntu/Debian only):

```bash
sudo a2ensite speech-to-text.conf
```

3. Enable required Apache modules:

```bash
# For Ubuntu/Debian
sudo a2enmod proxy proxy_fcgi proxy_http headers rewrite
sudo systemctl restart apache2

# For macOS (using Homebrew)
sudo brew services restart httpd
```

### 3. Update Your Hosts File (for local development)

Add the following line to your `/etc/hosts` file:

```
127.0.0.1 speech-to-text.example.com
```

### 4. Access Your Application

Open your browser and navigate to:

```
http://speech-to-text.example.com
```

## Configuration Notes

### Docker Container

The Docker container runs PHP-FPM 8.2 on port 9000 and is configured to connect to your local MySQL database using `host.docker.internal` which resolves to your host machine's IP address from within the container.

When the container starts, it automatically:
1. Updates Composer dependencies to ensure compatibility with PHP 8.2
2. Generates an application key if not already set
3. Runs database migrations

### Apache Configuration

The Apache configuration file (`apache-reverse-proxy.conf`) is set up to:
- Serve static files directly from the Laravel public directory
- Proxy PHP requests to the PHP-FPM container running on port 9000
- Handle Laravel's front controller pattern with proper rewrite rules

### Troubleshooting

If you encounter issues with the connection between Apache and the Docker container:

1. Ensure the Docker container is running:
   ```bash
   docker-compose ps
   ```

2. Check if PHP-FPM is listening on port 9000:
   ```bash
   docker-compose exec app netstat -tuln
   ```

3. Verify Apache logs for any errors:
   ```bash
   # For macOS (using Homebrew)
   tail -f /usr/local/var/log/httpd/speech-to-text-error.log

   # For Ubuntu/Debian
   sudo tail -f /var/log/apache2/speech-to-text-error.log
   ```

4. Make sure the Apache modules are enabled:
   ```bash
   # For Ubuntu/Debian
   apache2ctl -M | grep proxy
   ```

5. Check Docker container logs:
   ```bash
   docker-compose logs app
   ```
