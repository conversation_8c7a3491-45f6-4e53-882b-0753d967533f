<VirtualHost *:80>
    ServerName speech-to-text.example.com
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/html/public

    # Enable required modules
    <IfModule mod_proxy.c>
        ProxyPreserveHost On

        # PHP-FPM handler for PHP files
        <FilesMatch "\.php$">
            <PERSON><PERSON>and<PERSON> "proxy:fcgi://127.0.0.1:9000"
        </FilesMatch>

        # Set headers for proxying
        RequestHeader set X-Forwarded-Proto "http"
        RequestHeader set X-Forwarded-Port "80"
    </IfModule>

    # Directory settings
    <Directory /var/www/html/public>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted

        # Handle the rewrite rules for <PERSON><PERSON>'s front controller
        <IfModule mod_rewrite.c>
            RewriteEngine On
            RewriteBase /
            RewriteRule ^index\.php$ - [L]
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteCond %{REQUEST_FILENAME} !-d
            RewriteRule . /index.php [L]
        </IfModule>
    </Directory>

    # Logs
    ErrorLog ${APACHE_LOG_DIR}/speech-to-text-error.log
    CustomLog ${APACHE_LOG_DIR}/speech-to-text-access.log combined
</VirtualHost>
