# React Frontend Integration for Quill Editor Content

This document outlines the steps to integrate the Quill editor for creating and managing content, and interacting with the Laravel backend API.

## 1. Prerequisites

*   A React project set up and running.
*   Familiarity with React components, state management, and API calls.
*   Node.js and npm/yarn installed.

## 2. Installation

Install `react-quill` and `axios` (or your preferred HTTP client).

```bash
npm install react-quill axios
# OR
yarn add react-quill axios
```

## 3. Creating a Quill Editor Component (e.g., `ContentEditor.js`)

Create a component to handle the Quill editor and form submission.

```jsx
// src/components/ContentEditor.js
import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles
import axios from 'axios';

const ContentEditor = () => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [htmlContent, setHtmlContent] = useState('');
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setMessage('');
        setError('');

        try {
            // Assuming your API token is stored in localStorage or a similar place
            const token = localStorage.getItem('jwt_token'); // Adjust as per your auth setup

            const response = await axios.post(
                '/api/user/content-history', // Your API endpoint
                { title, description, html_content: htmlContent },
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                }
            );
            setMessage('Content saved successfully!');
            setTitle('');
            setDescription('');
            setHtmlContent('');
            console.log('Saved content:', response.data);
        } catch (err) {
            setError('Failed to save content. Please try again.');
            console.error('Error saving content:', err.response ? err.response.data : err.message);
        }
    };

    const modules = {
        toolbar: [
            [{ 'header': [1, 2, false] }],
            ['bold', 'italic', 'underline', 'strike', 'blockquote'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }],
            ['link', 'image'],
            ['clean']
        ],
    };

    const formats = [
        'header',
        'bold', 'italic', 'underline', 'strike', 'blockquote',
        'list', 'bullet', 'indent',
        'link', 'image'
    ];

    return (
        <div>
            <h2>Create New Content</h2>
            {message && <p style={{ color: 'green' }}>{message}</p>}
            {error && <p style={{ color: 'red' }}>{error}</p>}
            <form onSubmit={handleSubmit}>
                <div>
                    <label htmlFor="title">Title:</label>
                    <input
                        type="text"
                        id="title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        required
                    />
                </div>
                <div>
                    <label htmlFor="description">Description (Optional):</label>
                    <textarea
                        id="description"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                    />
                </div>
                <div>
                    <label>HTML Content:</label>
                    <ReactQuill
                        theme="snow"
                        value={htmlContent}
                        onChange={setHtmlContent}
                        modules={modules}
                        formats={formats}
                        style={{ height: '300px', marginBottom: '50px' }} // Adjust height as needed
                    />
                </div>
                <button type="submit" style={{ marginTop: '20px' }}>Save Content</button>
            </form>
        </div>
    );
};

export default ContentEditor;
```

## 4. Displaying Stored HTML Content (e.g., `ContentDisplay.js`)

To display the HTML content safely, use `dangerouslySetInnerHTML`.

```jsx
// src/components/ContentDisplay.js
import React from 'react';

const ContentDisplay = ({ htmlString }) => {
    return (
        <div
            className="quill-content-display"
            dangerouslySetInnerHTML={{ __html: htmlString }}
        />
    );
};

export default ContentDisplay;
```
**Important Note on `dangerouslySetInnerHTML`**: Using `dangerouslySetInnerHTML` can expose your application to cross-site scripting (XSS) attacks if the HTML content comes from untrusted sources. Ensure that the HTML content is sanitized on the backend before storage, or use a library like `dompurify` on the frontend if you cannot guarantee clean input.

## 5. Fetching and Listing Content

You'll likely want to fetch and display a list of saved content.

```jsx
// src/pages/ContentListPage.js (Example)
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ContentDisplay from '../components/ContentDisplay'; // Assuming you created this

const ContentListPage = () => {
    const [contentList, setContentList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        const fetchContent = async () => {
            try {
                const token = localStorage.getItem('jwt_token'); // Adjust as per your auth setup
                const response = await axios.get('/api/user/content-history', {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });
                setContentList(response.data.data); // Assuming Laravel API Resource returns 'data'
                setLoading(false);
            } catch (err) {
                setError('Failed to fetch content.');
                setLoading(false);
                console.error('Error fetching content:', err.response ? err.response.data : err.message);
            }
        };
        fetchContent();
    }, []);

    if (loading) return <p>Loading content...</p>;
    if (error) return <p style={{ color: 'red' }}>{error}</p>;

    return (
        <div>
            <h2>My Saved Content</h2>
            {contentList.length === 0 ? (
                <p>No content saved yet. Start creating!</p>
            ) : (
                contentList.map((content) => (
                    <div key={content.id} style={{ border: '1px solid #ccc', padding: '10px', margin: '10px 0' }}>
                        <h3>{content.title}</h3>
                        {content.description && <p><strong>Description:</strong> {content.description}</p>}
                        <h4>Content:</h4>
                        <ContentDisplay htmlString={content.html_content} />
                        <p><small>Last updated: {new Date(content.updated_at).toLocaleString()}</small></p>
                        {/* Add Edit/Delete buttons and logic here */}
                    </div>
                ))
            )}
        </div>
    );
};

export default ContentListPage;
```

## 6. Integrating into Your App

Import and use these components in your main `App.js` or routing setup.

```jsx
// src/App.js (Example)
import React from 'react';
import ContentEditor from './components/ContentEditor';
import ContentListPage from './pages/ContentListPage';
// import { BrowserRouter as Router, Route, Routes } from 'react-router-dom'; // If using React Router

function App() {
    return (
        <div className="App">
            <h1>My Application</h1>
            {/* Example usage */}
            <ContentEditor />
            <hr />
            <ContentListPage />
            {/* If using React Router: */}
            {/* <Router>
                <Routes>
                    <Route path="/create" element={<ContentEditor />} />
                    <Route path="/list" element={<ContentListPage />} />
                    // Add routes for editing/viewing single content items
                </Routes>
            </Router> */}
        </div>
    );
}

export default App;
```

## 7. API Endpoints Summary

The Laravel backend exposes the following endpoints (assuming `/api/user/content-history` base path):

*   **POST** `/api/user/content-history`
    *   **Purpose**: Create new content.
    *   **Body**: `{ title: string, description?: string, html_content: string }`
    *   **Authentication**: Required (JWT Token in `Authorization: Bearer` header).
*   **GET** `/api/user/content-history`
    *   **Purpose**: Retrieve all content for the authenticated user.
    *   **Authentication**: Required.
*   **GET** `/api/user/content-history/{id}`
    *   **Purpose**: Retrieve a specific content item by ID.
    *   **Authentication**: Required.
*   **PUT/PATCH** `/api/user/content-history/{id}`
    *   **Purpose**: Update an existing content item.
    *   **Body**: `{ title?: string, description?: string, html_content?: string }` (all fields optional for update)
    *   **Authentication**: Required.
*   **DELETE** `/api/user/content-history/{id}`
    *   **Purpose**: Delete a specific content item.
    *   **Authentication**: Required.

Remember to handle authentication (JWT token storage and retrieval) appropriately in your React application.
