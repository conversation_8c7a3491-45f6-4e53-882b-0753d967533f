# Session Management Implementation

This document explains the enhanced session management system that implements single-device login with session conflict handling.

## Features Implemented

### 1. Database Changes
- **Users Table**: Added session tracking fields
  - `current_session_id`: UUID of the current active session
  - `last_login_device`: Device type (Mobile/Tablet/Desktop)
  - `last_login_ip`: IP address of last login
  - `last_login_at`: Timestamp of last login

- **UserSessions Table**: New table for comprehensive session tracking
  - Tracks all user sessions with device info, IP, user agent
  - Supports session expiration and active/inactive status
  - Indexed for performance

### 2. Enhanced Login Flow
- **Session Conflict Detection**: Checks if user already has an active session
- **Force Login Option**: Allows users to terminate other sessions and continue
- **Device Detection**: Simple device type detection from user agent
- **Session Expiration**: Configurable session expiration (default: 30 days)

### 3. API Endpoints

#### Authentication Endpoints
- `POST /api/user/login` - Enhanced with session conflict handling
  - New field: `force_login` (boolean, optional)
  - Returns 409 status with `SESSION_CONFLICT` code when session exists
  - Returns session_id in successful login response

- `POST /api/user/logout` - Enhanced with session cleanup
  - Marks current session as inactive
  - Clears session ID from user record

#### Session Management Endpoints
- `GET /api/user/sessions` - List all active sessions
- `DELETE /api/user/sessions/{sessionId}` - Terminate specific session
- `DELETE /api/user/sessions/others/terminate` - Terminate all other sessions

### 4. Middleware Enhancement
- **CheckSingleDeviceSession**: Enhanced to use new session table
- **Backward Compatibility**: Still supports original JWT timestamp approach
- **Better Error Messages**: Clear session conflict messages

### 5. Models
- **User Model**: Added session relationships and fillable fields
- **UserSession Model**: New model with validation methods

## Testing the Implementation

### 1. Basic Login Flow
```bash
# First login - should succeed
curl -X POST http://localhost:8000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

### 2. Session Conflict Test
```bash
# Second login from different device - should return 409 conflict
curl -X POST http://localhost:8000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'

# Expected Response:
# {
#   "error": "Session Conflict",
#   "message": "You are already logged in on another device. Do you want to log out from that device and continue here?",
#   "code": "SESSION_CONFLICT"
# }
```

### 3. Force Login Test
```bash
# Force login - should succeed and invalidate previous session
curl -X POST http://localhost:8000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password",
    "force_login": true
  }'
```

### 4. Session Management
```bash
# List active sessions
curl -X GET http://localhost:8000/api/user/sessions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Terminate specific session
curl -X DELETE http://localhost:8000/api/user/sessions/SESSION_UUID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Terminate all other sessions
curl -X DELETE http://localhost:8000/api/user/sessions/others/terminate \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Frontend Integration

The frontend should handle these response codes:

### Session Conflict (409)
```javascript
if (response.status === 409 && response.data.code === 'SESSION_CONFLICT') {
  // Show confirmation dialog
  const forceLogin = confirm(response.data.message);
  if (forceLogin) {
    // Retry login with force_login: true
    login({ ...credentials, force_login: true });
  }
}
```

### Session Expired (401)
```javascript
if (response.status === 401 && response.data.code === 'SESSION_EXPIRED_NEW_LOGIN') {
  // Show session expired message
  alert(response.data.message);
  // Redirect to login
  redirectToLogin();
}
```

## Maintenance Commands

### Clean Up Expired Sessions
```bash
# Clean up sessions older than 30 days (default)
php artisan sessions:cleanup

# Clean up sessions older than 7 days
php artisan sessions:cleanup --days=7
```

### Schedule Regular Cleanup
Add to `app/Console/Kernel.php`:
```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('sessions:cleanup')->daily();
}
```

## Security Considerations

1. **Session Expiration**: Sessions expire after 30 days by default
2. **IP Tracking**: Login IP addresses are tracked for security auditing
3. **Device Detection**: Basic device type detection for user awareness
4. **Session Invalidation**: Previous sessions are properly invalidated
5. **Audit Trail**: All session activities are logged in the database

## Configuration Options

### Session Expiration
Modify in `AuthController::login()`:
```php
'expires_at' => now()->addDays(30) // Change to desired duration
```

### Device Detection
Enhance device detection by installing `jenssegers/agent`:
```bash
composer require jenssegers/agent
```

Then update `getDeviceInfo()` method in `AuthController`.

## Database Indexes

The implementation includes optimized indexes:
- `user_sessions(user_id, session_id)` - For session lookups
- `user_sessions(user_id, is_active)` - For active session queries

## Backward Compatibility

The system maintains backward compatibility with the existing JWT timestamp approach while adding the new session management features. Existing tokens will continue to work until they expire naturally.