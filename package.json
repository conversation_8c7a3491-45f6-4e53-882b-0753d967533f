{"name": "speech-to-text", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-free": "^6.6.0", "@google/generative-ai": "^0.24.1", "@mui/material": "^5.16.6", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-mui": "^5.0.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "date-fns": "^4.1.0", "localforage": "^1.10.0", "md5": "^2.3.0", "react": "^18.3.1", "react-bootstrap": "^2.10.4", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-router-dom": "^7.5.0", "react-scripts": "5.0.1", "react-speech-recognition": "^4.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}