<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class, // Usually not needed unless specifically configured
        \App\Http\Middleware\TrustProxies::class,    // Good for load balancers/proxies
        \Illuminate\Http\Middleware\HandleCors::class, // IMPORTANT for APIs accessed from different origins (React frontend)
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            // Standard web middleware (not typically used for stateless API)
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            // Base middleware for ALL API routes defined in routes/api.php
            // Authentication should be applied specifically within your route groups
            // using middleware like 'auth:api' or 'auth:admin_api'.
            \Illuminate\Routing\Middleware\ThrottleRequests::class.':api', // API rate limiting
            \Illuminate\Routing\Middleware\SubstituteBindings::class, // For route model binding

            // DO NOT put middleware requiring authentication (like CheckUserStatus) here.
            // It runs too early. Apply them within routes/api.php AFTER 'auth:guard'.
            // \App\Http/Middleware\CheckUserStatus::class, // <- REMOVE FROM HERE
        ],
    ];

    /**
     * The application's middleware aliases.
     * Aliases may be used instead of class names to conveniently assign middleware.
     * These look correct.
     *
     * @var array<string, class-string|string>
     */
    protected $middlewareAliases = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'precognitive' => \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,

        // Your Custom Aliases (These are defined correctly)
        'auth.single_device' => \App\Http\Middleware\CheckSingleDeviceSession::class,
        'auth.user.status' => \App\Http\Middleware\CheckUserStatus::class,

         // Add alias for tymon/jwt-auth if you use it directly (optional)
         // 'jwt.auth' => \Tymon\JWTAuth\Http\Middleware\Authenticate::class,
         // 'jwt.refresh' => \Tymon\JWTAuth\Http\Middleware\RefreshToken::class,
    ];
}
