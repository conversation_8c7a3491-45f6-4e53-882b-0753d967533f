<?php
namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        // Adjust field name if needed (e.g., 'username' for admin)
        $identityField = $this->routeIs('api.admin.login') ? 'username' : 'email';
        $identityRule = $this->routeIs('api.admin.login') ? 'string' : 'email';

        return [
            $identityField => ['required', $identityRule],
            'password' => ['required', 'string'],
            'force_login' => ['sometimes', 'boolean'], // New field for force login
        ];
    }

     /**
     * Get the validation attributes that apply to the request.
     * Needed if identity field changes based on route
     * @return array
     */
    public function attributes()
    {
        return [
            'username' => 'username',
            'email' => 'email',
        ];
    }
}
