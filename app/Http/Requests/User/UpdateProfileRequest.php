<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule; // Import Rule facade
use Illuminate\Support\Facades\Auth; // To get current user ID

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * Authorization is handled by middleware (auth:api), this can be true.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Get the authenticated user's ID (ensure you are using the 'api' guard)
        $userId = Auth::guard('api')->id();

        return [
            // 'sometimes' means validate only if present in the request
            'name' => ['sometimes', 'required', 'string', 'max:255'], // Still required if present
            'email' => [
                'sometimes',
                'required',
                'string',
                'email',
                'max:255',
                // Ensure email is unique, ignoring the current user's record
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'state' => ['sometimes', 'required', 'string', 'max:255'],
            'city' => ['sometimes', 'required', 'string', 'max:255'],
            'phone' => ['sometimes', 'required', 'string', 'max:255'],
            // Password update is optional
            'password' => ['sometimes', 'nullable', 'string', Password::defaults(), 'confirmed'],
        ];
    }
}
