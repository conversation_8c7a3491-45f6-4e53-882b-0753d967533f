<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Assuming route middleware handles admin check
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Get the plan ID from the route, e.g., /api/admin/plans/{plan}
        $planId = $this->route('plan')?->id;

        return [
            // Use 'sometimes' for optional fields on update
            'plan_name' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                // Optional: Add unique check ignoring current plan
                // Rule::unique('plans', 'plan_name')->ignore($planId),
            ],
            'months' => ['sometimes', 'required', 'string', 'max:255'], // Add 'numeric' if needed
        ];
    }
}
