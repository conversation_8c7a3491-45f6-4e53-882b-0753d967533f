<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Assuming route middleware handles admin check
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan_name' => ['required', 'string', 'max:255'], // Maybe add unique:plans,plan_name if needed
            // Months is varchar in schema, treat as string. Add 'numeric' if it must be a number.
            'months' => ['required', 'string', 'max:255'],
        ];
    }
}
