<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule; // Import Rule

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * Authorization handled by middleware (auth:admin_api), can be true.
     */
    public function authorize(): bool
    {
        return true; // Assuming route middleware handles admin check
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            // Ensure email is unique in the 'users' table when creating
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'state' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:255'],
            'password' => ['required', 'string', 'size:32'],
            // Plan is optional, but if provided, must exist in 'plans' table
            'plan_id' => ['nullable', 'integer', Rule::exists('plans', 'id')],
            // Dates are optional, but must be valid dates if provided
            'purchase_date' => ['nullable', 'date_format:Y-m-d'],
            // Expiry date is nullable string, max length from schema
            'expiry_date' => ['nullable', 'string', 'max:255'],
            // Executive status is optional string
            'executive' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Prepare the data for validation.
     * Convert empty strings for nullable fields to actual null.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'plan_id' => $this->plan_id === '' ? null : $this->plan_id,
            'purchase_date' => $this->purchase_date === '' ? null : $this->purchase_date,
            'expiry_date' => $this->expiry_date === '' ? null : $this->expiry_date,
            'executive' => $this->has('executive') && $this->executive === '' ? null : $this->executive,
        ]);
    }
}
