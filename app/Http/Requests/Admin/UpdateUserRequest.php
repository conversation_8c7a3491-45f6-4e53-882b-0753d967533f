<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule; // Import Rule

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * Authorization typically handled by route middleware for admins.
     */
    public function authorize(): bool
    {
        return true; // Assuming middleware handles admin check
    }

    /**
     * Get the validation rules that apply to the request for updating a user.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Get the user ID from the route parameter (e.g., /api/admin/users/{user})
        $userId = $this->route('user')?->id;

        // If $userId is somehow null, you might want to abort or handle differently
        if (!$userId) {
             // Optionally throw an exception or return specific validation
             // For simplicity, we proceed assuming it's available via route model binding
        }

        return [
            // Use 'sometimes' for fields that are optional during update
            // 'required' within 'sometimes' means it must have a value *if* the field is present at all
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'email' => [
                'sometimes',
                'required',
                'string',
                'email',
                'max:255',
                // Ensure email is unique, ignoring the user being updated
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'state' => ['sometimes', 'required', 'string', 'max:255'], // Assuming state name is sent
            'city' => ['sometimes', 'required', 'string', 'max:255'],
            'phone' => ['sometimes', 'required', 'string', 'max:255'], // Add specific phone validation if needed

            // Password Validation on Update:
            // - 'sometimes': Only validate if 'password' field is present in the request.
            // - 'nullable': Allow the field to be explicitly null or empty string (we handle empty string later).
            // - 'string': Must be a string.
            // - 'size:32': Must be exactly 32 characters (for the incoming MD5 hash).
            'password' => ['sometimes', 'nullable', 'string', 'size:32'],
            // --- NO 'confirmed' rule here ---

            // Optional fields - validate if present
            'plan_id' => ['sometimes', 'nullable', 'integer', Rule::exists('plans', 'id')], // Must exist in plans table if not null
            'purchase_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'expiry_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'is_active' => ['sometimes', 'boolean'], // Allow updating status via this form if needed
            'executive' => ['sometimes', 'nullable', 'string', 'max:255'], // Executive as string
        ];
    }

    /**
     * Prepare the data for validation.
     * Convert empty strings for nullable fields to actual null before validation runs.
     */
    protected function prepareForValidation(): void
    {
        $mergeData = [];

        // Convert empty optional fields to null so 'nullable' rule works correctly
        if ($this->has('plan_id') && $this->plan_id === '') { $mergeData['plan_id'] = null; }
        if ($this->has('purchase_date') && $this->purchase_date === '') { $mergeData['purchase_date'] = null; }
        if ($this->has('expiry_date') && $this->expiry_date === '') { $mergeData['expiry_date'] = null; }
        if ($this->has('executive') && $this->executive === '') { $mergeData['executive'] = null; }

        // If password field exists but is empty/null, ensure it passes 'nullable' validation
        // We will remove it later in the controller if it's empty/null, so it doesn't overwrite existing hash
         if ($this->has('password') && ($this->password === '' || is_null($this->password))) {
            $mergeData['password'] = null; // Set explicitly to null for nullable validation
        }

        if (!empty($mergeData)) {
             $this->merge($mergeData);
        }
    }
}
