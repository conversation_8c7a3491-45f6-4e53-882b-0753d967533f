<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreUserRequest; // Request for creating
use App\Http\Requests\Admin\UpdateUserRequest; // Request for updating
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response; // For status codes

class UserController extends Controller
{
    protected string $guard = 'admin_api'; // Actions require admin auth

    public function __construct()
    {
         $this->middleware('auth:' . $this->guard); // Apply admin auth middleware
         // Middleware for status check ('auth.user.status') is applied in routes/Kernel if needed globally
    }

    /**
     * Display a listing of the resource (Users).
     * Includes filtering by search term and plan_id.
     */
    public function index(Request $request)
    {
        \Illuminate\Support\Facades\Log::debug("User index request params: " . json_encode($request->all()));
        $query = User::with('plan'); // Start query, eager load plan

        // Apply search term filter (name or email)
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Apply executive search filter
        if ($executiveSearch = $request->input('executive_search')) {
            $query->where('executive', 'LIKE', "%{$executiveSearch}%");
        }

        // Apply plan filter
        if ($planId = $request->input('plan_id')) {
            // Handle 'no plan' filter if needed, e.g., plan_id=0 or plan_id=none
            if ($planId === '0' || $planId === 'none') {
                $query->whereNull('plan_id');
            } else {
                 $query->where('plan_id', $planId);
            }
        }

        // Apply state filter
        if ($state = $request->input('state')) {
            \Illuminate\Support\Facades\Log::debug("Filtering by state: {$state}");
            $query->where('state', $state);
        }

        // Apply status filter
        if ($request->has('is_active') && !is_null($request->input('is_active'))) {
            $query->where('is_active', (bool)$request->input('is_active'));
        }

        // Paginate results
        $users = $query->orderBy('id', 'desc')->paginate($request->input('per_page', 15));

        // Return paginated resource collection
        return UserResource::collection($users);
    }

    /**
     * Store a newly created resource in storage (Create User).
     * Assumes password received is MD5 hash (if model hashing disabled)
     * or performs MD5 hashing here if model hashing enabled (adjust based on model).
     */
    public function store(StoreUserRequest $request) // Use StoreUserRequest validation
    {
        $validatedData = $request->validated();

        // Ensure Model does NOT hash automatically if MD5 hash is received
        // If Model *does* hash automatically (Bcrypt), HASH HERE (MD5) INSTEAD:
        // if (!empty($validatedData['password'])) {
        //    $validatedData['password'] = md5($validatedData['password']);
        // }

        // Create user with validated data
        $user = User::create($validatedData);

        // Return created user resource
        return UserResource::make($user->load('plan'))
                         ->response()
                         ->setStatusCode(Response::HTTP_CREATED); // 201
    }

    /**
     * Display the specified resource (Single User).
     */
    public function show(User $user) // Uses route model binding
    {
        // Eager load plan and return resource
        return UserResource::make($user->load('plan'));
    }

    /**
     * Update the specified resource in storage (Update User).
     * Uses UpdateUserRequest validation.
     * Assumes User model does NOT automatically hash passwords.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
         $validated = $request->validated(); // Get validated data

         // IMPORTANT: Only update the password field if it was actually provided
         // in the request and passed validation (i.e., present in $validated).
         // The validated data ($validated['password']) already contains the MD5 hash
         // sent from the frontend IF a new password was submitted.
         // If no password was submitted, $validated['password'] will not exist or be null.

         // If 'password' key is not set in validated data, remove it to avoid overwriting
         // the existing password with null during mass update.
         if (!isset($validated['password']) || is_null($validated['password'])) {
            unset($validated['password']);
         }
         // If the key *is* set, it contains the validated MD5 hash, ready for update.

         // Perform the update using mass assignment
         $user->update($validated);

         // Return the updated user resource
         return UserResource::make($user->fresh()->load('plan'));
    }

    /**
     * Update the activation status of the specified user.
     */
    public function updateStatus(Request $request, User $user)
    {
        // Validate the incoming status
        $validated = $request->validate([
            'is_active' => 'required|boolean',
        ]);

        // Update the user's status
        $user->update(['is_active' => $validated['is_active']]);

        // Return the updated user resource
        return UserResource::make($user->fresh()->load('plan'));
    }


    /**
     * Remove the specified resource from storage (Delete User).
     */
    public function destroy(User $user)
    {
        // Add checks here if needed (e.g., prevent deleting super-admin)
        // if ($user->isSuperAdmin()) { return response()->json([...], 403); }

        $user->delete();

        // Foreign key constraint 'ON DELETE SET NULL' for plan_id handles detachment

        return response()->noContent(); // HTTP 204 No Content
    }
}
