<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StorePlanRequest;
use App\Http\Requests\Admin\UpdatePlanRequest;
use App\Http\Resources\PlanResource;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PlanController extends Controller
{
    protected string $guard = 'admin_api'; // Actions require admin auth

    public function __construct()
    {
         $this->middleware('auth:' . $this->guard);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
{
    $perPage = $request->input('per_page');

    if ($perPage === '-1' || strtolower($perPage) === 'all') {
        // Fetch all plans without pagination
        $plans = Plan::get();
        return response()->json([
            'data' => PlanResource::collection($plans),
            'links' => null, // Indicate no pagination links
            'meta' => null   // Indicate no pagination meta
        ]);

    } else {
        $plans = Plan::paginate($perPage ?? 15); // Use default 15 if not provided
        return PlanResource::collection($plans); // Returns paginated resource collection
    }
}

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePlanRequest $request)
    {
        $plan = Plan::create($request->validated());
        return PlanResource::make($plan)->response()->setStatusCode(Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(Plan $plan)
    {
        return PlanResource::make($plan);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePlanRequest $request, Plan $plan)
    {
        $plan->update($request->validated());
        return PlanResource::make($plan->fresh());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan)
    {
        // Consider implications: Users with this plan_id will have it set to NULL.
        // You might want to prevent deletion if users are assigned, or handle it differently.
        if ($plan->users()->exists()) {
             return response()->json(['error' => 'Cannot delete plan with assigned users.'], Response::HTTP_CONFLICT); // 409
        }

        $plan->delete();
        return response()->noContent(); // HTTP 204
    }
}
