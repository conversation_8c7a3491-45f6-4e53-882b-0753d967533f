<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Admin; // Use Admin model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon; // If implementing single device for admin

class AuthController extends Controller
{
    protected string $guard = 'admin_api'; // Specify the guard

    public function __construct()
    {
         // Apply auth middleware to methods needing authentication
        $this->middleware('auth:' . $this->guard)->except(['login']);
        // Apply single device check if implemented for admins
        // $this->middleware('auth.single_device:' . $this->guard)->only(['logout', 'me', 'refresh']);
    }

    public function login(LoginRequest $request)
    {
    $credentials = $request->only('username', 'password');
    $plainPassword = $credentials['password'];

    // 1. Find the admin user by username
    $admin = Admin::where('username', $credentials['username'])->first();

    // 2. Check if user exists AND if the manually computed MD5 matches stored hash
    if ($admin && hash_equals($admin->password, md5($plainPassword))) { // Use hash_equals for timing attack resistance
        // 3. Manually Log in the user and generate token
        $token = Auth::guard($this->guard)->login($admin); // Manually log in this user
        return $this->respondWithToken($token);
    }

    // 4. If check fails, return unauthorized
    return response()->json(['error' => 'Unauthorized'], 401);
    }

    public function me()
    {
        // You might want an AdminResource if you need specific formatting
        return response()->json(Auth::guard($this->guard)->user());
    }

    public function logout()
    {
        Auth::guard($this->guard)->logout();
        return response()->json(['message' => 'Successfully logged out']);
    }

    public function refresh()
    {
        // Add timestamp update logic here if implementing single device for admin
        return $this->respondWithToken(Auth::guard($this->guard)->refresh());
    }

    protected function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
             // Use the TTL configured for the admin provider/guard if different
            'expires_in' => Auth::guard($this->guard)->factory()->getTTL() * 60,
            'admin' => Auth::guard($this->guard)->user() // Or AdminResource
        ]);
    }
}
