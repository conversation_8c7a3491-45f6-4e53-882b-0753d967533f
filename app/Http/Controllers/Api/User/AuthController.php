<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterUserRequest;
use App\Http\Requests\User\UpdateProfileRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Response;
use Carbon\Carbon;

class AuthController extends Controller
{
    /**
     * The authentication guard for regular users.
     * @var string
     */
    protected string $guard = 'api';

    /**
     * Create a new AuthController instance.
     * Apply middleware for authentication and status checks.
     *
     * @return void
     */
    public function __construct()
    {
        // Authentication required for all methods except login and register
        $this->middleware('auth:' . $this->guard)->except(['login', 'register']);

        // Apply single device check middleware after authentication (only for specific methods)
        $this->middleware('auth.single_device:' . $this->guard)->only(['logout', 'me', 'updateProfile', 'refresh']);

        // Apply user status check middleware after authentication (only for specific methods)
        // This ensures active/non-expired status for these actions
        $this->middleware('auth.user.status')->only(['me', 'updateProfile', 'refresh']);
    }

    /**
     * Handle a registration request for a new user.
     * Hashes password using MD5 before saving.
     *
     * @param  \App\Http\Requests\Auth\RegisterUserRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(RegisterUserRequest $request)
    {
        $validatedData = $request->validated();

        // Hash password using MD5 before creating
        if (!empty($validatedData['password'])) {
             $validatedData['password'] = md5($validatedData['password']);
        } else {
             // This case should ideally be caught by validation, but as a fallback:
             return response()->json(['error' => 'Password is required during registration.'], Response::HTTP_UNPROCESSABLE_ENTITY); // 422
        }

        // Remove password_confirmation as it's not stored in the database
        unset($validatedData['password_confirmation']);

        // Set defaults for new user
        $validatedData['is_active'] = $validatedData['is_active'] ?? true;
        $validatedData['last_token_issued_at'] = null;

        // Create the user record
        $user = User::create($validatedData);

        // Return success response with user data
        return response()->json([
            'message' => 'User successfully registered',
            'user' => UserResource::make($user->loadMissing('plan')) // Load plan if possible
        ], Response::HTTP_CREATED); // 201
    }

    /**
     * Handle a login request for a user.
     * Manually compares MD5 password hash.
     * Checks user status and plan expiry.
     * Implements session conflict handling.
     *
     * @param  \App\Http\Requests\Auth\LoginRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginRequest $request)
    {
        $credentials = $request->validated();
        $plainPassword = $credentials['password'];
        $email = $credentials['email'];
        $forceLogin = $request->input('force_login', false);
        
        $user = User::where('email', $email)->first();

        if (!$user) { 
            return response()->json(['error' => 'Invalid credentials'], Response::HTTP_UNAUTHORIZED); 
        }

        // Check password (MD5)
        if (!($user->password && hash_equals($user->password, md5($plainPassword)))) {
            return response()->json(['error' => 'Invalid credentials'], Response::HTTP_UNAUTHORIZED);
        }

        // Check status/expiry
        if (!$user->is_active) { 
            return response()->json([
                'error' => 'Account inactive.', 
                'message' => 'Your account is currently inactive. Please contact support.', 
                'code' => 'ACCOUNT_INACTIVE'
            ], Response::HTTP_FORBIDDEN); 
        }
        
        if ($user->expiry_date) { 
            try { 
                $expiry = Carbon::parse($user->expiry_date)->endOfDay(); 
                if (Carbon::now()->isAfter($expiry)) { 
                    return response()->json([
                        'error' => 'Plan expired.', 
                        'message' => 'Your plan has expired. Please contact support to renew.', 
                        'code' => 'PLAN_EXPIRED'
                    ], Response::HTTP_FORBIDDEN); 
                } 
            } catch (\Exception $e) { 
                /* Optional: Handle error */ 
            } 
        }

        // Check if user already has an active session
        if ($user->current_session_id && !$forceLogin) {
            // Check if the current session is still valid
            if ($this->isSessionActive($user->current_session_id)) {
                return response()->json([
                    'error' => 'Session Conflict',
                    'message' => 'You are already logged in on another device. Do you want to log out from that device and continue here?',
                    'code' => 'SESSION_CONFLICT'
                ], 409); // 409 Conflict status
            }
        }

        // Generate new session ID
        $sessionId = \Illuminate\Support\Str::uuid();

        // Update user session info
        $user->update([
            'current_session_id' => $sessionId,
            'last_login_device' => $this->getDeviceInfo($request),
            'last_login_ip' => $request->ip(),
            'last_login_at' => now(),
            'last_token_issued_at' => now()
        ]);

        // Store in sessions table
        \App\Models\UserSession::create([
            'user_id' => $user->id,
            'session_id' => $sessionId,
            'device_info' => $this->getDeviceInfo($request),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'expires_at' => now()->addDays(30) // Adjust as needed
        ]);

        // If force login, invalidate previous sessions
        if ($forceLogin) {
            $this->invalidatePreviousSessions($user->id, $sessionId);
        }

        // Generate JWT token
        $token = Auth::guard($this->guard)->login($user);

        return $this->respondWithToken($token, $sessionId);
    }

    /**
     * Get the authenticated User's profile information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\UserResource
     */
    public function me(Request $request)
    {
        // Auth and status middleware already applied
        $user = Auth::guard($this->guard)->user()->loadMissing('plan'); // Eager load plan relationship
        return UserResource::make($user);
    }

     /**
      * Update the authenticated user's profile.
      * Hashes password using MD5 if provided.
      *
      * @param  \App\Http\Requests\User\UpdateProfileRequest  $request
      * @return \App\Http\Resources\UserResource
      */
     public function updateProfile(UpdateProfileRequest $request)
     {
        $user = Auth::guard($this->guard)->user(); // Get authenticated user
        $validated = $request->validated(); // Get validated update data

        // Handle password update separately if provided
        if (!empty($validated['password'])) {
            // Hash new password using MD5
            $user->password = md5($validated['password']);
            // Remove plain text password from validated array before mass update
            unset($validated['password']);
        }

        // Update other validated fields using mass assignment
        $user->update($validated);

        // Return the updated user resource, loading the plan relationship
        return UserResource::make($user->fresh()->loadMissing('plan'));
     }

    /**
     * Log the user out (Invalidate the token server-side if blacklist enabled).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        $user = Auth::guard($this->guard)->user();
        
        // Mark current session as inactive
        if ($user && $user->current_session_id) {
            \App\Models\UserSession::where('user_id', $user->id)
                ->where('session_id', $user->current_session_id)
                ->update(['is_active' => false]);
        }

        // Clear session info from user
        if ($user) {
            $user->update(['current_session_id' => null]);
        }

        Auth::guard($this->guard)->logout(); // Invalidate the token
        return response()->json(['message' => 'Successfully logged out']);
    }

    /**
     * Refresh the current authentication token.
     * Also updates the timestamp for single-device login.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        $user = Auth::guard($this->guard)->user();

        // **** SINGLE DEVICE LOGIN LOGIC ****
        // Update timestamp *before* refreshing the token
        if ($user) {
             $user->last_token_issued_at = now();
             $user->save();
        }
        // ***********************************

        $refreshedToken = Auth::guard($this->guard)->refresh();
        return $this->respondWithToken($refreshedToken);
    }

    /**
     * Format the JWT response.
     *
     * @param  string $token
     * @param  string|null $sessionId
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token, $sessionId = null)
    {
        // Get the authenticated user (should exist after login/refresh)
        $user = Auth::guard($this->guard)->user();

        $response = [
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => Auth::guard($this->guard)->factory()->getTTL() * 60, // Expiry in seconds
            // Include user details, loading plan if not already loaded
            'user' => $user ? UserResource::make($user->loadMissing('plan')) : null
        ];

        if ($sessionId) {
            $response['session_id'] = $sessionId;
        }

        return response()->json($response);
    }

    /**
     * Check if a session is still active and valid.
     *
     * @param string $sessionId
     * @return bool
     */
    private function isSessionActive($sessionId)
    {
        // Check if session exists and is not expired
        return \App\Models\UserSession::where('session_id', $sessionId)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->exists();
    }

    /**
     * Invalidate all previous sessions for a user except the current one.
     *
     * @param int $userId
     * @param string $currentSessionId
     * @return void
     */
    private function invalidatePreviousSessions($userId, $currentSessionId)
    {
        // Mark all other sessions as inactive
        \App\Models\UserSession::where('user_id', $userId)
            ->where('session_id', '!=', $currentSessionId)
            ->update(['is_active' => false]);
    }

    /**
     * Get device information from the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    private function getDeviceInfo($request)
    {
        $userAgent = $request->userAgent();
        
        // Simple device detection - you can use a library like jenssegers/agent for better detection
        if (strpos($userAgent, 'Mobile') !== false) {
            return 'Mobile Device';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            return 'Tablet';
        } else {
            return 'Desktop';
        }
    }
}
