<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\UserSession;

class SessionController extends Controller
{
    /**
     * Get all active sessions for the authenticated user.
     */
    public function index()
    {
        $user = Auth::guard('api')->user();
        
        $sessions = UserSession::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($session) use ($user) {
                return [
                    'id' => $session->id,
                    'session_id' => $session->session_id,
                    'device_info' => $session->device_info,
                    'ip_address' => $session->ip_address,
                    'created_at' => $session->created_at,
                    'expires_at' => $session->expires_at,
                    'is_current' => $session->session_id === $user->current_session_id
                ];
            });

        return response()->json([
            'sessions' => $sessions,
            'total' => $sessions->count()
        ]);
    }

    /**
     * Terminate a specific session.
     */
    public function destroy($sessionId)
    {
        $user = Auth::guard('api')->user();
        
        $session = UserSession::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->where('is_active', true)
            ->first();

        if (!$session) {
            return response()->json(['error' => 'Session not found'], 404);
        }

        // Mark session as inactive
        $session->update(['is_active' => false]);

        // If this is the current session, clear it from user
        if ($user->current_session_id === $sessionId) {
            $user->update(['current_session_id' => null]);
        }

        return response()->json(['message' => 'Session terminated successfully']);
    }

    /**
     * Terminate all other sessions except the current one.
     */
    public function destroyOthers()
    {
        $user = Auth::guard('api')->user();
        
        $terminatedCount = UserSession::where('user_id', $user->id)
            ->where('session_id', '!=', $user->current_session_id)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        return response()->json([
            'message' => 'All other sessions terminated successfully',
            'terminated_count' => $terminatedCount
        ]);
    }
}