<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\StoreContentHistoryRequest;
use App\Http\Requests\User\UpdateContentHistoryRequest;
use App\Http\Resources\ContentHistoryResource;
use App\Models\ContentHistory;
use Illuminate\Support\Facades\Auth;

class ContentHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $contentHistory = Auth::user()->contentHistory()->paginate(10);
        return ContentHistoryResource::collection($contentHistory);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContentHistoryRequest $request)
    {
        $contentHistory = Auth::user()->contentHistory()->create($request->validated());
        return new ContentHistoryResource($contentHistory);
    }

    /**
     * Display the specified resource.
     */
    public function show(ContentHistory $contentHistory)
    {
        $this->authorize('view', $contentHistory);
        return new ContentHistoryResource($contentHistory);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContentHistoryRequest $request, ContentHistory $contentHistory)
    {
        $this->authorize('update', $contentHistory);
        $contentHistory->update($request->validated());
        return new ContentHistoryResource($contentHistory);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContentHistory $contentHistory)
    {
        $this->authorize('delete', $contentHistory);
        $contentHistory->delete();
        return response()->noContent();
    }
}
