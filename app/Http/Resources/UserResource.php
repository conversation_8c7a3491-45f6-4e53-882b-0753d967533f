<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'state' => $this->state,
            'city' => $this->city,
            'phone' => $this->phone,
            'purchase_date' => $this->purchase_date?->toDateString(), // Format date
            'expiry_date' => $this->expiry_date, // Assuming it's stored as varchar/string
            'created_at' => $this->created_at?->toDateTimeString(),
            'updated_at' => $this->updated_at?->toDateTimeString(),
            'is_active' => $this->is_active,
            'executive' => $this->executive,
            'plan' => PlanResource::make($this->whenLoaded('plan')), // Include plan if loaded
            // Do NOT include password or last_token_issued_at
        ];
    }
}
