<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CheckUserStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ensure this runs *after* the 'auth:api' middleware has authenticated the user
        $user = Auth::guard('api')->user(); // Get the authenticated user

        if ($user) {
            // Check if inactive
            if (!$user->is_active) {
                Log::warning('API Access Denied: User ID ' . $user->id . ' is inactive.');
                // Optionally logout the user associated with the token
                // Auth::guard('api')->logout();
                return response()->json([
                     'error' => 'Account inactive.',
                     'message' => 'Your account is inactive. Access denied. Please contact support.',
                     'code' => 'ACCOUNT_INACTIVE' // Specific code for frontend
                ], Response::HTTP_FORBIDDEN); // 403
            }

            // Check expiry date
            if ($user->expiry_date) {
                try {
                    $expiry = Carbon::parse($user->expiry_date)->endOfDay();
                    if (Carbon::now()->isAfter($expiry)) {
                         Log::warning('API Access Denied: Plan expired for user ID ' . $user->id);
                        // Optionally logout the user
                        // Auth::guard('api')->logout();
                         return response()->json([
                             'error' => 'Plan expired.',
                             'message' => 'Your plan has expired. Access denied. Please contact support.',
                             'code' => 'PLAN_EXPIRED' // Specific code for frontend
                         ], Response::HTTP_FORBIDDEN); // 403
                    }
                } catch (\Exception $e) {
                     Log::error('Middleware: Error parsing expiry date for user ID ' . $user->id . ': ' . $e->getMessage());
                     // Decide action on parse error - deny access?
                     // return response()->json(['error'=>'Internal Error'], 500);
                }
            }
        }

        return $next($request); // User is active and plan not expired (or no expiry set)
    }
}
