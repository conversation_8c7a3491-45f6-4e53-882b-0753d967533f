<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\UserSession;

class CheckSingleDeviceSession
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $guard = 'api'): Response
    {
        $user = Auth::guard($guard)->user();
        if (!$user) { 
            return $next($request); 
        }

        // Check if current session is still active using the new session management
        if ($user->current_session_id) {
            $activeSession = UserSession::where('user_id', $user->id)
                ->where('session_id', $user->current_session_id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if (!$activeSession) {
                // Session was invalidated (user logged in elsewhere or session expired)
                Auth::guard($guard)->logout();
                return response()->json([
                    'error' => 'Session Expired',
                    'message' => 'Your session has expired because you logged in elsewhere.',
                    'code' => 'SESSION_EXPIRED_NEW_LOGIN'
                ], Response::HTTP_UNAUTHORIZED);
            }
        }

        // Fallback to original JWT timestamp check for backward compatibility
        if (!isset($user->last_token_issued_at)) {
            Log::warning("CheckSingleDeviceSession: User ID {$user->id} model missing last_token_issued_at for guard '{$guard}'. Skipping timestamp check.");
            return $next($request);
        }

        try {
            $payload = JWTAuth::parseToken()->getPayload();
            $tokenTimestampLimit = $payload->get('ts_limit');
            $dbTimestamp = null;
            if ($user->last_token_issued_at instanceof \Carbon\Carbon) {
                $dbTimestamp = $user->last_token_issued_at->timestamp;
            }

            $isStale = false;
            if (is_null($tokenTimestampLimit) && !is_null($dbTimestamp)) {
                $isStale = true;
                Log::info("CheckSingleDeviceSession: Stale token detected for user {$user->id} (null token ts, DB has ts) on guard '{$guard}'.");
            } elseif (!is_null($tokenTimestampLimit) && !is_null($dbTimestamp) && $tokenTimestampLimit < $dbTimestamp) {
                $isStale = true;
                Log::info("CheckSingleDeviceSession: Stale token detected for user {$user->id} (token ts {$tokenTimestampLimit} < DB ts {$dbTimestamp}) on guard '{$guard}'.");
            }

            if ($isStale) {
                Auth::guard($guard)->logout();
                return response()->json([
                    'error' => 'Session expired.',
                    'message' => 'Your session has expired because you logged in on another device.',
                    'code' => 'SESSION_EXPIRED_NEW_LOGIN'
                ], Response::HTTP_UNAUTHORIZED);
            }

        } catch (TokenExpiredException $e) {
            return response()->json(['error' => 'Token expired.'], Response::HTTP_UNAUTHORIZED);
        } catch (TokenInvalidException $e) {
            return response()->json(['error' => 'Token invalid.'], Response::HTTP_UNAUTHORIZED);
        } catch (JWTException $e) {
            return response()->json(['error' => 'Token absent or unparseable.'], Response::HTTP_UNAUTHORIZED);
        }

        return $next($request);
    }
}
