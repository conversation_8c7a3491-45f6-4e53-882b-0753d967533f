<?php

namespace App\Policies;

use App\Models\ContentHistory;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ContentHistoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Users can view their own list of content histories
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ContentHistory $contentHistory): bool
    {
        return $user->id === $contentHistory->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // Users can create content histories
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ContentHistory $contentHistory): bool
    {
        return $user->id === $contentHistory->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ContentHistory $contentHistory): bool
    {
        return $user->id === $contentHistory->user_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ContentHistory $contentHistory): bool
    {
        return $user->id === $contentHistory->user_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ContentHistory $contentHistory): bool
    {
        return $user->id === $contentHistory->user_id;
    }
}
