<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Facades\Hash;


class Admin extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'admins'; // Explicitly define table name

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'password',
        // Add last_token_issued_at if admins also need single-device login
        // 'last_token_issued_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        // 'last_token_issued_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'password' => 'hashed',
        // 'last_token_issued_at' => 'timestamp',
    ];

    // Automatically hash password when setting it
     public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::needsRehash($value) ? Hash::make($value) : $value;
    }

    // JWTSubject Methods
    public function getJWTIdentifier()
    {
        return $this->getKey(); // Use 'id' by default
    }

    public function getJWTCustomClaims()
    {
        // Add claims if needed, e.g., for single-device login for admins
        // return [
        //     'timestamp_limit' => $this->last_token_issued_at?->timestamp,
        // ];
        return []; // No custom claims needed for admin by default
    }
}
