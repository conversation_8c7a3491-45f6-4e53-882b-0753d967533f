<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail; // Optional: If you want email verification
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Hash;

class User extends Authenticatable implements JWTSubject // Optional: , MustVerifyEmail
{
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users'; // Explicitly define table name

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'state',
        'city',
        'phone',
        'password',
        'purchase_date',
        'expiry_date',
        'plan_id',
        'last_token_issued_at', // <-- Add for single device login
        'current_session_id',
        'last_login_device',
        'last_login_ip',
        'last_login_at',
        'is_active', // <-- Add is_active here
        'executive', // <-- Add executive here
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'last_token_issued_at', // Hide this internal field
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime', // Add if using MustVerifyEmail
        //'password' => 'hashed',
        'purchase_date' => 'date',
        'last_token_issued_at' => 'datetime', // MUST be 'datetime' or 'timestamp'
        'last_login_at' => 'datetime',
        'is_active' => 'boolean', // <-- Add boolean cast here
        'executive' => 'string', // <-- Executive as string
    ];

    // Relationship with Plan
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the content history for the user.
     */
    public function contentHistory(): HasMany
    {
        return $this->hasMany(ContentHistory::class);
    }

    /**
     * Get the user sessions for the user.
     */
    public function userSessions(): HasMany
    {
        return $this->hasMany(UserSession::class);
    }

    /**
     * Get the current active session for the user.
     */
    public function currentSession(): HasMany
    {
        return $this->hasMany(UserSession::class)->where('session_id', $this->current_session_id)->where('is_active', true);
    }

    // JWTSubject Methods
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        $timestampValue = null;

        // Access the property - Laravel should provide a Carbon object due to casting
        $carbonDate = $this->last_token_issued_at;

        // Log what Laravel provides after casting
        \Illuminate\Support\Facades\Log::debug(
            "[getJWTCustomClaims] User: {$this->id} | " .
            "Accessed \$this->last_token_issued_at | Type: " . gettype($carbonDate) . " | " .
            "Value: " . ($carbonDate instanceof \Carbon\Carbon ? $carbonDate->toIso8601String() : json_encode($carbonDate))
        );

        // Check if it's actually a Carbon instance AFTER accessing it
        if ($carbonDate instanceof \Carbon\Carbon) {
            $timestampValue = $carbonDate->timestamp; // Get the Unix timestamp
        } else {
            // Log a warning if casting didn't work as expected
            if (!is_null($carbonDate)) { // Only log if it wasn't supposed to be null
                 \Illuminate\Support\Facades\Log::warning("[getJWTCustomClaims] User: {$this->id} | last_token_issued_at was NOT a Carbon object after access! Casting might be missing or failed.");
            }
        }

        \Illuminate\Support\Facades\Log::debug("[getJWTCustomClaims] User: {$this->id} | Final Timestamp Value for 'ts_limit': " . ($timestampValue ?? 'NULL'));

        return [
            'ts_limit' => $timestampValue, // Use the determined timestamp or null
        ];
    }
    // --- End JWTSubject Methods ---
}
