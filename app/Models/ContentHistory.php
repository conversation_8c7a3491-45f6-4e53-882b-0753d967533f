<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContentHistory extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'content_history';
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'html_content',
    ];

    /**
     * Get the user that owns the ContentHistory
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
