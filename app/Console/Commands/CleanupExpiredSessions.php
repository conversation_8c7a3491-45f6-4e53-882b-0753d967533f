<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSession;
use Carbon\Carbon;

class CleanupExpiredSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:cleanup {--days=30 : Delete sessions older than this many days}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired and old user sessions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        // Delete expired sessions
        $expiredCount = UserSession::where('expires_at', '<', now())
            ->orWhere('created_at', '<', $cutoffDate)
            ->delete();

        // Mark inactive sessions as inactive if they're expired
        $inactiveCount = UserSession::where('expires_at', '<', now())
            ->where('is_active', true)
            ->update(['is_active' => false]);

        $this->info("Cleaned up {$expiredCount} expired sessions.");
        $this->info("Marked {$inactiveCount} sessions as inactive.");

        return Command::SUCCESS;
    }
}
