<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-test {--email=<EMAIL>} {--password=password123} {--name=Test User}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for session management testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email');
        $password = $this->option('password');
        $name = $this->option('name');

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            $this->error("User with email {$email} already exists!");
            return Command::FAILURE;
        }

        // Create the test user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => md5($password), // Using MD5 as per your existing implementation
            'is_active' => true,
            'state' => 'Test State',
            'city' => 'Test City',
            'phone' => '1234567890'
        ]);

        $this->info("Test user created successfully!");
        $this->line("Email: {$email}");
        $this->line("Password: {$password}");
        $this->line("User ID: {$user->id}");

        return Command::SUCCESS;
    }
}
