<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Adds the executive column to the users table.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add the 'executive' column
            $table->boolean('executive')
                  ->nullable()       // Make it optional
                  ->default(null)    // Default value is null
                  ->after('is_active') // Place it after the is_active column
                  ->comment('Indicates if the user has executive privileges');
        });
    }

    /**
     * Reverse the migrations.
     * Removes the executive column from the users table.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the column if the migration is rolled back
            $table->dropColumn('executive');
        });
    }
};
