<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Adds the is_active column to the users table.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add the 'is_active' column
            $table->boolean('is_active') // Creates a BOOLEAN/TINYINT(1) column
                  ->default(true)       // Default new users to active
                  ->after('plan_id')    // Place it after the plan_id column (optional placement)
                  ->comment('Tracks if the user account is active or inactive'); // Optional description
        });
    }

    /**
     * Reverse the migrations.
     * Removes the is_active column from the users table.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the column if the migration is rolled back
            $table->dropColumn('is_active');
        });
    }
};
