<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Changes the executive column from boolean to string in the users table.
     */
    public function up(): void
    {
        // We need to do this in two separate Schema::table calls
        // First, drop the existing column
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('executive');
        });

        // Then add the new column
        Schema::table('users', function (Blueprint $table) {
            $table->string('executive', 255)->nullable()->after('is_active')
                  ->comment('Indicates if the user has executive privileges (as string)');
        });
    }

    /**
     * Reverse the migrations.
     * Changes the executive column back from string to boolean in the users table.
     */
    public function down(): void
    {
        // We need to do this in two separate Schema::table calls
        // First, drop the string column
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('executive');
        });

        // Then add it back as a boolean column
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('executive')->nullable()->after('is_active')
                  ->comment('Indicates if the user has executive privileges');
        });
    }
};
