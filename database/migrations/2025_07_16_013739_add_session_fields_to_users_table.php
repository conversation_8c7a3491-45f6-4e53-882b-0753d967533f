<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('current_session_id')->nullable()->after('last_token_issued_at');
            $table->string('last_login_device')->nullable()->after('current_session_id');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_device');
            $table->timestamp('last_login_at')->nullable()->after('last_login_ip');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'current_session_id',
                'last_login_device', 
                'last_login_ip',
                'last_login_at'
            ]);
        });
    }
};