// database/migrations/xxxx_xx_xx_xxxxxx_add_last_token_issued_at_to_users_table.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->timestamp('last_token_issued_at')->nullable()->after('remember_token'); // Or adjust position
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('last_token_issued_at');
        });
    }
};
