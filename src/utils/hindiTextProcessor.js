// src/utils/hindiTextProcessor.js
/**
 * Utility for post-processing Hindi text from speech recognition
 * to improve accuracy by correcting common misrecognitions
 */

// Common misrecognitions in Hindi speech-to-text
// Format: { 'misrecognized text': 'correct text' }
const commonMisrecognitions = {
  // Numbers
  'एक': 'एक',
  'दो': 'दो',
  'तीन': 'तीन',
  'चार': 'चार',
  'पांच': 'पांच',
  'छह': 'छह',
  'सात': 'सात',
  'आठ': 'आठ',
  'नौ': 'नौ',
  'दस': 'दस',

  // Common words that are frequently misrecognized
  'नमस्कार': 'नमस्कार',
  'नमस्ते': 'नमस्ते',
  'धन्यवाद': 'धन्यवाद',
  'शुक्रिया': 'शुक्रिया',
  'हां': 'हां',
  'नहीं': 'नहीं',
  'क्या': 'क्या',
  'कैसे': 'कैसे',
  'कौन': 'कौन',
  'कहां': 'कहां',
  'क्यों': 'क्यों',

  // Common misrecognitions (examples - you'll need to fill these with actual misrecognitions)
  'नमस्कर': 'नमस्कार',
  'नमसकार': 'नमस्कार',
  'नमसते': 'नमस्ते',
  'धनयवाद': 'धन्यवाद',
  'शुकरिया': 'शुक्रिया',
  'हा': 'हां',
  'नही': 'नहीं',
  'कया': 'क्या',
  'कैसा': 'कैसे',
  'कहा': 'कहां',
  'कयों': 'क्यों',

  // Common phrases
  'आप कैसे हैं': 'आप कैसे हैं',
  'मैं ठीक हूं': 'मैं ठीक हूं',
  'मेरा नाम': 'मेरा नाम',
  'आपका नाम क्या है': 'आपका नाम क्या है',

  // Misrecognized phrases (examples)
  'आप कैसे है': 'आप कैसे हैं',
  'आप केसे हैं': 'आप कैसे हैं',
  'मै ठीक हूं': 'मैं ठीक हूं',
  'मेरा नम': 'मेरा नाम',
  'आपका नाम कया है': 'आपका नाम क्या है',

  // Specific problematic phrases reported by user
  'क्रिकेट से संन्यास ास': 'क्रिकेट से संन्यास',
  'क्रिकेट से सन्यास': 'क्रिकेट से संन्यास',
  'क्रिकेट से सन्यास ास': 'क्रिकेट से संन्यास',
  'क्रिकेट से संन्यास आस': 'क्रिकेट से संन्यास',
  'क्रिकेट संन्यास ास': 'क्रिकेट से संन्यास',

  // Extra "े" character issues
  'पेशपूरी तरह से े': 'पूरी तरह से',
  'पेशपूरी तरह से े ेस से': 'पूरी तरह से',
  'तरह से े': 'तरह से',
  'से े': 'से',
  'े ेस से': 'से',
  'े ेस': '',
};

// Phonetic similarity patterns for Hindi
// These are common substitution patterns in speech recognition
const phoneticPatterns = [
  { pattern: /([क-ह])([ा-ौ])?([ं])?([ँ])?/, replacement: '$1$2$3$4' }, // Preserve basic structure
  { pattern: /([त])([ा-ौ])?/, replacement: '$1$2' }, // त/ट confusion
  { pattern: /([द])([ा-ौ])?/, replacement: '$1$2' }, // द/ड confusion
  { pattern: /([न])([ा-ौ])?/, replacement: '$1$2' }, // न/ण confusion
  { pattern: /([स])([ा-ौ])?/, replacement: '$1$2' }, // स/श confusion
];

/**
 * Process Hindi text to correct common speech recognition errors
 * @param {string} text - The raw text from speech recognition
 * @returns {string} - Corrected text
 */
export function processHindiText(text) {
  if (!text) return text;

  // Special case for the problematic "क्रिकेट से संन्यास ास" pattern
  if (text.includes('क्रिकेट से संन्यास ास') ||
      text.includes('क्रिकेट से सन्यास ास') ||
      text.includes('क्रिकेट से संन्यास आस')) {
    text = text.replace(/क्रिकेट से स[ंन]्यास ा?स/g, 'क्रिकेट से संन्यास');
  }

  // Fix the issue with extra "े" characters
  // Pattern: Remove standalone "े" characters that are not attached to consonants
  text = text.replace(/\s+े+\s+/g, ' '); // Remove standalone े with spaces around it
  text = text.replace(/([^\u0915-\u0939])े+/g, '$1'); // Remove े after non-consonants
  text = text.replace(/े{2,}/g, 'े'); // Replace multiple consecutive े with a single one

  // Fix specific patterns like "तरह से े" -> "तरह से"
  text = text.replace(/([^\s])(\s+)े+(\s+|$)/g, '$1$2$3');

  // Fix specific pattern "पेशपूरी तरह से े ेस से" -> "पूरी तरह से"
  if (text.includes('पेश') && text.includes('तरह से')) {
    text = text.replace(/पेशपूरी तरह से\s+े+\s+ेस\s+से/g, 'पूरी तरह से');
  }

  // First, check for exact matches in our dictionary
  if (commonMisrecognitions[text]) {
    return commonMisrecognitions[text];
  }

  // Split text into words for word-by-word processing
  let words = text.split(' ');
  let correctedWords = words.map(word => {
    // Skip empty words or standalone vowel marks
    if (!word || word === 'े' || word === 'ा' || word === 'ी' || word === 'ू') {
      return '';
    }

    // Check if this word is in our dictionary
    if (commonMisrecognitions[word]) {
      return commonMisrecognitions[word];
    }

    // Fix words ending with extra े
    if (word.endsWith('े') && word.length > 1) {
      const lastChar = word.charAt(word.length - 2);
      // If the character before े is not a consonant, remove the े
      if (!/[\u0915-\u0939]/.test(lastChar)) {
        word = word.substring(0, word.length - 1);
      }
    }

    // If not found directly, try to find the closest match
    // This is a simple implementation - could be improved with more sophisticated
    // string similarity algorithms
    for (const [misrecognized, correct] of Object.entries(commonMisrecognitions)) {
      // Skip if lengths are too different (optimization)
      if (Math.abs(word.length - misrecognized.length) > 2) continue;

      // Simple character-based similarity check
      let similarity = calculateSimilarity(word, misrecognized);
      if (similarity > 0.8) { // 80% similarity threshold
        return correct;
      }
    }

    return word; // Keep original if no correction found
  });

  // Filter out empty strings and join
  let result = correctedWords.filter(word => word).join(' ');

  // Final check for the problematic pattern in the full text
  if (result.includes('ास') && result.includes('क्रिकेट')) {
    result = result.replace(/क्रिकेट से स[ंन]्यास ा?स/g, 'क्रिकेट से संन्यास');
  }

  // Clean up multiple spaces
  result = result.replace(/\s+/g, ' ').trim();

  return result;
}

/**
 * Calculate similarity between two strings (0-1 scale)
 * This is a simple implementation of string similarity
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Similarity score (0-1)
 */
function calculateSimilarity(str1, str2) {
  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;

  // Count matching characters
  let matches = 0;
  const maxLength = Math.max(str1.length, str2.length);

  // Simple character matching
  for (let i = 0; i < str1.length; i++) {
    if (i < str2.length && str1[i] === str2[i]) {
      matches++;
    }
  }

  return matches / maxLength;
}

/**
 * Process Hindi text with context awareness
 * This function can be expanded to use surrounding words for better corrections
 * @param {string} text - Full text to process
 * @returns {string} - Corrected text
 */
export function processHindiTextWithContext(text) {
  if (!text) return text;

  // Basic processing first
  let processed = processHindiText(text);

  // Here you could add more sophisticated context-aware processing
  // For example, checking grammar patterns or n-gram models

  return processed;
}

export default processHindiText;
