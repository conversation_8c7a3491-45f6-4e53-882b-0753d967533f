// src/utils/languageProcessor.js
/**
 * Utility for post-processing text from speech recognition for various languages
 * to improve accuracy by correcting common misrecognitions
 */

import { processHindiText } from './hindiTextProcessor';

// Common misrecognitions in Marathi speech-to-text
// Format: { 'misrecognized text': 'correct text' }
const marathiMisrecognitions = {
  // Numbers
  'एक': 'एक',
  'दोन': 'दोन',
  'तीन': 'तीन',
  'चार': 'चार',
  'पाच': 'पाच',
  'सहा': 'सहा',
  'सात': 'सात',
  'आठ': 'आठ',
  'नऊ': 'नऊ',
  'दहा': 'दहा',
  
  // Common words that are frequently misrecognized
  'नमस्कार': 'नमस्कार',
  'धन्यवाद': 'धन्यवाद',
  'आभारी': 'आभारी',
  'हो': 'हो',
  'नाही': 'नाही',
  'काय': 'काय',
  'कसे': 'कसे',
  'कोण': 'कोण',
  'कुठे': 'कुठे',
  'का': 'का',
  
  // Common misrecognitions (examples)
  'नमसकार': 'नमस्कार',
  'धनयवाद': 'धन्यवाद',
  'आभारी आहे': 'आभारी आहे',
  'नाहि': 'नाही',
  'कय': 'काय',
  'कसा': 'कसे',
  'कुठे आहे': 'कुठे आहे',
};

// Common misrecognitions in Bengali speech-to-text
const bengaliMisrecognitions = {
  // Basic words
  'নমস্কার': 'নমস্কার',
  'ধন্যবাদ': 'ধন্যবাদ',
  'হ্যাঁ': 'হ্যাঁ',
  'না': 'না',
  'কি': 'কি',
  'কেমন': 'কেমন',
  'কে': 'কে',
  'কোথায়': 'কোথায়',
  'কেন': 'কেন',
  
  // Misrecognitions
  'নমসকার': 'নমস্কার',
  'ধনযবাদ': 'ধন্যবাদ',
  'হযাঁ': 'হ্যাঁ',
  'কেমন আছেন': 'কেমন আছেন',
};

// Common misrecognitions in Gujarati speech-to-text
const gujaratiMisrecognitions = {
  // Basic words
  'નમસ્તે': 'નમસ્તે',
  'આભાર': 'આભાર',
  'હા': 'હા',
  'ના': 'ના',
  'શું': 'શું',
  'કેમ': 'કેમ',
  'કોણ': 'કોણ',
  'ક્યાં': 'ક્યાં',
  'કેમ': 'કેમ',
  
  // Misrecognitions
  'નમસતે': 'નમસ્તે',
  'આભાર': 'આભાર',
  'શુ': 'શું',
  'કેમ છો': 'કેમ છો',
};

/**
 * Calculate similarity between two strings (0-1 scale)
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Similarity score (0-1)
 */
function calculateSimilarity(str1, str2) {
  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;
  
  // Count matching characters
  let matches = 0;
  const maxLength = Math.max(str1.length, str2.length);
  
  // Simple character matching
  for (let i = 0; i < str1.length; i++) {
    if (i < str2.length && str1[i] === str2[i]) {
      matches++;
    }
  }
  
  return matches / maxLength;
}

/**
 * Process text with a dictionary of misrecognitions
 * @param {string} text - Text to process
 * @param {Object} dictionary - Dictionary of misrecognitions
 * @returns {string} - Corrected text
 */
function processWithDictionary(text, dictionary) {
  if (!text) return text;
  
  // First, check for exact matches in our dictionary
  if (dictionary[text]) {
    return dictionary[text];
  }
  
  // Split text into words for word-by-word processing
  let words = text.split(' ');
  let correctedWords = words.map(word => {
    // Check if this word is in our dictionary
    if (dictionary[word]) {
      return dictionary[word];
    }
    
    // If not found directly, try to find the closest match
    for (const [misrecognized, correct] of Object.entries(dictionary)) {
      // Skip if lengths are too different (optimization)
      if (Math.abs(word.length - misrecognized.length) > 2) continue;
      
      // Simple character-based similarity check
      let similarity = calculateSimilarity(word, misrecognized);
      if (similarity > 0.8) { // 80% similarity threshold
        return correct;
      }
    }
    
    return word; // Keep original if no correction found
  });
  
  return correctedWords.join(' ');
}

/**
 * Process Marathi text to correct common speech recognition errors
 * @param {string} text - The raw text from speech recognition
 * @returns {string} - Corrected text
 */
export function processMarathiText(text) {
  return processWithDictionary(text, marathiMisrecognitions);
}

/**
 * Process Bengali text to correct common speech recognition errors
 * @param {string} text - The raw text from speech recognition
 * @returns {string} - Corrected text
 */
export function processBengaliText(text) {
  return processWithDictionary(text, bengaliMisrecognitions);
}

/**
 * Process Gujarati text to correct common speech recognition errors
 * @param {string} text - The raw text from speech recognition
 * @returns {string} - Corrected text
 */
export function processGujaratiText(text) {
  return processWithDictionary(text, gujaratiMisrecognitions);
}

/**
 * Process text based on language code
 * @param {string} text - Text to process
 * @param {string} languageCode - Language code (e.g., 'hi', 'mr', 'bn', 'gu')
 * @returns {string} - Processed text
 */
export function processTextByLanguage(text, languageCode) {
  if (!text || !languageCode) return text;
  
  // Extract base language code if it includes region (e.g., 'hi-in' -> 'hi')
  const baseLanguage = languageCode.split('-')[0].toLowerCase();
  
  switch (baseLanguage) {
    case 'hi': // Hindi
      return processHindiText(text);
    case 'mr': // Marathi
      return processMarathiText(text);
    case 'bn': // Bengali
      return processBengaliText(text);
    case 'gu': // Gujarati
      return processGujaratiText(text);
    default:
      return text; // Return original text for unsupported languages
  }
}

export default processTextByLanguage;
