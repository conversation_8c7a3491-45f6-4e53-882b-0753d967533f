// src/components/UserProtectedRoute.js
import React, { useEffect } from 'react';
import { Navigate, Outlet, useLocation, Link, useNavigate } from 'react-router-dom'; // Added useNavigate
import { useAuth } from '../context/AuthContext';
import LoadingSpinner from './common/LoadingSpinner'; // Optional: Adjust path
// **** Import date-fns functions ****
import { parseISO, isAfter, isValid } from 'date-fns';

// --- Access Denied Message Component ---
// Now accepts logout function as a prop
const AccessDeniedMessage = ({ reason, onLogout }) => {
    let message = "Your account requires attention.";
    let title = "Access Denied";

    if (reason === 'inactive') {
        message = "Your account is currently inactive.";
        title = "Account Inactive";
    } else if (reason === 'no_plan') {
        message = "You do not have an active plan assigned to your account.";
        title = "No Plan Assigned";
    } else if (reason === 'expired') {
        message = "Your current plan has expired.";
        title = "Plan Expired";
    }

    // Handle logout action
    const handleSwitchAccount = () => {
        if (onLogout) {
            onLogout(); // Call the passed logout function (clears state/storage)
            // Navigation happens after state clears in the main component or context effect
        }
    };

    return (
        <div style={{ /* ... styles for centering ... */
            display: 'flex', flexDirection: 'column', alignItems: 'center',
            justifyContent: 'center', minHeight: '80vh', padding: '20px', textAlign: 'center'
        }}>
            <i className="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h2>{title}</h2>
            <p className="lead text-muted">{message}</p>
            <p>Please contact support or your administrator for assistance.</p>
            <div className="mt-3">
                {/* Option 1: Button to log out / switch account */}
                <button onClick={handleSwitchAccount} className="btn btn-outline-primary mr-2">
                    Login with Different Account
                </button>
                {/* Option 2: Link to landing page */}
                <Link to="/" className="btn btn-secondary">
                    Go to Homepage
                </Link>
            </div>
        </div>
    );
};

// --- Protected Route Component ---
const UserProtectedRoute = () => {
    // Get USER specific state AND logout function
    const { userToken, user, userIsLoading, logoutUser } = useAuth();
    const location = useLocation();
    const navigate = useNavigate(); // Hook for navigation after logout

    // Cleanup AdminLTE classes
    useEffect(() => {
        document.body.classList.remove('login-page', 'register-page', 'sidebar-mini', 'layout-fixed');
    }, []);

    // --- 1. Handle Loading State ---
    if (userIsLoading && !user) {
       return ( <div style={{ /* ... styles ... */ }}><LoadingSpinner /></div> );
    }

    // --- 2. Handle No Token ---
    if (!userToken) {
        return <Navigate to="/user/login" state={{ from: location }} replace />;
    }

    // --- 3. Handle Missing User Data After Loading ---
    if (!user) {
        console.warn("UserProtectedRoute: Token exists but user data missing. Forcing logout/re-login.");
        // Force logout if user data is inconsistent with token
        logoutUser();
        return <Navigate to="/user/login" state={{ from: location }} replace />;
    }

    // --- 4. Check User Status, Plan, and Expiry ---
    // Active Check
    if (!user.is_active) {
        console.warn(`UserProtectedRoute: Access denied for user ${user.id} - Inactive account.`);
        // Pass logoutUser function to the message component
        return <AccessDeniedMessage reason="inactive" onLogout={() => { logoutUser(); navigate('/user/login'); }} />;
    }

    // Plan Existence Check
    // Use `plan_id` if the nested `plan` object might not always be loaded
    if (user.plan_id === null || !user.plan) { // Check both just in case
        console.warn(`UserProtectedRoute: Access denied for user ${user.id} - No plan assigned.`);
        return <AccessDeniedMessage reason="no_plan" onLogout={() => { logoutUser(); navigate('/user/login'); }} />;
    }

    // Plan Expiry Check
    if (user.expiry_date) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Compare start of day
            const expiryDateObj = parseISO(user.expiry_date);
             expiryDateObj.setHours(0,0,0,0); // Compare start of day

            if (isValid(expiryDateObj)) {
                if (isAfter(today, expiryDateObj)) { // Check if today is strictly *after* expiry date
                    console.warn(`UserProtectedRoute: Access denied for user ${user.id} - Plan expired on ${user.expiry_date}.`);
                    return <AccessDeniedMessage reason="expired" onLogout={() => { logoutUser(); navigate('/user/login'); }} />;
                }
            } else {
                 console.error(`UserProtectedRoute: Invalid expiry date format for user ${user.id}: ${user.expiry_date}`);
                 // Decide: Allow access with invalid date or deny? Denying might be safer.
                 // return <AccessDeniedMessage reason="invalid_date" onLogout={() => { logoutUser(); navigate('/user/login'); }} />;
            }
        } catch (error) {
            console.error(`UserProtectedRoute: Error parsing expiry date for user ${user.id}:`, error);
            // Deny access if date parsing fails
            // return <AccessDeniedMessage reason="date_error" onLogout={() => { logoutUser(); navigate('/user/login'); }} />;
        }
    }
     // If no expiry date exists, maybe allow access or treat as 'no plan'? Depends on business logic.
     // else if (!user.expiry_date && user.plan_id) { // Has plan but no expiry?
     //     console.warn(`UserProtectedRoute: User ${user.id} has plan but no expiry date.`);
     //     // Decide if access is allowed in this case. Currently allows.
     // }


    // --- 5. Allow Access ---
    return <Outlet />; // Render the intended child route component
};

export default UserProtectedRoute;