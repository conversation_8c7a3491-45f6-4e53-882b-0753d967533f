// src/components/common/SessionConflictDialog.js
import React from 'react';
import styles from './ErrorDialog.module.css'; // Reuse existing modal styles

function SessionConflictDialog({ 
    title = "Session Conflict", 
    message = "You are already logged in on another device. Do you want to log out from that device and continue here?", 
    onConfirm, 
    onCancel,
    isLoading = false 
}) {
    return (
        <div className={styles.modalBackdrop}>
            <div className={styles.modalContentWrapper}>
                <div className={styles.modalHeader}>
                    <h3>
                        <i className="fas fa-exclamation-triangle text-warning mr-2"></i> 
                        {title}
                    </h3>
                </div>
                <div className={styles.modalBody}>
                    <p>{message}</p>
                </div>
                <div className={styles.modalFooter}>
                    <button 
                        onClick={onCancel} 
                        className="btn btn-secondary me-2"
                        disabled={isLoading}
                    >
                        Cancel
                    </button>
                    <button 
                        onClick={onConfirm} 
                        className={`btn btn-primary ${styles.actionButton}`}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Logging in...
                            </>
                        ) : (
                            'Yes, Log Out Other Device'
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
}

export default SessionConflictDialog;