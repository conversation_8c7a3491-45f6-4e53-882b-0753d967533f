.modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent black */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050; /* Ensure it's above other content */
    padding: 15px;
  }
  
  .modalContentWrapper {
    background-color: #fff;
    border-radius: 0.3rem; /* Bootstrap's default modal radius */
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 500px; /* Limit width on larger screens */
    display: flex;
    flex-direction: column;
    max-height: 90vh; /* Limit height */
    overflow: hidden; /* Prevent content spilling */
  }
  
  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6; /* Bootstrap modal header border */
    background-color: #f8f9fa; /* Light background for header */
  }
  
  .modalHeader h3 {
    margin-bottom: 0;
    font-size: 1.25rem; /* Adjust as needed */
    display: flex;
    align-items: center;
  }
  
  .modalCloseBtn {
    background: none;
    border: none;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto; /* Align right */
    cursor: pointer;
  }
  .modalCloseBtn:hover {
    opacity: 0.75;
  }
  
  .modalBody {
    position: relative;
    flex: 1 1 auto; /* Allow body to grow and shrink */
    padding: 1rem;
    overflow-y: auto; /* Add scroll if content is tall */
  }
  
  .modalBody p {
      margin-bottom: 1rem; /* Spacing between paragraphs */
  }
   .modalBody p:last-child {
      margin-bottom: 0;
  }
  
  .modalFooter {
    display: flex;
    justify-content: flex-end; /* Align buttons to the right */
    align-items: center;
    padding: 0.75rem 1rem;
    border-top: 1px solid #dee2e6; /* Bootstrap modal footer border */
    background-color: #f8f9fa; /* Light background for footer */
  }
  
  .modalFooter .actionButton {
     /* Add any specific styles for footer buttons if needed */
     margin-left: 0.5rem; /* Space between buttons if multiple */
  }
  
  /* Ensure text-danger class works if not globally defined */
  .text-danger {
      color: #dc3545 !important;
  }
  
  /* Adjust icon margin if needed */
  .modalHeader h3 .mr-2 {
      margin-right: 0.5rem;
  }