// src/components/common/ErrorDialog.js
import React from 'react';
import styles from './ErrorDialog.module.css'; // Create this CSS Module

function ErrorDialog({ title = "Access Denied", message, onClose }) {
    if (!message) return null; // Don't render if no message

    return (
        <div className={styles.modalBackdrop}> {/* Re-use modal styles or create new */}
            <div className={styles.modalContentWrapper}> {/* Re-use modal styles */}
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-exclamation-triangle text-danger mr-2"></i> {title}</h3>
                    {/* Optional: Hide close button if action is mandatory */}
                    {/* <button onClick={onClose} className={styles.modalCloseBtn}>×</button> */}
                </div>
                <div className={styles.modalBody}>
                    <p>{message}</p>
                    <p>Please contact administrator for assistance.</p>
                    {/* You could add contact info here */}
                </div>
                <div className={styles.modalFooter}>
                    {/* Force user to acknowledge */}
                    <button onClick={onClose} className={`btn btn-primary ${styles.actionButton}`}>
                        OK
                    </button>
                </div>
            </div>
        </div>
    );
}

export default ErrorDialog;