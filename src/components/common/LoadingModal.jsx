import React from 'react';
import LoadingSpinner from './LoadingSpinner';
import styles from '../../pages/editor/main.module.css'; // Assuming styles are shared or passed

const LoadingModal = ({ isVisible, message }) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop}>
            <div className={`${styles.modalContentWrapper} ${styles.smallLoading}`} onClick={(e) => e.stopPropagation()}>
                <div className={`${styles.modalHeader} ${styles.hidden}`}></div>
                <div className={`${styles.modalBody} ${styles.noPadding}`} style={{ textAlign: 'center', padding: '30px' }}>
                    <LoadingSpinner />
                    <p style={{ marginTop: '10px', fontSize: '0.9rem', color: '#555' }}>{message}</p>
                </div>
            </div>
        </div>
    );
};

export default LoadingModal;