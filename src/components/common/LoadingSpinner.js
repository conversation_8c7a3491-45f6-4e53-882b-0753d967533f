// src/components/common/LoadingSpinner.js
import React from 'react';

const LoadingSpinner = ({ small = false, centered = true }) => (
    <div className={`d-flex ${centered ? 'justify-content-center align-items-center py-3' : ''}`}>
        <div className={`spinner-border text-primary ${small ? 'spinner-border-sm' : ''}`} role="status">
            <span className="visually-hidden sr-only">Loading...</span> {/* Use visually-hidden for accessibility */}
        </div>
    </div>
);

export default LoadingSpinner;