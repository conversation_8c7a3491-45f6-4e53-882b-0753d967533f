// src/components/ProtectedRoute.js
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import AdminLayout from '../layout/AdminLayout';

const ProtectedRoute = () => {
  const { adminToken, isLoading } = useAuth();
  const location = useLocation();

  // If initial check is happening, show loading (optional)
  // This depends on how isLoading is managed in AuthContext
  if (isLoading && !adminToken) {
     return <div>Checking authentication...</div>; // Or spinner
  }

  // If no token after loading/checking, redirect to login
  if (!adminToken) {
    console.log("ProtectedRoute: No token found, redirecting to login.");
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If token exists, render the layout which contains the Outlet for nested routes
  // console.log("ProtectedRoute: Token found, rendering AdminLayout.");
  return <AdminLayout />;
};

export default ProtectedRoute;