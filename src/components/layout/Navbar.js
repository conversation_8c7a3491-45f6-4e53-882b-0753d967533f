// src/components/layout/Navbar.js
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
// Import the updated Auth hook
import { useAuth } from '../../context/AuthContext'; // Adjust path if needed

function Navbar() {
    // **** FIX: Destructure the correct ADMIN logout function ****
    const { logoutAdmin } = useAuth();
    // *********************************************************
    const navigate = useNavigate();

    const handleLogout = async (e) => {
        e.preventDefault(); // Prevent default link behavior if using <a>

        // **** FIX: Call the correct ADMIN logout function ****
        // No need for await if logoutAdmin is synchronous in context
        logoutAdmin();
        // ****************************************************

        navigate('/login'); // Redirect to ADMIN login page after logout
    };

    return (
        <nav className="main-header navbar navbar-expand navbar-white navbar-light">
            {/* Left navbar links */}
            <ul className="navbar-nav">
                <li className="nav-item">
                    {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                    <a className="nav-link" data-widget="pushmenu" href="#" role="button"><i className="fas fa-bars"></i></a>
                </li>
                <li className="nav-item d-none d-sm-inline-block">
                    {/* Link to admin dashboard */}
                    <Link to="/dashboard" className="nav-link">Home</Link>
                </li>
            </ul>

            {/* Right navbar links */}
            <ul className="navbar-nav ml-auto">
                 {/* Optional: Messages/Notifications */}

                <li className="nav-item">
                     {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                    <a className="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i className="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>

                {/* Logout Button */}
                <li className="nav-item">
                     {/* Ensure onClick calls the correct handleLogout */}
                     {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                    <a href="#" className="nav-link" onClick={handleLogout} role="button" title="Logout">
                        <i className="fas fa-sign-out-alt"></i>
                    </a>
                </li>

                {/* Optional: Control Sidebar Toggle */}
            </ul>
        </nav>
    );
}

export default Navbar;