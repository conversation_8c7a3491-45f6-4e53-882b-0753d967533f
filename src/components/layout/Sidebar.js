// src/components/layout/Sidebar.js
import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

function Sidebar() {
    const { adminUser } = useAuth();

    const getNavLinkClass = ({ isActive }) => {
        return isActive ? "nav-link active" : "nav-link";
    };

    return (
        <aside className="main-sidebar sidebar-dark-primary elevation-4">
            <Link to="/dashboard" className="brand-link">
                <img src="/logo192.png"
                     alt="Admin Panel Logo"
                     className="brand-image img-circle elevation-3"
                     style={{ opacity: .8 }} />
                <span className="brand-text font-weight-light">Admin Panel</span>
            </Link>

            <div className="sidebar">
                {adminUser && (
                    <div className="user-panel mt-3 pb-3 mb-3 d-flex">
                        <div className="image">
                            <img src="https://adminlte.io/themes/v3/dist/img/user2-160x160.jpg"
                                 className="img-circle elevation-2"
                                 alt="User" />
                        </div>
                        <div className="info">
                            <span className="d-block text-white">{adminUser.username}</span>
                        </div>
                    </div>
                )}

                <nav className="mt-2">
                    <ul className="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li className="nav-item">
                            <NavLink to="/dashboard" className={getNavLinkClass} end>
                                <i className="nav-icon fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </NavLink>
                        </li>
                        <li className="nav-item">
                             <NavLink to="/users" className={getNavLinkClass}>
                                <i className="nav-icon fas fa-users"></i>
                                <p>Users</p>
                            </NavLink>
                        </li>
                        <li className="nav-item">
                            <NavLink to="/plans" className={getNavLinkClass}>
                                <i className="nav-icon fas fa-list-alt"></i>
                                <p>Plans</p>
                            </NavLink>
                        </li>
                         <li className="nav-item">
                            <NavLink to="/admins" className={getNavLinkClass}>
                                <i className="nav-icon fas fa-user-shield"></i>
                                <p>Admins</p>
                            </NavLink>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
    );
}

export default Sidebar;