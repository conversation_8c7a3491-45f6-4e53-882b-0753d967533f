// src/components/SessionTestComponent.js
import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';

const SessionTestComponent = () => {
    const { loginUser, user, userToken, userAuthError, userIsLoading } = useAuth();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');

    const handleLogin = async (e) => {
        e.preventDefault();
        if (!email || !password) {
            alert('Please enter both email and password');
            return;
        }
        
        const success = await loginUser(email, password);
        if (success) {
            console.log('Login successful');
        }
    };

    if (user && userToken) {
        return (
            <div className="container mt-4">
                <div className="card">
                    <div className="card-header bg-success text-white">
                        <h4>✅ Logged In Successfully</h4>
                    </div>
                    <div className="card-body">
                        <p><strong>User:</strong> {user.name || user.email}</p>
                        <p><strong>Email:</strong> {user.email}</p>
                        <p><strong>Token:</strong> {userToken.substring(0, 20)}...</p>
                        <div className="alert alert-info">
                            <strong>Test Instructions:</strong>
                            <ol>
                                <li>Keep this browser/tab open</li>
                                <li>Open another browser or incognito tab</li>
                                <li>Try to login with the same credentials</li>
                                <li>You should see a session conflict dialog</li>
                                <li>Click "Yes, Log Out Other Device" to test force login</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mt-4">
            <div className="row justify-content-center">
                <div className="col-md-6">
                    <div className="card">
                        <div className="card-header">
                            <h4>Session Management Test</h4>
                        </div>
                        <div className="card-body">
                            <form onSubmit={handleLogin}>
                                <div className="mb-3">
                                    <label htmlFor="email" className="form-label">Email</label>
                                    <input
                                        type="email"
                                        className="form-control"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="mb-3">
                                    <label htmlFor="password" className="form-label">Password</label>
                                    <input
                                        type="password"
                                        className="form-control"
                                        id="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                    />
                                </div>
                                {userAuthError && (
                                    <div className="alert alert-danger">
                                        {userAuthError}
                                    </div>
                                )}
                                <button 
                                    type="submit" 
                                    className="btn btn-primary w-100"
                                    disabled={userIsLoading}
                                >
                                    {userIsLoading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Logging in...
                                        </>
                                    ) : (
                                        'Login'
                                    )}
                                </button>
                            </form>
                            
                            <div className="mt-4">
                                <h6>How to Test Session Management:</h6>
                                <ol className="small">
                                    <li>Login with valid credentials here</li>
                                    <li>Open another browser/incognito tab</li>
                                    <li>Navigate to the same login page</li>
                                    <li>Try to login with same credentials</li>
                                    <li>Should see session conflict dialog</li>
                                    <li>Test both "Cancel" and "Yes, Log Out Other Device" options</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SessionTestComponent;