// src/contexts/AuthContext.js
import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
// Adjust path to your api service file if needed
import apiClient, {
    loginAdmin as apiLoginAdmin,
    getAdminProfile as apiGetAdminProfile, // Assuming you might want this later
    loginUser as apiLoginUser,
    forceLoginUser as apiForceLoginUser,
    registerUser as apiRegisterUser,
    // getUserProfile as apiGetUserProfile // Add if/when implemented
} from '../services/api';
// Adjust path to your ErrorDialog component if needed
import ErrorDialog from '../components/common/ErrorDialog';
import SessionConflictDialog from '../components/common/SessionConflictDialog';

// Create the context
const AuthContext = createContext(null);

// Create the Provider Component
export const AuthProvider = ({ children }) => {
    // --- Admin State ---
    const [adminToken, setAdminToken] = useState(localStorage.getItem('adminToken'));
    const [adminUser, setAdminUser] = useState(() => {
        const stored = localStorage.getItem('adminUser');
        try { return stored ? JSON.parse(stored) : null; } catch (e) { return null; }
    });
    const [adminIsLoading, setAdminIsLoading] = useState(false); // Separate loading state
    const [adminAuthError, setAdminAuthError] = useState(null);

    // --- User State ---
    const [userToken, setUserToken] = useState(localStorage.getItem('userToken'));
    const [user, setUser] = useState(() => {
        const stored = localStorage.getItem('user');
        try { return stored ? JSON.parse(stored) : null; } catch (e) { return null; }
    });
    const [userIsLoading, setUserIsLoading] = useState(false); // Separate loading state
    const [userAuthError, setUserAuthError] = useState(null);
    const [userRegisterSuccess, setUserRegisterSuccess] = useState(null);

    // --- Global Error Dialog State ---
    const [globalErrorDialog, setGlobalErrorDialog] = useState({ show: false, title: '', message: '' });

    // --- Session Conflict Dialog State ---
    const [sessionConflictDialog, setSessionConflictDialog] = useState({ 
        show: false, 
        credentials: null, 
        isLoading: false 
    });

    // --- Show/Close Error Dialog Functions ---
    const showGlobalErrorDialog = useCallback((title, message) => {
        console.log("Showing global error:", title, message); // Debug
        setGlobalErrorDialog({ show: true, title: title || "Error", message: message || "An error occurred." });
    }, []); // Empty dependency array, function identity is stable

    const closeGlobalErrorDialog = useCallback(() => {
        setGlobalErrorDialog({ show: false, title: '', message: '' });
        // Determine where to redirect after closing potentially critical errors
        // If triggered by user error, send to user login. If admin, admin login?
        // This basic redirect sends to user login for 403/500 etc.
        if (window.location.pathname !== '/user/login' && window.location.pathname !== '/login') {
            console.log("Redirecting after closing error dialog."); // Debug
            window.location.href = '/user/login';
        }
    }, []); // Empty dependency array


    // --- Authentication Functions ---

    const loginAdmin = async (username, password) => {
        setAdminIsLoading(true);
        setAdminAuthError(null);
        if (userToken) logoutUser(); // Logout user if admin logs in
        try {
            const response = await apiLoginAdmin({ username, password });
            const { access_token, admin } = response.data;
            setAdminToken(access_token);
            setAdminUser(admin);
            localStorage.setItem('adminToken', access_token);
            localStorage.setItem('adminUser', JSON.stringify(admin));
            return true;
        } catch (error) {
            const message = error.response?.data?.error || 'Admin login failed.';
            setAdminAuthError(message);
            return false;
        } finally {
             setAdminIsLoading(false);
        }
    };

    const logoutAdmin = useCallback(() => { // Wrap in useCallback if passed as dependency
        setAdminToken(null); setAdminUser(null);
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        console.log("AuthContext: Admin logged out.");
        // Navigation should be handled by the component calling this (e.g., Navbar)
    }, []);

    const loginUser = async (email, password) => {
        setUserIsLoading(true);
        setUserAuthError(null);
        if (adminToken) logoutAdmin(); // Logout admin if user logs in
        try {
            const response = await apiLoginUser({ email, password });
            const { access_token, user: userData } = response.data;
            setUserToken(access_token);
            setUser(userData);
            localStorage.setItem('userToken', access_token);
            localStorage.setItem('user', JSON.stringify(userData));
            return true;
        } catch (error) {
            // Check for session conflict (409 Conflict)
            if (error.response && error.response.status === 409 && error.response.data?.code === 'SESSION_CONFLICT') {
                console.log("Session conflict detected, showing confirmation dialog");
                setSessionConflictDialog({
                    show: true,
                    credentials: { email, password },
                    isLoading: false
                });
                return false;
            }
            // Check for specific 403 errors from login attempt
            else if (error.response && error.response.status === 403 && error.response.data?.code) {
                 showGlobalErrorDialog(error.response.data.error || "Access Denied", error.response.data.message || "Please contact support.");
                 // Don't set userAuthError if showing global dialog
            } else {
                const message = error.response?.data?.error || 'User login failed.';
                setUserAuthError(message);
            }
            return false;
        } finally {
             setUserIsLoading(false);
        }
    };

     const registerUser = async (userData) => {
        setUserIsLoading(true);
        setUserAuthError(null);
        setUserRegisterSuccess(null);
        try {
            const response = await apiRegisterUser(userData);
            setUserRegisterSuccess(response.data.message || "Registration successful! Please log in.");
            return true; // Indicate success
        } catch (error) {
            let message = 'Registration failed. Please try again.';
            if (error.response && error.response.status === 422) {
                 message = Object.values(error.response.data.errors).map(errArray => errArray[0]).join(' ');
                 setUserAuthError(message || 'Please check the registration form for errors.');
            } else {
                 setUserAuthError(error.response?.data?.message || message);
            }
            return false; // Indicate failure
        } finally {
            setUserIsLoading(false);
        }
    };

    const logoutUser = useCallback(() => { // Wrap in useCallback if passed as dependency
        setUserToken(null); setUser(null);
        localStorage.removeItem('userToken');
        localStorage.removeItem('user');
        console.log("AuthContext: User logged out.");
        // Navigation should be handled by the component calling this (e.g., ModernSpeechEditor Navbar)
    }, []);

    // --- Session Conflict Dialog Handlers ---
    const handleSessionConflictConfirm = async () => {
        if (!sessionConflictDialog.credentials) return;
        
        setSessionConflictDialog(prev => ({ ...prev, isLoading: true }));
        
        try {
            const { email, password } = sessionConflictDialog.credentials;
            const response = await apiForceLoginUser({ email, password });
            const { access_token, user: userData } = response.data;
            
            setUserToken(access_token);
            setUser(userData);
            localStorage.setItem('userToken', access_token);
            localStorage.setItem('user', JSON.stringify(userData));
            
            // Close dialog and clear credentials
            setSessionConflictDialog({ show: false, credentials: null, isLoading: false });
            
            console.log("Force login successful");
        } catch (error) {
            console.error("Force login failed:", error);
            const message = error.response?.data?.error || 'Force login failed.';
            setUserAuthError(message);
            setSessionConflictDialog({ show: false, credentials: null, isLoading: false });
        }
    };

    const handleSessionConflictCancel = () => {
        setSessionConflictDialog({ show: false, credentials: null, isLoading: false });
        setUserAuthError(null); // Clear any previous errors
    };


    // --- Axios Interceptor Setup (Runs Once) ---
    useEffect(() => {
        console.log("AuthContext: Setting up Axios interceptor."); // DEBUG
        const responseInterceptor = apiClient.interceptors.response.use(
            (response) => response, // Pass through success
            (error) => {
                // IMPORTANT: Check if error and error.response exist before accessing properties
                if (error.response) {
                    const status = error.response.status;
                    const data = error.response.data; // API response data (might be JSON or HTML)

                    console.error(`AuthContext Interceptor: API Error Status=${status}`, data);

                    if (status === 401) {
                        // Check for session expired due to new login
                        if (data?.code === 'SESSION_EXPIRED_NEW_LOGIN') {
                            console.warn("AuthContext Interceptor: Session expired due to new login elsewhere.");
                            logoutUser(); // Clear user state/storage
                            showGlobalErrorDialog(
                                "Session Expired", 
                                data.message || "Your session has expired because you logged in elsewhere."
                            );
                        } else {
                            console.warn("AuthContext Interceptor: Unauthorized (401). Logging out user & admin.");
                            logoutAdmin(); // Clear admin state/storage
                            logoutUser();  // Clear user state/storage
                        }
                        // Redirect after clearing state (handled by component or ProtectedRoute now)
                        // Avoid direct window.location if possible, rely on React Router redirects
                    } else if (status === 403 && data?.code && (data.code === 'ACCOUNT_INACTIVE' || data.code === 'PLAN_EXPIRED')) {
                        console.warn(`AuthContext Interceptor: Forbidden (403) - Code: ${data.code}. Showing dialog.`);
                        logoutUser(); // Log out the user encountering the error
                        showGlobalErrorDialog(data.error || "Access Denied", data.message || "Please contact support.");
                        // The dialog's onClose will handle redirection
                    } else if (status >= 500) {
                        console.error("AuthContext Interceptor: Server Error (5xx). Showing dialog.");
                         showGlobalErrorDialog("Server Error", "An unexpected error occurred on the server. Please try again later.");
                    }
                    // Allow other errors (like 404, 422 validation) to pass through to the calling function's catch block
                } else {
                    // Handle network errors or errors without a response
                    console.error("AuthContext Interceptor: Network or other error:", error);
                    showGlobalErrorDialog("Network Error", "Could not connect to the server. Please check your connection.");
                }
                // Crucially, reject the promise so the original .catch() block in the calling code still runs
                return Promise.reject(error);
            }
        );

        // Cleanup function to eject the interceptor when the AuthProvider unmounts
        return () => {
            console.log("AuthContext: Ejecting Axios interceptor."); // DEBUG
            apiClient.interceptors.response.eject(responseInterceptor);
        };
    // Dependencies: Include functions from useCallback that are used inside
    }, [showGlobalErrorDialog, logoutAdmin, logoutUser]);


    // --- Context Value exposed to consumers ---
    const value = {
        // Admin
        adminToken,
        adminUser,
        adminIsLoading,
        adminAuthError,
        loginAdmin,
        logoutAdmin,
        // User
        userToken,
        user,
        userIsLoading,
        userAuthError,
        userRegisterSuccess,
        loginUser,
        logoutUser,
        registerUser,
        // Global Error Dialog (provide state and close function if needed by consumers)
        globalErrorDialog,
        closeGlobalErrorDialog,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
            {/* Render the ErrorDialog conditionally based on its state */}
            {globalErrorDialog.show && (
                <ErrorDialog
                    title={globalErrorDialog.title}
                    message={globalErrorDialog.message}
                    onClose={closeGlobalErrorDialog} // Pass the closing function
                />
            )}
            {/* Render the SessionConflictDialog conditionally */}
            {sessionConflictDialog.show && (
                <SessionConflictDialog
                    onConfirm={handleSessionConflictConfirm}
                    onCancel={handleSessionConflictCancel}
                    isLoading={sessionConflictDialog.isLoading}
                />
            )}
        </AuthContext.Provider>
    );
};

// --- Custom Hook to use the context ---
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};