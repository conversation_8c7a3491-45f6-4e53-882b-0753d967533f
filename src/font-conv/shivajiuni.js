export default function Shivaji_to_Unicode(text, options = {}) {
  // Extracted and adapted from the provided Shivaji_to_Unicode function
  const array_one = [
    'iM', '|', 'K', '#', 'k', '@', 'G', 'g', '=', 'C', 'c', 'J', 'j', 'H',
    'z', 'T', 'Z', 'D', 'N', 'q', 't', '%', 'Q', 'd', 'n',
    'f', 'p', 'P', 'B', 'b', 'm', 'y', 'r', 'l', 'v', 'S', 'Y', 's', 'h',
    'p`', '`', '/', 'x', '~', '&', 'V', 'Ù', 'E', '(', 'w', 'É', '$', 'Ë', 'W', '<', '>', '_',
    'AaO', 'AÝ', 'Aao', 'Aa', 'A', 'eo', 'e', 'š', '[', ']', '?', '¸', '.', ' aa', ' a',
    '\\', 'Ý', 'aO', 'O', 'I', 'U', 'u', 'R', 'ृ', 'ao', 'o', 'a', '^M', 'M', 'Á',
    '³', '´', '््',
    // Added based on inspection of array_two - needs verification for Shivaji font
    "ळ्", "ऊ", "ॠ", "्a" // Assuming these map from Shivaji to Unicode
  ];

  const array_two = [
    'Mi', 'ऽ', 'ख', 'ख्', 'क', 'क्', 'घ्', 'ग्', 'ङ', 'छ', 'च्', 'झ्', 'ज्', 'ञ्',
    'ठ', 'ट', 'ढ', 'ड', 'ण्', 'थ्', 'त', 'त्', 'ध्', 'द', 'न्',
    'फ', 'प', 'प्', 'भ्', 'ब्', 'म्', 'य्', 'र', 'ल्', 'व्', 'श्', 'ष्', 'स्', 'ह',
    'प्र्', '्र', '्र', 'क्ष्', 'त्र', 'ज्ञ', 'द्य', 'न्न', 'श्र्', 'ह्म', 'द्ध', 'रु', 'रू', 'क्र', 'द्व', 'त्त्', 'क्त', 'द्द',
    'औ', 'औ', 'ओ', 'आ', 'अ', 'ऐ', 'ए', 'ई', 'इ', 'उ', 'ऋ', ',', '|', ' ||', ' |',
    '्', 'ौ', 'ौ', 'ै', 'ी', 'ू', 'ु', 'ॄ', 'ृ', 'ो', 'े', 'ा', 'ँ', 'ं', ':',
    '(', ')', '्',
    // Added based on inspection of array_one - needs verification for Shivaji font
    "ळ्", "ऊ", "ॠ", "" // Assuming these map from Shivaji to Unicode
  ];

  // Combine into the required mapping format
  const mapping = array_one.map((key, index) => [key, array_two[index]]);

  // Add the specific nukta mappings from the original file if they are correct
  const nuktaMappings = [
      ["Ö", "अ़"], ["Ù", "आ़"], ["Ú", "इ़"], ["Û", "ई़"], ["Ü", "उ़"],
      ["Ý", "ऊ़"], ["Þ", "ऋ़"], ["ß", "ॠ़"], ["à", "ऌ़"], ["á", "ॡ़"],
      ["ã", "ए़"], ["ä", "ऐ़"], ["å", "ऍ़"], ["æ", "ऑ़"], ["ç", "ओ़"],
      ["è", "औ़"], ["é", "क़"], ["ê", "ख़"], ["ë", "ग़"], ["ì", "ज़"],
      ["í", "ड़"], ["î", "ढ़"], ["ï", "फ़"], ["ð", "य़"], ["ñ", "ऱ"],
      ["ò", "ऩ"], ["ó", "ऴ"], ["ô", "क़"], ["õ", "ख़"], ["ö", "ग़"],
      ["÷", "ज़"], ["ø", "ड़"], ["ù", "ढ़"], ["ú", "फ़"], ["û", "य़"],
      ["ü", "त़"], ["ý", "थ़"], ["þ", "ध़"], ["ÿ", "ष़"], ["Ā", "श़"],
      ["ā", "ॸ"], ["Ă", "ॹ"], ["ă", "ॺ"], ["Ą", "ॻ"], ["ą", "ॼ"],
      ["Ć", "ॾ"], ["ć", "ॿ"], ["Ç", "ऱ"], ["È", "ऩ"], ["É", "ऴ"]
  ];

  // Prepend nukta mappings to ensure they are processed first if they overlap
  const fullMapping = [...nuktaMappings, ...mapping];


  let result = text;

  // --- Apply Main Character Mappings ---
  fullMapping.forEach(([shivaji, unicode]) => {
    // Escape special regex characters in the Shivaji key
    const shivajiRegex = new RegExp(shivaji.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), "g");
    result = result.replace(shivajiRegex, unicode);
  });


  // --- Post-processing based on provided logic ---

  // 1. Correct 'i' matra position (Shivaji 'i' + consonant -> Unicode consonant + 'ि')
  let position_of_i = result.indexOf("i");
  while (position_of_i !== -1) {
    const char_next_to_i = result.charAt(position_of_i + 1);
    const char_to_be_replaced = "i" + char_next_to_i;
    // Ensure we don't replace if the next char is a space or end of string
    if (char_next_to_i && char_next_to_i !== ' ') {
        result = result.replace(char_to_be_replaced, char_next_to_i + "ि");
    }
    // Find the next 'i' after the current position + 1 to avoid infinite loops on "ii"
    position_of_i = result.indexOf("i", position_of_i + 1);
  }

  // 2. Correct ' ि्' (wrong ee on half-letters)
  let position_of_wrong_ee = result.indexOf("ि्");
  while (position_of_wrong_ee !== -1) {
    const consonent_next_to_wrong_ee = result.charAt(position_of_wrong_ee + 2);
    const char_to_be_replaced = "ि्" + consonent_next_to_wrong_ee;
    // Ensure we have a consonant following
     if (consonent_next_to_wrong_ee && consonent_next_to_wrong_ee !== ' ') {
        result = result.replace(char_to_be_replaced, "्" + consonent_next_to_wrong_ee + "ि");
     }
    // Find the next 'ि्' after the current position + 1
    position_of_wrong_ee = result.indexOf("ि्", position_of_wrong_ee + 1);
  }


  // 3. Handle Reph (Shivaji '-' -> Unicode 'र्')
  // This logic assumes '-' indicates reph and needs to be moved before the base consonant, skipping matras.
  const set_of_matras = "ािीुूृेैोौंःँॅ"; // Includes anusvara/visarga/chandrabindu for safety
  let position_of_reph = result.indexOf("-");
  while (position_of_reph > 0) { // Check > 0 to ensure there's a char before it
      let probable_position_of_half_r = position_of_reph - 1;
      let char_at_probable_position = result.charAt(probable_position_of_half_r);

      // Find the base consonant by skipping backwards over matras
      while (set_of_matras.includes(char_at_probable_position) && probable_position_of_half_r > 0) {
          probable_position_of_half_r--;
          char_at_probable_position = result.charAt(probable_position_of_half_r);
      }

      // Ensure we didn't skip all the way to the beginning or hit another reph marker
       if (probable_position_of_half_r >= 0 && result.charAt(probable_position_of_half_r) !== '-') {
            const string_to_replace = result.substring(probable_position_of_half_r, position_of_reph + 1); // Include '-'
            const replacement_string = "र्" + result.substring(probable_position_of_half_r, position_of_reph); // Prepend 'र्'

            // Use replace with a check to avoid infinite loops if replacement doesn't remove '-'
            if (string_to_replace !== replacement_string) {
                 result = result.replace(string_to_replace, replacement_string);
            } else {
                 // Break if replacement didn't work to prevent infinite loop
                 break;
            }
       } else {
            // Cannot process this reph, maybe it's at the start or malformed. Skip it.
            // To prevent infinite loop if '-' is the first char or follows another '-', search after current position.
             position_of_reph = result.indexOf("-", position_of_reph + 1);
             continue; // Continue to next iteration of while loop
       }


      // Find the next reph marker
      position_of_reph = result.indexOf("-");
  }

// 4. Specific Conjunct Fixes (Add as needed)
  result = result.replace(/स््त्/g, "स्त"); // Fix for नमस्ते
  return result;
}