// src/font-conv/unicode_to_kruti.js

/**
 * Converts Unicode (Devanagari) text to Krutidev legacy font encoding.
 *
 * This function reverses the logic found in krutiuni.js.
 * It relies heavily on the parallel arrays mapping Unicode sequences to Krutidev sequences.
 * The order of replacements derived from the original krutiuni.js arrays is critical.
 */

// Arrays copied directly from krutiuni.js
var kruti_array = new Array(
    "ñ", "Q+Z", "sas", "aa", ")Z", "ZZ", "‘", "’", "“", "”", "å", "ƒ", "„", "…", "†", "‡", "ˆ", "‰", "Š", "‹",
    "¶+", "d+", "[+k", "[+", "x+", "T+", "t+", "M+", "<+", "Q+", ";+", "j+", "u+", "Ùk", "Ù", "ä", "–", "—",
    "é", "™", "=kk", "f=k", "à", "á", "â", "ã", "ºz", "º", "í", "{k", "{", "=", "«", "Nî", "Vî", "Bî", "Mî", "<î",
    "|", "K", "}", "J", "Vª", "Mª", "<ªª", "Nª", "Ø", "Ý", "nzZ", "æ", "ç", "Á", "xz", "#", ":", "v‚", "vks", "vkS",
    "vk", "v", "b±", "Ã", "bZ", "b", "m", "Å", ",s", ",", "_", "ô", "d", "Dk", "D", "[k", "[", "x", "Xk", "X",
    "Ä", "?k", "?", "³", "pkS", "p", "Pk", "P", "N", "t", "Tk", "T", ">", "÷", "¥", "ê", "ë", "V", "B", "ì", "ï",
    "M+", "<+", "M", "<", ".k", ".", "r", "Rk", "R", "Fk", "F", ")", "n", "/k", "èk", "/", "Ë", "è", "u", "Uk", "U",
    "i", "Ik", "I", "Q", "¶", "c", "Ck", "C", "Hk", "H", "e", "Ek", "E", ";", "¸", "j", "y", "Yk", "Y", "G", "o",
    "Ok", "O", "'k", "'", "\"k", "\"", "l", "Lk", "L", "g", "È", "z", "Ì", "Í", "Î", "Ï", "Ñ", "Ò", "Ó", "Ô", "Ö",
    "Ø", "Ù", "Ük", "Ü", "‚", "ks", "kS", "k", "h", "q", "w", "`", "s", "S", "a", "¡", "%", "W", "•", "·", "∙",
    "·", "~j", "~", "\\", "+", " ः", "^", "*", "Þ", "ß", "(", "¼", "½", "¿", "À", "¾", "A", "-", "&", "&", "Œ", "]",
    "~ ", "@"
);

var unicode_array = new Array(
    "॰", "QZ+", "sa", "a", "र्द्ध", "Z", "\"", "\"", "'", "'", "०", "१", "२", "३", "४", "५", "६", "७", "८", "९",
    "फ़्", "क़", "ख़", "ख़्", "ग़", "ज़्", "ज़", "ड़", "ढ़", "फ़", "य़", "ऱ", "ऩ", "त्त", "त्त्", "क्त", "दृ", "कृ", "न्न", "न्न्",
    "=k", "f=", "ह्न", "ह्य", "हृ", "ह्म", "ह्र", "ह्", "द्द", "क्ष", "क्ष्", "त्र", "त्र्", "छ्य", "ट्य", "ठ्य", "ड्य", "ढ्य",
    "द्य", "ज्ञ", "द्व", "श्र", "ट्र", "ड्र", "ढ्र", "छ्र", "क्र", "फ्र", "र्द्र", "द्र", "प्र", "प्र", "ग्र", "रु", "रू",
    "ऑ", "ओ", "औ", "आ", "अ", "ईं", "ई", "ई", "इ", "उ", "ऊ", "ऐ", "ए", "ऋ", "क्क", "क", "क", "क्", "ख", "ख्",
    "ग", "ग", "ग्", "घ", "घ", "घ्", "ङ", "चै", "च", "च", "च्", "छ", "ज", "ज", "ज्", "झ", "झ्", "ञ", "ट्ट", "ट्ठ",
    "ट", "ठ", "ड्ड", "ड्ढ", "ड़", "ढ़", "ड", "ढ", "ण", "ण्", "त", "त", "त्", "थ", "थ्", "द्ध", "द", "ध", "ध", "ध्",
    "ध्", "ध्", "न", "न", "न्", "प", "प", "प्", "फ", "फ्", "ब", "ब", "ब्", "भ", "भ्", "म", "म", "म्", "य", "य्",
    "र", "ल", "ल", "ल्", "ळ", "व", "व", "व्", "श", "श्", "ष", "ष्", "स", "स", "स्", "ह", "ीं", "्र", "द्द", "ट्ट",
    "ट्ठ", "ड्ड", "कृ", "भ", "्य", "ड्ढ", "झ्", "क्र", "त्त्", "श", "श्", "ॉ", "ो", "ौ", "ा", "ी", "ु", "ू", "ृ",
    "े", "ै", "ं", "ँ", "ः", "ॅ", "ऽ", "ऽ", "ऽ", "ऽ", "्र", "्", "?", "़", ":", "‘", "’", "“", "”", ";", "(",
    ")", "{", "}", "=", "।", ".", "-", "µ", "॰", ",", "् ", "/"
);


export function unicode_to_kruti(unicodeText) {
    if (!unicodeText) {
        return "";
    }

    let krutiText = unicodeText;
    const array_length = unicode_array.length; // Use length of unicode_array

    // --- Stage 1: Replace Unicode sequences with Krutidev sequences using the arrays ---
    // Iterate in the *same order* as krutiuni.js to prioritize longer sequences
    for (let i = 0; i < array_length; i++) {
        // Use a loop for replacement to handle multiple occurrences, similar to original logic
        // This is safer than RegExp if unicode_array[i] contains special regex characters
        let current_unicode_char = unicode_array[i];
        let current_kruti_char = kruti_array[i];

        // Skip empty strings in the unicode array if any exist, as they cause infinite loops
        if (current_unicode_char === "") continue;

        let idx = krutiText.indexOf(current_unicode_char);
        while (idx !== -1) {
             // Ensure we don't accidentally replace parts of already converted sequences
             // (This check might be overly cautious but safer)
            // Example: Avoid replacing 'k' inside '=kk' after '=kk' was converted from '=k'
             // This check is hard to implement perfectly here. Relying on order is key.

            krutiText = krutiText.substring(0, idx) + current_kruti_char + krutiText.substring(idx + current_unicode_char.length);
            idx = krutiText.indexOf(current_unicode_char, idx + current_kruti_char.length); // Start search after the replacement
        }

        // Alternative using RegExp (potentially faster but needs careful escaping):
        /*
        try {
            // Escape special regex characters in the search string
            const escapedUnicode = current_unicode_char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(escapedUnicode, 'g');
            krutiText = krutiText.replace(regex, current_kruti_char);
        } catch (e) {
            console.error(`Error creating/using RegExp for: ${current_unicode_char}`, e);
            // Fallback or log error - the loop above is a safer fallback
        }
        */
    }

    // --- Stage 2: Adjustments for 'f' (Short i-matra) ---
    // The array handles many cases like "कि" -> "fd", but we need a rule
    // for consonants possibly combined with halant or other signs followed by 'ि'.
    // In Krutidev, 'f' comes *before* the consonant.
    // Example: Unicode "कि" should become Kruti "fd". Unicode "क्लि" should become "fDy".
    // This regex tries to find a consonant (including half forms represented by halant)
    // followed by 'ि' and moves the 'ि' to the front as 'f'.

    // Define consonants for the regex
    // (Including base consonants क-ह and common conjuncts like क्ष, त्र, ज्ञ that might act as a base)
    const consonants = "कखगघङचछजझञटठडढणतथदधनपफबभमयरलवशषसहक्षज्ञत्र"; // Basic consonants
    // Match a consonant possibly followed by halant (्), then the short i matra (ि)
    // This needs careful testing, especially with complex conjuncts.
    // A simpler approach first: Match *any* character from the consonant list followed by ि
    krutiText = krutiText.replace(new RegExp(`([${consonants}])ि`, 'g'), 'f$1');

    // Handle cases like क् + ि = कि (fD) which might be missed if क् was already converted.
    // This might require more complex lookbehind/lookahead or further analysis of krutiuni's exact halant handling.
    // For now, the above rule covers the most common cases.

    // --- Stage 3: Adjustments for Reph (Z) ---
    // The original code moved 'Z' from after the character/cluster to 'र्' before it.
    // The reverse needs to find 'र्' and potentially move it after the character/cluster as 'Z'.
    // However, the arrays already map many 'र्' combinations (like क्र, प्र, ट्र, र्द, र्द्र).
    // Let's assume the arrays cover the majority of Reph cases.
    // We only need to handle the specific cases reversed from the original special handling if any.

    // Reverse specific replacements from krutiuni's special handling:
    // Original: modified_substring = modified_substring.replace( /Æ/g , "र्f" ) ;
    // Reverse (Problematic - 'र्f' might be generated by Stage 2): krutiText = krutiText.replace( /र्f/g , "Æ" ) ; -- OMITTING this as it's likely incorrect

    // Original: modified_substring = modified_substring.replace( /Ê/g , "ीZ" ) ;
    // Reverse: Find 'ी' followed by 'Z' and replace with 'Ê'
    krutiText = krutiText.replace(/ीZ/g, 'Ê');

    // What about other Matras + Z? The original code had logic to find Z, step back over matras,
    // and place 'र्' before the base. Reversing this precisely is complex.
    // Let's rely on the array mappings and the specific 'ीZ' -> 'Ê' reversal for now.


    // --- Stage 4: Final simple replacements (if any) ---
    // Original: modified_substring = modified_substring.replace( /±/g , "Zं" ) ;
    // Reverse:
    krutiText = krutiText.replace(/Zं/g, '±');

    // Reverse other potential simple replacements if they were present and confirmed necessary.


    // Return the converted text
    return krutiText;
}

// Note: This reverse conversion might not be 100% perfect, especially with
// complex conjuncts or rare Reph combinations that weren't explicitly
// covered in the original arrays or special handling. Thorough testing is recommended.