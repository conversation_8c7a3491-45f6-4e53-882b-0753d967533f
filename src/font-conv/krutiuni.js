var kruti_array = new Array("ñ","Q+Z","sas","aa",")Z","ZZ","‘","’","“","”","å",  "ƒ",  "„",   "…",   "†",   "‡",   "ˆ",   "‰",   "Š",   "‹", "¶+",   "d+", "[+k","[+", "x+",  "T+",  "t+", "M+", "<+", "Q+", ";+", "j+", "u+","Ùk", "Ù", "ä", "–", "—","é","™","=kk","f=k",  "à",   "á",    "â",   "ã",   "ºz",  "º",   "í", "{k", "{", "=",  "«", "Nî",   "Vî",    "Bî",   "Mî",   "<î", "|", "K", "}","J",   "Vª",   "Mª",  "<ªª",  "Nª",   "Ø",  "Ý", "nzZ",  "æ", "ç", "Á", "xz", "#", ":", "v‚","vks",  "vkS",  "vk",    "v",  "b±", "Ã",  "bZ",  "b",  "m",  "Å",  ",s",  ",",   "_","ô",  "d", "Dk", "D", "[k", "[", "x","Xk", "X", "Ä", "?k", "?",   "³", "pkS",  "p", "Pk", "P",  "N",  "t", "Tk", "T",  ">", "÷", "¥","ê",  "ë",   "V",  "B",   "ì",   "ï", "M+", "<+", "M",  "<", ".k", ".", "r",  "Rk", "R",   "Fk", "F",  ")", "n", "/k", "èk",  "/", "Ë", "è", "u", "Uk", "U",   "i",  "Ik", "I",   "Q",    "¶",  "c", "Ck",  "C",  "Hk",  "H", "e", "Ek",  "E", ";",  "¸",   "j",    "y", "Yk",  "Y",  "G",  "o", "Ok", "O", "'k", "'",   "\"k",  "\"",  "l", "Lk",  "L",   "g",  "È", "z", "Ì", "Í", "Î",  "Ï",  "Ñ",  "Ò",  "Ó",  "Ô",   "Ö",  "Ø",  "Ù","Ük", "Ü","‚",    "ks",   "kS",   "k",  "h",    "q",   "w",   "`",    "s",    "S", "a",    "¡",    "%",     "W",  "•", "·", "∙", "·", "~j",  "~", "\\","+"," ः","^", "*",  "Þ", "ß", "(", "¼", "½", "¿", "À", "¾", "A", "-", "&", "&", "Œ", "]","~ ","@");

var unicode_array = new Array("॰","QZ+","sa","a","र्द्ध","Z","\"","\"","'","'", "०",  "१",  "२",  "३",     "४",   "५",  "६",   "७",   "८",   "९",  "फ़्",  "क़",  "ख़", "ख़्",  "ग़", "ज़्", "ज़",  "ड़",  "ढ़",   "फ़",  "य़",  "ऱ",  "ऩ",  "त्त", "त्त्", "क्त",  "दृ",  "कृ","न्न","न्न्","=k","f=", "ह्न",  "ह्य",  "हृ",  "ह्म",  "ह्र",  "ह्",   "द्द",  "क्ष", "क्ष्", "त्र", "त्र्",  "छ्य",  "ट्य",  "ठ्य",  "ड्य",  "ढ्य", "द्य", "ज्ञ", "द्व", "श्र",  "ट्र",    "ड्र",    "ढ्र",    "छ्र",   "क्र",  "फ्र", "र्द्र",  "द्र",   "प्र", "प्र",  "ग्र", "रु",  "रू", "ऑ",   "ओ",  "औ",  "आ",   "अ", "ईं", "ई",  "ई",   "इ",  "उ",   "ऊ",  "ऐ",  "ए", "ऋ", "क्क", "क", "क", "क्", "ख", "ख्", "ग", "ग", "ग्", "घ", "घ", "घ्", "ङ", "चै",  "च", "च", "च्", "छ", "ज", "ज", "ज्",  "झ",  "झ्", "ञ", "ट्ट",   "ट्ठ",   "ट",   "ठ",   "ड्ड",   "ड्ढ",  "ड़", "ढ़", "ड",   "ढ", "ण", "ण्", "त", "त", "त्", "थ", "थ्",  "द्ध",  "द", "ध", "ध", "ध्", "ध्", "ध्", "न", "न", "न्",    "प", "प", "प्",  "फ", "फ्",  "ब", "ब", "ब्",  "भ", "भ्",  "म",  "म", "म्", "य", "य्",  "र", "ल", "ल", "ल्",  "ळ",  "व", "व", "व्", "श", "श्",  "ष", "ष्", "स", "स", "स्", "ह", "ीं", "्र", "द्द", "ट्ट","ट्ठ","ड्ड","कृ","भ","्य","ड्ढ","झ्","क्र","त्त्","श","श्","ॉ",  "ो",   "ौ",   "ा",   "ी",   "ु",   "ू",   "ृ",   "े",   "ै", "ं",   "ँ",   "ः",   "ॅ",  "ऽ", "ऽ", "ऽ", "ऽ", "्र",  "्", "?", "़",":", "‘",   "’",   "“",   "”",  ";",  "(",    ")",   "{",    "}",   "=", "।", ".", "-",  "µ", "॰", ",","् ","/");
function krutiunicode(inputText) {
    var text_size = inputText.length;
    var kruti_array_length = kruti_array.length ;
    var kruti_text = inputText;

    var processed_text = '' ;

    var n = 0 ;  var o = 0 ;  var r = 1 ;

    var max_text_size = 7000;

    while ( r == 1 )
    {
        n = o ;

        if ( o < ( text_size - max_text_size ) )
        {
            o +=  max_text_size ;
            while (inputText.charAt ( o ) != ' ') {o--;}
        }
        else  { o = text_size  ;  r = 0 }

        var kruti_text_part = inputText.substring ( n, o )  ;

        kruti_text_part = replsym(kruti_text_part) ;

        processed_text += kruti_text_part ;
    }

    return processed_text;

    function replsym(input_substring) {
        var modified_substring = input_substring;
        var input_symbol_idx;
        var idx;
        var pi;
        var cni;
        var ctbr;
        var cntip2;
        var powe;
        var cntwe;
        var matraslist;
        var rpos;
        var pphr;
        var chtr;
        var rstr;


        if ( modified_substring !== "" ) {
            for ( input_symbol_idx = 0;   input_symbol_idx < kruti_array_length;    input_symbol_idx++ ) {
                idx = 0  ;
                while (idx !== -1 ) {
                    modified_substring = modified_substring.replace( kruti_array[ input_symbol_idx ] , unicode_array[input_symbol_idx] );
                    idx = modified_substring.indexOf( kruti_array[input_symbol_idx] );
                }
            }
        }

        modified_substring = modified_substring.replace( /±/g , "Zं" ) ;
        modified_substring = modified_substring.replace( /Æ/g , "र्f" ) ;

        pi = modified_substring.indexOf( "f" );
        while ( pi !== -1 ) {
            cni = modified_substring.charAt( pi + 1 );
            ctbr = "f" + cni;
            modified_substring = modified_substring.replace( ctbr , cni + "ि" );
            pi = modified_substring.search( /f/ , pi + 1 );
        }

        modified_substring = modified_substring.replace( /Ç/g , "fa" ) ;
        modified_substring = modified_substring.replace( /É/g , "र्fa" ) ;

        pi = modified_substring.indexOf( "fa" );
        while ( pi !== -1 ) {
            cntip2 = modified_substring.charAt( pi + 2 );
            ctbr = "fa" + cntip2;
            modified_substring = modified_substring.replace( ctbr , cntip2 + "िं" );
            pi = modified_substring.search( /fa/ , pi + 2 );
        }

        modified_substring = modified_substring.replace( /Ê/g , "ीZ" ) ;

        powe = modified_substring.indexOf( "ि्" );
        while ( powe !== -1 ) {
            cntwe = modified_substring.charAt( powe + 2 );
            ctbr = "ि्" + cntwe;
            modified_substring = modified_substring.replace( ctbr , "्" + cntwe + "ि" );
            powe = modified_substring.search( /ि्/ , powe + 2 );
        }

        matraslist = "अ आ इ ई उ ऊ ए ऐ ओ औ ा ि ी ु ू ृ े ै ो ौ ं : ँ ॅ";
        rpos = modified_substring.indexOf( "Z" );
        while ( rpos > 0 ) {
            pphr = rpos - 1 ;
            chtr = modified_substring.charAt( pphr );

            while ( matraslist.match( chtr ) !== null ) {
                pphr = pphr - 1 ;
                chtr = modified_substring.charAt( pphr ) ;
            }

            ctbr = modified_substring.substr ( pphr , ( rpos - pphr ) ) ;
            rstr = "र्" + ctbr ;
            ctbr = ctbr + "Z" ;
            modified_substring = modified_substring.replace( ctbr , rstr ) ;
            rpos = modified_substring.indexOf( "Z" ) ;
        }
        return modified_substring;
    }
}

export default krutiunicode;
