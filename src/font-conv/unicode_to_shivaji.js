// src/font-conv/unicode_to_shivaji.js

/**
 * Converts Unicode (Devanagari) text to Shivaji legacy font encoding.
 * Uses the accurate mapping provided by the user.
 */

// Accurate mapping for Unicode to Shivaji 02
const unicodeToShivajiMap = {
    // Independent Vowels
    'अ': 'A', 'आ': 'Aa', 'इ': '[', 'ई': '[-', 'उ': ']', 'ऊ': '}',
    'ऋ': '?', 'ए': 'e', 'ऐ': 'eo', 'ओ': 'Aao', 'औ': 'AaO', 'ऑ': 'Aa^', 'ॲ': 'A^',
    
    // Consonants (full form)
    'क': 'k', 'क्': '@', 'ख': 'K', 'ख्': '#', 'ग': 'ga', 'ग्': 'g',
    'घ': 'Ga', 'घ्': 'G', 'ङ': '=', 'ङ्': '=\\', 'च': 'ca', 'च्': 'c',
    'छ': 'C', 'ज': 'ja', 'ज्': 'j', 'ञ': 'Ha', 'ञ्': 'H', 'ट': 'T', 'ड': 'D',
    'त': 't', 'त्': '%', 'थ': 'qa', 'थ्': 'q', 'द': 'd', 'द्': 'd\\',
    'ध': 'Qa', 'ध्': 'Q', 'न': 'na', 'न्': 'n', 'प': 'p', 'प्': 'P',
    'फ': 'f', 'फ्': 'F', 'ब': 'ba', 'ब्': 'b', 'भ': 'Ba', 'भ्': 'B', 'म': 'ma', 'म्': 'm',
    'य': 'ya', 'य्': 'y', 'र': 'r', 'र्': '-', 'ल': 'la', 'ल्': 'l',
    'व': 'va', 'व्': 'v', 'श': 'Xa', 'श्': 'X', 'ष': 'Ya', 'ष्': 'Y', 'स': 'sa',
    'स्': 's', 'ह': 'h', 'ण': 'Na', 'ण्': 'N', 'ळ': 'L', 'झ': 'Ja', 'झ्': 'J',
    'ठ': 'z', 'ढ': 'Z', 'ज्ञ': '&a', 'ज्ञ्': '&', 'रु': 'à', 'रू': '$',
    
    // Conjuncts and Ligatures
    'क्ष': 'xa', 'क्ष्': 'x', 'त्न': '%na', 'त्य': '%ya', 'द्‌गु': 'd\\gu',
    'ह्य': '(', 'र्ऌ': ';', 'श्व': 'Xva', 'प्र': 'p`',
    'र्म': 'ma-', 'र्य': 'ya-', 'श्ल': 'Sla', 'त्र': '~a', 'त्र्': '~', 'हृ': ')', 'रॉ': 'ra^', 'ग्र': 'ga`', 'लॉ': 'laa^', 'अंबि': 'AMiba', 'ब्र': 'ba`', 'ह्मा': 'mha', 'र्या': 'yaa-',
    'द्ध': 'w', 'क्र': 'k`', 'श्र': 'Ea', 'द्व': 'W', 'स्मि': 'isma', 'द्रु': 'd`u', 'श्चं': 'ôcaM', 'प्नि': 'iPna', 'र्वा': 'vaa-', 'द्वि': 'iW', 'ट्यू': 'TêU', 'द्र': 'd`',
    'र्गी': 'gaI-', 'र्ष': 'Ya-', 'र्त': 't-', 'र्च': 'ca-', 'र्प': 'p-', 'सर्वांनी': 'savaa-MnaI', 'द्य': 'V', 'ख्रि': 'i#a`', 'घ्र': 'Ga`', 'ज्र': 'ja`', 'ष्ट्र': 'YT/', 'ड्र': 'D/',
    'र्धा': 'Qaa-', 'र्दी': 'dI-', 'र्ती': 'tI-', 'र्थ': 'qa-', 'र्श': 'Xa-', 'ट्र': 'T/', 'ध्र': 'Qa`', 'न्र': 'na`', 'फ्र': 'f`', 'फ्रॉ': 'f`a^', 'ह्म': 'mh', 'र्चा': 'caa-', 'म्र': 'ma`',
    'र्मी': 'maI-', 'र्भ': 'Ba-', 'र्ज': 'ja-', 'र्ध्य': 'Qya-', 'र्ल': 'la-', 'र्ड्': 'D-\\', 'र्ड': 'D-', '+': '¤', 'ट्ट': '+', '=': 'Û', 'ड्ड': 'Ô', 'द्द': '_', 'ख्र': '#a`', 'ढ्ढ': 'Õ',
    'र्मा': 'maa-', 'र्ण': 'Na-', 'ब्द': 'bd', 'द्म': 'ï', 'भ्य': 'Bya', 'र्ट': 'T-', 'र्द': 'd-', 'र्न': 'na-', 'र्फ': 'f-', 'र्ब': 'ba-', 'र्ह': 'h-', 'द्धि': 'iw', 'क्रि': 'ik`',
    'र्स': 'sa-', 'र्ग': 'ga-', 'र्फी': 'fI-', 'र्घ': 'Ga-', 'र्षा': 'Yaa-', 'कॉ': 'ka^', 'चॉ': 'caa^', 'स्ट्रॉ': 'sT/a^', 'सँ': 'saç', 'न्सि': 'insa', 'स्कि': 'isk', 'स्थि': 'isqa',
    'र्ध': 'Qa-', 'र्थि': 'iqa-', 'र्दो': 'da-', 'र्व': 'va-', 'र्ता': 'ta-', 'डॉ': 'Da^', 'दुःख': 'du:K', 'र्थी': 'qaI-', 'र्नि': 'ina-', 'ष्ट्यां': 'YTêaM', 'त्वि': 'i%va', 'त्यि': 'i%ya',
    'र्वि': 'iva-', 'र्क': 'k-', 'र्वी': 'vaI-', 'र्व्ह': 'vho-', 'र्ने': 'nao-', 'टॉ': 'Ta^', 'ठॉ': 'za^', 'गॉ': 'gaa^', 'ब्दि': 'ibd', 'र्थां': 'qaa-M', 'र्गि': 'iga-',
    'पर्दि': 'pid-', 'ञ्जि': 'iHja', 'र्षि': 'iYa-', 'प्ति': 'iPt', 'ड़': 'DM', 'हॉ': 'ha^', 'स्र': 'sa`', 'स्त्रि': 'is~a', 'स्वतः': 'svat:',
    'आँ': 'Aaç', 'इँ': '[ç', 'कँ': 'kç', 'गँ': 'gaç', 'चँ': 'caç', 'जँ': 'jaç', 'पँ': 'pç', 'बँ': 'baç', 'मँ': 'maç', 'ग्नि': 'igna',
    'अः': 'A:', 'इः': '[:', 'उः': ']:', 'कः': 'k:', 'तपः': 'tp:', 'पुनः': 'puna:', 'प्रातः': 'p`at:', 'ॐ': '!', 'अँ': 'Aç', 'स्ट्रि': 'isT/', 'ऍ': 'e^',
    'प्रि': 'ip`', 'स्पि': 'isp', 'श्चि': 'iXca', 'र्वां': 'vaa-M', 'र्णा': 'Naa-', 'र्वो': 'vaa-o', 'सद्‌गुरु': 'sad\\gauà', 'र्श्व': 'Xva-', 'र्टी': 'TI-', 'ल्लि': 'illa',
    '|': 'ß', 'ग्लि': 'igla', 'र्व्हि': 'ivh', 'र्दा': 'da-', 'ह्रि': 'i)', 'ब्रि': 'iba`', 'बि': 'iba', 'र्शि': 'iXa-', 'श्रि': 'iEa', 'ल्डिं': 'ilDM', 'व्र': 'va`',
    'ळ्या': 'Lêa', 'न्त्रि': 'in~a', 'र्ति': 'it-', 'र्णि': 'iNa-', 'र्गो': 'gaa-', 'र्णी': 'NaI-', 'स्वि': 'isva', 'र्गा': 'gaa-', 'श्‍व': 'Sva',
    
    // Consonant + Matra Combinations
    'कि': 'ik', 'खि': 'iK', 'गि': 'iga', 'घि': 'iGa', 'चि': 'ica',
    'छि': 'iC', 'जि': 'ija', 'झि': 'iJa', 'टि': 'iT', 'ठि': 'iz',
    'डि': 'iD', 'ढि': 'iZ', 'णि': 'iNa', 'ति': 'it', 'थि': 'iqa',
    'दि': 'id', 'धि': 'iQa', 'नि': 'ina', 'पि': 'ip', 'फि': 'if',
    'बि': 'iba', 'भि': 'iBa', 'मि': 'ima', 'यि': 'iya', 'रि': 'ir',
    'लि': 'ila', 'वि': 'iva', 'शि': 'iXa', 'षि': 'iYa', 'सि': 'isa',
    'हि': 'ih', 'न्दि': 'ind', 'क्ति': 'i@t', 'मर्दि': 'maid-',
    'मर्पि': 'maip-', 'म्बि': 'imba', 'त्रि': 'i~a', 'ल्बि': 'ilba', 'ठ्ठ्या': '{êa', 'ऱ्या': ';yaa',
    'ण्डि': 'iND', 'त्मि': 'i%ma', 'व्हि': 'ivh', 'क्षि': 'ixa',
    'स्रो': 'sa`ao', 'शु': 'Xau', 'कु': 'ku', 'मु': 'mau', 'टु': 'Tu', 'त्ता': '<aa', 'ठ्ठ': '{', 'श्वि': 'iÇ',
    'ंब': 'Mba', 'शी': 'XaI',
    
    // Special Sequences
    '्र': '`', 'श्री': 'EaI', 'सौ': 'saaO', 'गणेश': 'gaNaoXa', 'नमः': 'nama:',
    'प्रिया': 'ip`yaa', 'सूर्य': 'saUya-', 'कर्म': 'kma-', 'गर्व': 'gavaa-',
    'सद्‌गुरू': 'sad\\gu$', 'दुर्गा': 'dugaa-', 'दुर्गे': 'duga-o',
    'वर्ण': 'vaNa-', 'वर्णन': 'vaNa-na', 'पूर्ण': 'pUNa-', 'शीर्वाद': 'XaIvaa-d',
    'हर्ष': 'hYa-', 'ऊर्जा': '}jaa-', 'सामर्थ्य': 'saamaqya-',
    'आशीर्वाद': 'AaXaIvaa-d', 'पूर्ती': 'pUtI-', 'चिमु': 'icamau',
    'सर्व': 'sava-', 'गर्वा': 'gavaa-', 'अथर्व': 'Aqava-', 'पार्थ': 'paqa-',
    'सुदर्शन': 'saudXa-na', 'अर्णव': 'ANa-va', 'ध्रुव': 'Qa`uva',
    'चिन्मय': 'icanmaya', 'दर्शन': 'dXa-na', 'सिद्धार्थ': 'isawaqa-',
    'अर्जुन': 'Ajau-na', 'आर्या': 'Aayaa-', 'उर्वी': ']vaI-',
    'ऐश्वर्या': 'eoXvayaa-', 'पूर्वा': 'pUvaa-', 'रश्मी': 'rXmaI',
    'चंद्र': 'caMd`', 'शार्दुल': 'Xaadu-la',
    'सर्वांचा': 'savaa-Mcaa', 'सुवर्णा': 'sauvaNaa-', 'सर्वोदय': 'savaa-odya', 'सर्व्हिस': 'saivh-sa', 'कुऱ्हाडे': 'ku;haDo',
    
    // Vowel Signs (Matras)
    'ा': 'aa', 'ी': 'I', 'ि': 'i', 'े': 'o', 'ै': 'O', 'ृ': 'R',
    'ु': 'u', 'ू': 'U', 'ो': 'ao', 'ौ': 'aO', 'ॅ': '^', 'ँ': 'ç', 'ॉ': 'a^', 'ं': 'M', 'ः': ':', ' ॕ': '^',
    
    // Other Signs
    '्': '\\', '‌': '\\',
    
    // Punctuation
    '।': '.', '–': '-', ',': ',', ';': '¸', '(': '³', ')': '´', '-': '¯',
    '/': 'À', '!': 'è',
    
    // Digits
    '०': '0', '१': '1', '२': '2', '३': '3', '४': '4',
    '५': '5', '६': '6', '७': '7', '८': '8', '९': '9',
    
    // Space
    ' ': ' '
};

// Create reverse mapping for Shivaji to Unicode
const shivajiToUnicodeMap = Object.fromEntries(
    Object.entries(unicodeToShivajiMap).map(([k, v]) => [v, k])
);

export function unicode_to_shivaji(unicodeText) {
    if (!unicodeText) return "";
    
    let shivajiOutput = '';
    let i = 0;
    
    while (i < unicodeText.length) {
        // Check for multi-character Unicode sequences (longest first)
        let matched = false;
        const sortedKeys = Object.keys(unicodeToShivajiMap).sort((a, b) => b.length - a.length);
        
        for (let key of sortedKeys) {
            if (unicodeText.startsWith(key, i)) {
                shivajiOutput += unicodeToShivajiMap[key];
                i += key.length;
                matched = true;
                break;
            }
        }
        
        if (matched) continue;
        
        // Check for half consonants
        if (['स', 'च', 'क', 'य', 'थ', 'ग', 'प', 'न', 'ण', 'ल', 'त', 'द', 'ख', 'ज्ञ', 'ध', 'फ', 'ष', 'व', 'म', 'ङ', 'ञ', 'ज', 'ब', 'त्र', 'श', 'भ', 'झ'].includes(unicodeText[i]) && 
            i + 1 < unicodeText.length && unicodeText[i + 1] === '्') {
            const halfConsonant = unicodeText[i] + '्';
            shivajiOutput += unicodeToShivajiMap[halfConsonant] || halfConsonant;
            i += 2;
            continue;
        }
        
        // Fallback to single character
        shivajiOutput += unicodeToShivajiMap[unicodeText[i]] || unicodeText[i];
        i++;
    }
    
    return shivajiOutput;
}

export function shivaji_to_unicode(shivajiText) {
    if (!shivajiText) return "";
    
    let unicodeOutput = '';
    let i = 0;
    
    while (i < shivajiText.length) {
        // Check for multi-character Shivaji sequences (longest first)
        let matched = false;
        const sortedKeys = Object.keys(shivajiToUnicodeMap).sort((a, b) => b.length - a.length);
        
        for (let key of sortedKeys) {
            if (shivajiText.startsWith(key, i)) {
                unicodeOutput += shivajiToUnicodeMap[key];
                i += key.length;
                matched = true;
                break;
            }
        }
        
        if (!matched) {
            unicodeOutput += shivajiToUnicodeMap[shivajiText[i]] || shivajiText[i];
            i++;
        }
    }
    
    return unicodeOutput;
}

// Default export for backward compatibility
export default shivaji_to_unicode;