// src/font-conv/unicode_to_shree.js

/**
 * Converts Unicode (Devanagari) text to ShreeLipi (Shree-Dev-0708 or similar) legacy font encoding.
 *
 * This function reverses the logic found in ShreeLipi_to_Unicode.js.
 * It relies heavily on the parallel arrays mapping Unicode sequences to ShreeLipi sequences.
 * The order of replacements derived from the original arrays is critical.
 */

// Arrays copied directly from ShreeLipi_to_Unicode.js
var sarray_one = new Array(
    '~' ,  'u' ,  'ª' ,  '}' ,   'n' , 'p' ,
    'H' ,  '·' ,   'I' ,  '»' ,  'J' ,  '½' ,  'K' ,
    'M' ,  'À' ,  'N' ,   'µO' ,  'O' ,   'µÁ' ,  'Á' ,  'P' ,
    'Q' ,  'R' ,   '¶S' ,  'S' ,   '¶T' ,   'T' ,   'U' ,  'Ê' ,
    'W' , 'Ï' ,  'V' ,  'Ë' ,  'Y',  'Ü' ,  'X' ,  'Z' ,  'Ý' ,
    '\\' ,  'â' ,  '[' ,  'ß' ,  '^' ,   'ä' ,  ']' ,   'ã' ,   '_'  ,  'å' ,
    '`' ,    'a' ,    'c' ,    'ë',    'd' ,     'ì',
    'e' ,  'û' ,  'í' ,  'f' ,  'î' ,  'g' ,  'ñ' ,  'h' ,  'j' ,  'ú' ,  'k' ,
    'Ô' ,  'Û' ,  'Ú' ,  'à' ,  'Þ' , 'Q­' ,  'º$' ,  'Ì' ,  'Ð' ,  'Õ' ,  'l' ,  'Îm' ,   '¸' ,   '„' ,  'ˆ' ,  'œ' ,  'Å' ,  'È' ,
    'Am¡' ,  'Am{' ,  'Am|' ,  'Am' ,  'A' ,  'B©' ,  'B' , 'C' , 'D',   'E{',  'E' ,  'F' ,
    '§m' ,   '§w' ,   '§p' ,    '§y' ,    '±m' ,   '±w' ,   '±p' ,   '±y' ,    // for changing the order of anuswaar & chandrabindu with other maatraas
    'm°' ,    'm{' ,    'm|' ,    '{' ,   '|' ,  'm¡' ,   'm¢' ,   '¡' ,   '¢' ,
    'm' ,    'r' ,   's' ,  't' ,    'w' ,   'þ' ,  'y' ,   '§' ,  '¨' ,   '±',   '•' ,  '¥' ,  '²' ,
    'Ñ' ,  '«' ,  'é' ,  'ê' ,  '&' ,  '$' ,  '>' ,  'µ'  ,  '°' ,  '\¶'
);

var sarray_two = new Array(
    "ॐ", "ऽ", "©ं", "{©", "o", "o", // Note: n and p both map to 'o' in original, reverse needs care
    "क", "क्", "ख", "ख्", "ग", "ग्", "घ",
    "च", "च्", "छ", "ज़", "ज", "ज़्‌", "ज्", "झ",
    "ट", "ठ", "ड़", "ड", "ढ़", "ढ", "ण", "ण्",
    "थ", "थ्", "त", "त्", "ध", "ध्", "द", "न", "न्",
    "फ", "फ्", "प", "प्", "भ", "भ्", "ब", "ब्", "म", "म्",
    "य", "र", "ल", "ल्", "व", "व्", "श", "श्", "श्", // Note: multiple mappings to श्
    "ष", "ष्", "स", "स्", "ह", "क्ष", "क्ष्", "ज्ञ",
    "द्द", "द्व", "द्य", "प्र", "न्न", "ट्र", "क्त", "त्र", "द्र", "द्ध", "श्र", "त्त", "क्क", "ल्ल", "ह्व", "श्व", "ट्ट", "ड्ड",
    "औ", "ओ", "ओं", "आ", "अ", "ई", "इ", "उ", "ऊ", "ऐ", "ए", "ऋ",
    "ां", "ुं", "ुं", "ूँ", "ाँ", "ुँ", "ुँ", "ूँ", // Anusvara/Chandrabindu mappings
    "ॉ", "ो", "ों", "े", "ें", "ौ", "ौं", "ै", "ैं",
    "ा", "ी", "ी", "ीं", "ु", "ु", "ू", "ं", "ं", "ँ", "ः", "ृ", "्",
    "दृ", "्र", "रु", "रू", "।",
    "", "", "", "", "" // Placeholders at the end
);

export function unicode_to_shree(unicodeText) {
    if (!unicodeText) {
        return "";
    }

    let shreeText = unicodeText;
    const array_length = sarray_two.length;

    // --- Stage 1: Replace Unicode sequences with ShreeLipi sequences using the arrays ---
    // Iterate in the *same order* as ShreeLipi_to_Unicode.js
    for (let i = 0; i < array_length; i++) {
        let current_unicode_char = sarray_two[i];
        let current_shree_char = sarray_one[i];

        // Skip empty strings in the unicode array
        if (current_unicode_char === "" || current_unicode_char === undefined) continue;
        if (current_shree_char === undefined) current_shree_char = ""; // Handle potential undefined if arrays mismatch length

        // Use RegExp for replacement, escaping special characters
        try {
            const escapedUnicode = current_unicode_char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(escapedUnicode, 'g');
            shreeText = shreeText.replace(regex, current_shree_char);
        } catch (e) {
            console.error(`Error creating/using RegExp for Unicode: ${current_unicode_char} to Shree: ${current_shree_char}`, e);
             // Fallback to simpler replacement (less safe for special chars)
             let idx = shreeText.indexOf(current_unicode_char);
             while (idx !== -1) {
                 shreeText = shreeText.substring(0, idx) + current_shree_char + shreeText.substring(idx + current_unicode_char.length);
                 idx = shreeText.indexOf(current_unicode_char, idx + current_shree_char.length);
             }
        }
    }

    // --- Stage 2: Adjustments for Short 'i'-matra ( ि -> o/n/p ?) ---
    // Original code replaced 'o' + consonant -> consonant + 'ि'.
    // And 'q' + consonant -> consonant + 'o' (intermediate?)
    // And finally 'o' -> 'िं'.
    // The arrays map 'n' and 'p' to 'o'. This suggests 'o' is the Shree representation for 'ि'.
    // We will reverse the primary 'o' rule: consonant + 'ि' -> 'o' + consonant.
    // We ignore the complex 'q' and final 'o' -> 'िं' reversal, assuming the arrays
    // and the primary 'i'-matra rule cover most cases. 'िं' might be handled by
    // specific array mappings (e.g., '§m'/'±m'/'m°').

    // Define consonants (basic Hindi/Marathi consonants + common conjunct bases)
    const consonants = "कखगघङचछजझञटठडढणतथदधनपफबभमयरलवशषसहक्षज्ञत्रश्रद्य";
    // RegExp: Match a consonant, capture it ($1), followed by 'ि'. Replace with 'o' + captured consonant.
    shreeText = shreeText.replace(new RegExp(`([${consonants}])ि`, 'g'), 'o$1');


    // --- Stage 3: Adjustments for Reph ( र् -> © ) ---
    // Original code moved '©' (after cluster) to 'र्' (before cluster), skipping matras.
    // Reversing this placement accurately is complex.
    // The arrays map "©ं" -> "ª" and "{©" -> "}" and "B©" -> "È", "र्" -> "©".
    // We will *rely heavily* on the array mappings for Reph conversion.
    // No general `र्` -> `©` rule is added here due to the complexity of correct placement
    // relative to consonants and matras in ShreeLipi. We assume the array mappings
    // (like 'र्' -> '©', 'प्र' -> 'º$', 'ट्र' -> 'Ì', 'द्र' -> 'Õ', 'श्र' -> 'Å') handle it.


    // --- Stage 4: Final Cleanup / Specific Character Reversals (if any) ---
    // Check if any standalone characters need specific mapping not covered by arrays.
    // For example, if the original had simple punctuation swaps at the end. (None apparent here).

    // Return the converted text
    return shreeText;
}

// Note: This reverse conversion relies heavily on the provided arrays (`sarray_one`, `sarray_two`).
// Accuracy depends on the comprehensiveness of these arrays.
// The reversal of special handling for 'o' (ि) is simplified, and the Reph ('©') reversal
// relies almost entirely on the array mappings due to the complexity of placement rules.
// Thorough testing is essential.