var sarray_one = new Array(  '~' ,  'u' ,  'ª' ,  '}' ,   'n' , 'p' ,

    'H' ,  '·' ,   'I' ,  '»' ,  'J' ,  '½' ,  'K' ,

    'M' ,  'À' ,  'N' ,   'µO' ,  'O' ,   'µÁ' ,  'Á' ,  'P' ,

    'Q' ,  'R' ,   '¶S' ,  'S' ,   '¶T' ,   'T' ,   'U' ,  'Ê' ,

    'W' , 'Ï' ,  'V' ,  'Ë' ,  'Y',  'Ü' ,  'X' ,  'Z' ,  'Ý' ,


    '\\' ,  'â' ,  '[' ,  'ß' ,  '^' ,   'ä' ,  ']' ,   'ã' ,   '_'  ,  'å' ,

    '`' ,    'a' ,    'c' ,    'ë',    'd' ,     'ì',

    'e' ,  'û' ,  'í' ,  'f' ,  'î' ,  'g' ,  'ñ' ,  'h' ,  'j' ,  'ú' ,  'k' ,

    'Ô' ,  'Û' ,  'Ú' ,  'à' ,  'Þ' , 'Q­' ,  'º$' ,  'Ì' ,  'Ð' ,  'Õ' ,  'l' ,  'Îm' ,   '¸' ,   '„' ,  'ˆ' ,  'œ' ,  'Å' ,  'È' ,


    'Am¡' ,  'Am{' ,  'Am|' ,  'Am' ,  'A' ,  'B©' ,  'B' , 'C' , 'D',   'E{',  'E' ,  'F' ,

    '§m' ,   '§w' ,   '§p' ,    '§y' ,    '±m' ,   '±w' ,   '±p' ,   '±y' ,    // for  changing the order  of anuswaar & chandrabindu with other maatraas

    'm°' ,    'm{' ,    'm|' ,    '{' ,   '|' ,  'm¡' ,   'm¢' ,   '¡' ,   '¢' ,

    'm' ,    'r' ,   's' ,  't' ,    'w' ,   'þ' ,  'y' ,   '§' ,  '¨' ,   '±',   '•' ,  '¥' ,  '²' ,

    'Ñ' ,  '«' ,  'é' ,  'ê' ,  '&' ,  '$' ,  '>' ,  'µ'  ,  '°' ,  '\¶'      );


var sarray_two = new Array("ॐ", "ऽ", "©ं", "{©", "o", "o", "क", "क्", "ख", "ख्", "ग", "ग्", "घ", "च", "च्", "छ", "ज़", "ज", "ज़्‌", "ज्", "झ", "ट", "ठ", "ड़", "ड", "ढ़", "ढ", "ण", "ण्", "थ", "थ्", "त", "त्", "ध", "ध्", "द", "न", "न्", "फ", "फ्", "प", "प्", "भ", "भ्", "ब", "ब्", "म", "म्", "य", "र", "ल", "ल्", "व", "व्", "श", "श्", "श्", "ष", "ष्", "स", "स्", "ह", "क्ष", "क्ष्", "ज्ञ", "द्द", "द्व", "द्य", "प्र", "न्न", "ट्र", "क्त", "त्र", "द्र", "द्ध", "श्र", "त्त", "क्क", "ल्ल", "ह्व", "श्व", "ट्ट", "ड्ड", "औ", "ओ", "ओं", "आ", "अ", "ई", "इ", "उ", "ऊ", "ऐ", "ए", "ऋ", "ां", "ुं", "ुं", "ूँ", "ाँ", "ुँ", "ुँ", "ूँ", "ॉ", "ो", "ों", "े", "ें", "ौ", "ौं", "ै", "ैं", "ा", "ी", "ी", "ीं", "ु", "ु", "ू", "ं", "ं", "ँ", "ः", "ृ", "्", "दृ", "्र", "रु", "रू", "।", "", "", "", "", "");


export function ShreeLipi_to_Unicode(inputText) {
    var array_one_length = sarray_one.length;
    var modified_substring = inputText;

    function Replace_Symbols(input_substring) {
        var modified_substring = input_substring;
        var input_symbol_idx;
        var indx;
        var position_of_i;
        var charecter_next_to_i;
        var charecter_to_be_replaced;
        var position_of_wrong_ee;
        var consonent_next_to_wrong_ee;
        var set_of_matras;
        var position_of_reph;
        var probable_position_of_half_r;
        var charecter_at_probable_position_of_half_r;
        var new_replacement_string;

        if ( modified_substring !== "" ) {
            for(input_symbol_idx = 0;   input_symbol_idx < array_one_length;    input_symbol_idx++ ) {
                indx = 0  ;
                while (indx !== -1 ) {
                    modified_substring = modified_substring.replace( sarray_one[ input_symbol_idx ] , sarray_two[input_symbol_idx] );
                    indx = modified_substring.indexOf( sarray_one[input_symbol_idx] );
                }
            }

            position_of_i = modified_substring.indexOf( "o" );
            while ( position_of_i !== -1 ) {
                charecter_next_to_i = modified_substring.charAt( position_of_i + 1 );
                charecter_to_be_replaced = "o" + charecter_next_to_i;
                modified_substring = modified_substring.replace( charecter_to_be_replaced , charecter_next_to_i + "ि" );
                position_of_i = modified_substring.search( /o/ , position_of_i + 1 );
            }

            position_of_wrong_ee = modified_substring.indexOf( "ि्" );
            while ( position_of_wrong_ee !== -1 ) {
                consonent_next_to_wrong_ee = modified_substring.charAt( position_of_wrong_ee + 2 );
                charecter_to_be_replaced = "ि्" + consonent_next_to_wrong_ee;
                modified_substring = modified_substring.replace( charecter_to_be_replaced , "्" + consonent_next_to_wrong_ee + "ि" );
                position_of_wrong_ee = modified_substring.search( /ि्/ , position_of_wrong_ee + 2 );
            }

            position_of_i = modified_substring.indexOf( "q" );
            while ( position_of_i !== -1 ) {
                charecter_next_to_i = modified_substring.charAt( position_of_i + 1 );
                charecter_to_be_replaced = "q" + charecter_next_to_i;
                modified_substring = modified_substring.replace( charecter_to_be_replaced , charecter_next_to_i + "o" );
                position_of_i = modified_substring.search( /q/ , position_of_i + 1 );
            }

            position_of_wrong_ee = modified_substring.indexOf( "o्" );
            while ( position_of_wrong_ee !== -1 ) {
                consonent_next_to_wrong_ee = modified_substring.charAt( position_of_wrong_ee + 2 );
                charecter_to_be_replaced = "o्" + consonent_next_to_wrong_ee;
                modified_substring = modified_substring.replace( charecter_to_be_replaced , "्" + consonent_next_to_wrong_ee + "िं" );
                position_of_wrong_ee = modified_substring.search( /o्/ , position_of_wrong_ee + 3 );
            }

            modified_substring = modified_substring.replace( /o/g  ,  'िं' )  ;

            set_of_matras = "ािीुूृेैोौंःँॅ";
            position_of_reph = modified_substring.indexOf( "©" );

            while( position_of_reph > 0 ) {
                probable_position_of_half_r = position_of_reph - 1 ;
                charecter_at_probable_position_of_half_r = modified_substring.charAt( probable_position_of_half_r );

                while( set_of_matras.match( charecter_at_probable_position_of_half_r ) !== null ) {
                    probable_position_of_half_r = probable_position_of_half_r - 1 ;
                    charecter_at_probable_position_of_half_r = modified_substring.charAt( probable_position_of_half_r ) ;
                }

                charecter_to_be_replaced = modified_substring.substr ( probable_position_of_half_r , ( position_of_reph - probable_position_of_half_r ) ) ;
                new_replacement_string = "र्" + charecter_to_be_replaced ;
                charecter_to_be_replaced = charecter_to_be_replaced + "©" ;
                modified_substring = modified_substring.replace( charecter_to_be_replaced , new_replacement_string ) ;
                position_of_reph = modified_substring.indexOf( "©" ) ;
            }
        }
        return modified_substring;
    }

    return Replace_Symbols(modified_substring);
}
