var array_one_aps = new Array("“","”","\"","् ",
    "क़","ख़","ग़","ज़","ड़","ढ़","फ़",  // two-byte varnas                      //07
    "ऑ","क्त","क्र","के","कृ","कु","क़","फ़","ज़","ख़","य़","ग़","फ","फ","फ्","फ्",           //16
    "क़्","ख़्",                     						    //02
    "ॐ","।","ऋ",                    						    //03
    "स्त्र","स्त्र्","स्र","स्र्",                  				    //07

    "क्षि","त्रि","ज्ञि","श्रि",                     					    //04
    "श्र","श्र्","त्र","त्र्","ग्र्","व्र्","ज्र्","ह्न","प्र्","ह्र","झ्र्","प्र्","द्र",                    	    //13
    "रु","रू","रू",

    "क्ष","क्ष्","ज्ञ","ज्ञ्","न्न्","ष्ठ","ष्ठ","ङ्घ","ङ्ख","ङ्ग","h","ह्व","h","श्च्","द्म्","ट्ठ",      //17
    "ड्ढ","ङ्क","ञ्ज्","ङ्ग","दृ","ञ्च्","व्न्","द्य्","द्भ","ङ्ख","द्व","ढ्ढ","श्व्",              //15 "ष्ट्र","ष्ट",
    "द्ध","ड्ड","त्त्","ट्ट","द्ब","द्द","हृ","ठ्ठ","द्ग","ह्म्",                                //10

    "ड़","ढ़",                                                           //02

    "ट्र","ठ्र","ड्र","ढ्र","्रू","्रु","्र",                                         //07
    "्न","्य्","्ल",                                                    //03

    "क्","क","ख्","ख","ग्","ग","घ्","घ","ङ",                                    //09
    "च्","च","छ","ज़्","ज़","ज्","ज","झ्","झ","ञ्","ञ",                             //11
    "ट","ठ","ड","ढ","ण्","ण",                                              //06
    "त्","t","थ्","थ","द","ध्","ध","न्","न",                                    //09
    "प्","p","फ्","फ","ब्","ब","भ्","भ","म्","म",                                //10

    "शृ","य्","य","र","ल्","ल","ळ्","ळ","व्","व","श्","श","ष्","ष","स्","स","ह्","h",        //18 // "श्","श्",

    "औ","ओ","ऑ","आ","अ","ई","इ","उ","ऊ","ऋ","ऌ","ऐ","ए",                     //13

    "ौ","ो","ॉ","ा","ी","ु","ू","ृ","ॢ","े","ै","ँ","ं","ः","्","ॅ","ऽ","़",       //18

    "०","१","२","३","४","५","६","७","८","९");

var array_two_aps = new Array("‘","’","'","d ",
    "क़","ख़","ग़","ज़","ड़","ढ़","फ़",  //one-byte varnas
    "Dee@","òeâ","›eâ","kesâ","ke=â","kegâ","Ìkeâ","ÌHeâ","Ìpe","ÌKe","ÌÙe","Ìie","Heâ","heâ","Heä","heä",
    "Ìkeä","ÌK",
    "Ô","~","$e+",
    "Œe","Œ","œe","œ",

    "ef#e","ef$e","ef%e","efße",
    "ße","ß","$e","$","«","›","¿","Ö","Ø","Ü","ã","ø","õ",
    "®","™","¤",

    "#e","#","%e","%","V","…","‰","‹","“","”","¢","£","¥","§","©","ª",
    "º","»","À","Á","Â","Ã","Ä","Å","Æ","È","É","Ñ","Õ",  //"°^","°",
    "æ","ñ","ò","ó","ô","ö","ù","ú","û","ÿ",

    "Ì[","Ì{",

    "š^","\"^","[^","{^","Í","Î","Ç",
    ">","Ÿ","ê",

    "keä","keâ","K","Ke","i","ie","I","Ie","*",
    "Û","Ûe","Ú","[p","[pe","p","pe","P","Pe","_","_",
    "š","\"","[","{","C","Ce",
    "l","le","L","Le","o","O","Oe","v","ve",
    "h","he","heä","heâ","y","ye","Y","Ye","c","ce",

    "ëe=","Ù","Ùe","j","u","}","à","U","k","ke","M","Me","<","<e","m","me","å","n",  // "ç","ë",

    "Deew","Dees","Dee@","Dee","De","F&","F","G","T","$e+","è","Ss","S",

    "ew","es","e@","e","er","g","t","=","ï","s","w","B","b","Š","d","@","Ó","Ê",

    "0","1","2","3","4","5","6","7","8","9")

export function convert_unicode_to_aps(inputText) {
    var array_one_length = array_one_aps.length;
    var modified_substring = inputText;

    function Replace_Symbols(input_substring) {
        var modified_substring = input_substring;
        var input_symbol_idx;
        var idx;
        var position_of_f;
        var character_left_to_f;
        var string_to_be_replaced;
        var set_of_matras;
        var position_of_half_R;
        var probable_position_of_Z;
        var character_at_probable_position_of_Z;
        var right_to_position_of_Z;
        var character_right_to_position_of_Z;
        var space;


        if (modified_substring !== "" ) {

            modified_substring = modified_substring.replace ( /त्र्य/g , "$य" )  ;

            // code for replacing "ि" (chhotee ee kii maatraa) with "ef"  and correcting its position too.

            position_of_f = modified_substring.indexOf( "ि" )  ;
            while ( position_of_f !== -1 ) {
                character_left_to_f = modified_substring.charAt( position_of_f - 1 )  ;
                modified_substring = modified_substring.replace( character_left_to_f + "ि" ,  "µ" + character_left_to_f )  ;

                position_of_f = position_of_f - 1  ;

                while (( modified_substring.charAt( position_of_f - 1 ) === "्" )  &&  ( position_of_f !== 0  ) ) {
                    string_to_be_replaced = modified_substring.charAt( position_of_f - 2) + "्"  ;
                    modified_substring = modified_substring.replace( string_to_be_replaced + "µ", "µ" + string_to_be_replaced ) ;

                    position_of_f = position_of_f - 2  ;
                }
                position_of_f = modified_substring.search( /ि/ , position_of_f + 1 ) ; // search for f ahead of the current position.

            }
            //************************************************************
            // Eliminating "र्" and putting  Z  at proper position for this.

            set_of_matras = "ािीुूृेैोौं:ँॅ";

            modified_substring += '  '    ;  // add two spaces after the string to avoid UNDEFINED char in the following code.

            var position_of_half_R = modified_substring.indexOf( "र्" ) ;

            while ( position_of_half_R > 0  ) {
                // "र्"  is two bytes long
                probable_position_of_Z = position_of_half_R + 2   ;
                character_at_probable_position_of_Z = modified_substring.charAt( probable_position_of_Z );

                // trying to find non-maatra position right to probable_position_of_Z .

                while( set_of_matras.match( character_at_probable_position_of_Z ) !== null ) {
                    probable_position_of_Z = probable_position_of_Z + 1 ;
                    character_at_probable_position_of_Z = modified_substring.charAt( probable_position_of_Z ) ;
                }
                //************************************************************
                // check if the next character is a halant
                //************************************************************
                right_to_position_of_Z = probable_position_of_Z + 1 ;

                if (right_to_position_of_Z > 0) {
                    character_right_to_position_of_Z = modified_substring.charAt( right_to_position_of_Z );

                    while ("्".match( character_right_to_position_of_Z ) !== null ) {
                        //       halant found, move to next character
                        probable_position_of_Z = right_to_position_of_Z + 1 ;
                        character_at_probable_position_of_Z = modified_substring.charAt( probable_position_of_Z ) ;

                        right_to_position_of_Z = probable_position_of_Z + 1 ;
                        character_right_to_position_of_Z = modified_substring.charAt( right_to_position_of_Z );
                    }
                }
                //************************************************************

                string_to_be_replaced = modified_substring.substr ( position_of_half_R + 2,(probable_position_of_Z - position_of_half_R)-1) ;
                modified_substring = modified_substring.replace( "र्" + string_to_be_replaced, string_to_be_replaced + "&" ) ;

                position_of_half_R = modified_substring.indexOf( "र्" ) ;
            }
            //***********************************************************

            modified_substring = modified_substring.substr ( 0 , modified_substring.length - 2 )  ;

            //substitute array_two elements in place of corresponding array_one elements

            for( input_symbol_idx = 0; input_symbol_idx < array_one_length; input_symbol_idx++ ) {
                idx = 0  ;  // index of the symbol being searched for replacement

                while (idx !== -1 ) {
                    modified_substring = modified_substring.replace( array_one_aps[ input_symbol_idx ] , array_two_aps[input_symbol_idx] );
                    idx = modified_substring.indexOf( array_one_aps[input_symbol_idx] );
                }
            }

        }

        modified_substring = modified_substring.replace ( /µ/g , "ef" );
        modified_substring = modified_substring.replace ( /d &/g , "d&" );
        modified_substring = modified_substring.replace ( /&Ùe/g , "NÙe" );
        modified_substring = modified_substring.replace ( /&er/g , "er&" );
        modified_substring = modified_substring.replace ( /n&/g , "Nn" );

        return modified_substring;
    }

    return Replace_Symbols(modified_substring);
}
