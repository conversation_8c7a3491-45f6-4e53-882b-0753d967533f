// src/pages/user/UserEditPage.js
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { getUser, updateUser } from '../../services/api';
import UserForm from './UserForm'; // Import the reusable form
import LoadingSpinner from '../../components/common/LoadingSpinner';

function UserEditPage() {
    const { userId } = useParams();
    const navigate = useNavigate();
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [submitLoading, setSubmitLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [generalError, setGeneralError] = useState(null);

    const fetchUserData = useCallback(async () => {
        setLoading(true); setErrors({}); setGeneralError(null);
        try {
            const response = await getUser(userId);
            setUser(response.data.data);
        } catch (err) {
            console.error("Failed to fetch user:", err);
            setGeneralError('Failed to load user data.');
            setUser(null);
        } finally { setLoading(false); }
    }, [userId]);

    useEffect(() => { fetchUserData(); }, [fetchUserData]);

    const handleSubmit = async (formData) => { // Receives processed data from UserForm
        setSubmitLoading(true); setErrors({}); setGeneralError(null);
        try {
            console.log("UserEditPage submitting data:", formData); // Debug: See data before API call
            await updateUser(userId, formData); // Call API with processed data
            navigate('/users', { state: { message: 'User updated successfully!' } });
        } catch (err) {
             console.error("Failed to update user:", err.response?.data);
             if (err.response && err.response.status === 422) {
                  setErrors(err.response.data.errors);
                  setGeneralError('Please check the form for errors.');
             } else {
                 setGeneralError(err.response?.data?.message || 'An unexpected error occurred while updating.');
             }
             setSubmitLoading(false);
        }
    };

    if (loading) { return <LoadingSpinner />; }

    if (generalError && !user) {
        return (
             <div>
                <div className="content-header">
                    <div className="container-fluid"><h1 className="m-0">Edit User</h1><div className="alert alert-danger mt-3">{generalError}</div><Link to="/users" className="btn btn-secondary mt-2">Back to Users List</Link></div>
                </div>
            </div>
        );
    }

    return (
        <div>
            {/* Content Header */}
             <div className="content-header">
                 <div className="container-fluid">
                     <div className="row mb-2">
                         <div className="col-sm-6"><h1 className="m-0">Edit User: {user?.name}</h1></div>
                         <div className="col-sm-6">
                             <ol className="breadcrumb float-sm-right">
                                 <li className="breadcrumb-item"><Link to="/">Home</Link></li> {/* Should link to /dashboard */}
                                 <li className="breadcrumb-item"><Link to="/users">Users</Link></li>
                                 <li className="breadcrumb-item active">Edit User</li>
                             </ol>
                         </div>
                     </div>
                 </div>
             </div>
             {/* Main Content */}
             <section className="content">
                <div className="card card-primary">
                     <div className="card-header"><h3 className="card-title">Edit User Details</h3></div>
                     <div className="card-body">
                        {/* Display general error if it's not a validation error */}
                        {generalError && Object.keys(errors).length === 0 && <div className="alert alert-danger">{generalError}</div>}
                        {user && (
                            <UserForm
                                onSubmit={handleSubmit}
                                initialData={user}
                                isEditing={true}
                                isLoading={submitLoading}
                                errors={errors} // Pass down validation errors
                            />
                        )}
                     </div>
                 </div>
             </section>
        </div>
    );
}
export default UserEditPage;