// src/pages/user/UserForm.js
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { getPlans } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { addMonths, format, isValid, parseISO } from 'date-fns';
import md5 from 'md5';
import { indianStates } from '../../data/indianStates';

function UserForm({ onSubmit, initialData = null, isEditing = false, isLoading = false, errors: parentErrors = {} }) {

    const initialFormData = useMemo(() => ({
        name: '', email: '', state: '', city: '', phone: '',
        password: '', password_confirmation: '', plan_id: '',
        purchase_date: '', expiry_date: '', executive: '',
    }), []);

    const [formData, setFormData] = useState(initialFormData);
    const [plans, setPlans] = useState([]);
    const [plansLoading, setPlansLoading] = useState(false);
    const [plansError, setPlansError] = useState(null);
    const [errors, setErrors] = useState(parentErrors);
    const [planChangedDuringEdit, setPlanChangedDuringEdit] = useState(false);


     useEffect(() => { setErrors(parentErrors); }, [parentErrors]);

    // --- Fetch Plans ---
    const fetchPlansForDropdown = useCallback(async () => {
        setPlansLoading(true); setPlansError(null); setPlans([]);
        try {
            const response = await getPlans({ per_page: -1 });
            const fetchedPlans = response.data.data || [];
            if (Array.isArray(fetchedPlans)) { setPlans(fetchedPlans); }
            else { setPlansError('Received invalid plan data format.'); }
        } catch (error) { setPlansError('Could not load plans.'); }
        finally { setPlansLoading(false); }
     }, []);
    useEffect(() => { fetchPlansForDropdown(); }, [fetchPlansForDropdown]);

    // --- Populate Form on Edit / Reset on Create ---
    useEffect(() => {
        setPlanChangedDuringEdit(false); // Reset tracker
        if (isEditing && initialData) {
           const currentPlanId = initialData.plan?.id;
           const planIdForState = currentPlanId != null ? String(currentPlanId) : '';
           setFormData({
               name: initialData.name || '', email: initialData.email || '',
               state: initialData.state || '', city: initialData.city || '', phone: initialData.phone || '',
               password: '', password_confirmation: '', plan_id: planIdForState,
               purchase_date: initialData.purchase_date || '', expiry_date: initialData.expiry_date || '',
               executive: initialData.executive || '',
            });
            setErrors({});
        } else if (!isEditing) {
            setFormData(initialFormData);
            setErrors({});
        }
    }, [initialData, isEditing, initialFormData]);

    // --- Handle Input Changes ---
    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        // Handle checkbox inputs differently
        const inputValue = type === 'checkbox' ? checked : value;
        let updatedFormData = { ...formData, [name]: inputValue };

        if (errors[name]) { setErrors(prev => ({ ...prev, [name]: null })); }
        if (name === 'password' && errors.password_confirmation) { setErrors(prev => ({ ...prev, password_confirmation: null })); }

        // Calculate Dates when PLAN changes
        if (name === 'plan_id') {
            // Mark plan as changed if in edit mode
            if (isEditing) { setPlanChangedDuringEdit(true); }

            const selectedPlanId = value;
            if (!selectedPlanId) {
                updatedFormData.purchase_date = ''; updatedFormData.expiry_date = '';
            } else {
                const selectedPlan = plans.find(plan => String(plan.id) === selectedPlanId);
                if (selectedPlan) {
                    const durationMonths = parseInt(selectedPlan.months, 10);
                    if (!isNaN(durationMonths) && durationMonths >= 0) {
                        try {
                            const today = new Date();
                            updatedFormData.purchase_date = format(today, 'yyyy-MM-dd');
                            const expiryDateObj = addMonths(today, durationMonths);
                            updatedFormData.expiry_date = format(expiryDateObj, 'yyyy-MM-dd');
                        } catch (error) { updatedFormData.purchase_date = ''; updatedFormData.expiry_date = ''; }
                    } else { updatedFormData.purchase_date = ''; updatedFormData.expiry_date = ''; }
                } else { updatedFormData.purchase_date = ''; updatedFormData.expiry_date = ''; }
            }
        }
        setFormData(updatedFormData);
    };

    // --- Handle Form Submission ---
    const handleSubmit = (e) => {
        e.preventDefault();
        if (isLoading || plansLoading) return;
        setErrors({});
        if (formData.password && formData.password !== formData.password_confirmation) {
            setErrors(prev => ({ ...prev, password_confirmation: ["Password confirmation does not match."] }));
            return;
        }
        const dataToSubmit = { ...formData };
        if (dataToSubmit.password && dataToSubmit.password.length > 0) {
            dataToSubmit.password = md5(dataToSubmit.password);
            delete dataToSubmit.password_confirmation;
        } else {
            delete dataToSubmit.password;
            delete dataToSubmit.password_confirmation;
        }
        if (dataToSubmit.plan_id === '') dataToSubmit.plan_id = null;
        if (!dataToSubmit.purchase_date) dataToSubmit.purchase_date = null;
        if (!dataToSubmit.expiry_date) dataToSubmit.expiry_date = null;
        onSubmit(dataToSubmit);
    };

    // --- Helper to Display Errors ---
    const displayError = (field) => {
        return errors?.[field] ? <span className="invalid-feedback d-block">{errors[field].join(', ')}</span> : null;
    };

    // Determine if dates should be read-only
    const areDatesReadOnly = !isEditing || (isEditing && planChangedDuringEdit);

    // --- Render Form ---
    return (
        <form onSubmit={handleSubmit} noValidate>
            <div className="row">
                {/* Left Column */}
                <div className="col-md-6">
                     <div className="form-group mb-3"><label htmlFor="name">Name <span className="text-danger">*</span></label><input type="text" id="name" name="name" className={`form-control ${errors?.name ? 'is-invalid' : ''}`} value={formData.name} onChange={handleChange} required disabled={isLoading} />{displayError('name')}</div>
                     <div className="form-group mb-3"><label htmlFor="email">Email <span className="text-danger">*</span></label><input type="email" id="email" name="email" className={`form-control ${errors?.email ? 'is-invalid' : ''}`} value={formData.email} onChange={handleChange} required disabled={isLoading} />{displayError('email')}</div>
                     <div className="form-group mb-3">
                        <label htmlFor="state">State <span className="text-danger">*</span></label>
                        <select name="state" id="state" className={`form-control ${errors?.state ? 'is-invalid' : ''}`} value={formData.state} onChange={handleChange} required disabled={isLoading} >
                            <option value="" disabled>-- Select State --</option>
                            {indianStates.map(stateName => (<option key={stateName} value={stateName}>{stateName}</option>))}
                        </select>{displayError('state')}
                    </div>
                     <div className="form-group mb-3"><label htmlFor="city">City <span className="text-danger">*</span></label><input type="text" id="city" name="city" className={`form-control ${errors?.city ? 'is-invalid' : ''}`} value={formData.city} onChange={handleChange} required disabled={isLoading} />{displayError('city')}</div>
                    <div className="form-group mb-3"><label htmlFor="phone">Phone <span className="text-danger">*</span></label><input type="tel" id="phone" name="phone" className={`form-control ${errors?.phone ? 'is-invalid' : ''}`} value={formData.phone} onChange={handleChange} required disabled={isLoading} />{displayError('phone')}</div>

                    <div className="form-group mb-3">
                        <label htmlFor="executive">Executive</label>
                        <input
                            type="text"
                            id="executive"
                            name="executive"
                            className={`form-control ${errors?.executive ? 'is-invalid' : ''}`}
                            value={formData.executive}
                            onChange={handleChange}
                            placeholder="Executive (Optional)"
                            disabled={isLoading}
                        />
                        {displayError('executive')}
                    </div>
                </div>
                {/* Right Column */}
                <div className="col-md-6">
                     <div className="form-group mb-3"><label htmlFor="password">Password {isEditing && !formData.password ? '(Leave blank)' : <span className="text-danger">*</span>}</label><input type="password" id="password" name="password" className={`form-control ${errors?.password ? 'is-invalid' : ''}`} value={formData.password} onChange={handleChange} required={!isEditing} disabled={isLoading} />{displayError('password')}</div>
                    <div className="form-group mb-3"><label htmlFor="password_confirmation">Confirm Password {isEditing || !formData.password ? '' : <span className="text-danger">*</span>}</label><input type="password" id="password_confirmation" name="password_confirmation" className={`form-control ${errors?.password_confirmation ? 'is-invalid' : ''}`} value={formData.password_confirmation} onChange={handleChange} required={!isEditing && !!formData.password} disabled={isLoading} />{displayError('password_confirmation')}</div>
                    <div className="form-group mb-3">
                       <label htmlFor="plan_id">Assign Plan</label>
                       <select id="plan_id" name="plan_id" className={`form-control ${errors?.plan_id ? 'is-invalid' : ''}`} value={formData.plan_id} onChange={handleChange} disabled={isLoading || plansLoading} >
                           <option value="">-- No Plan --</option>
                           {Array.isArray(plans) && plans.map(plan => (<option key={plan.id} value={plan.id}>{plan.plan_name} ({plan.months} months)</option>))}
                       </select>
                       {plansLoading && <LoadingSpinner small centered={false}/>}
                       {plansError && !plansLoading && <small className="text-danger d-block mt-1">{plansError}</small>}
                       {displayError('plan_id')}
                   </div>
                   <div className="form-group mb-3">
                       <label htmlFor="purchase_date">Purchase Date</label>
                       <input type="date" id="purchase_date" name="purchase_date" className={`form-control ${errors?.purchase_date ? 'is-invalid' : ''} ${areDatesReadOnly ? 'bg-light' : ''}`} value={formData.purchase_date} onChange={handleChange} readOnly={areDatesReadOnly} disabled={isLoading}/>
                       {displayError('purchase_date')}
                   </div>
                   <div className="form-group mb-3">
                       <label htmlFor="expiry_date">Expiry Date {areDatesReadOnly ? '(Calculated)' : ''}</label>
                       <input type="date" id="expiry_date" name="expiry_date" className={`form-control ${errors?.expiry_date ? 'is-invalid' : ''} ${areDatesReadOnly ? 'bg-light' : ''}`} value={formData.expiry_date} onChange={handleChange} readOnly={areDatesReadOnly} disabled={isLoading}/>
                       {displayError('expiry_date')}
                   </div>
                </div>
            </div>
            {/* Submit Buttons */}
            <div className="row mt-3">
                <div className="col-12">
                     <Link to="/users" className="btn btn-secondary mr-2" disabled={isLoading}>Cancel</Link>
                     <button type="submit" className="btn btn-primary" disabled={isLoading || plansLoading}>
                         {isLoading ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update User' : 'Create User')}
                     </button>
                </div>
            </div>
        </form>
    );
}
export default UserForm;