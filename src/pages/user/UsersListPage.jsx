// src/pages/user/UsersListPage.js
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getUsers, deleteUser, getPlans, updateUserStatus } from '../../services/api'; // Import new functions
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';

// Simple debounce hook (or use lodash.debounce)
function useDebounce(value, delay) {
    const [debouncedValue, setDebouncedValue] = useState(value);
    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);
    return debouncedValue;
}


function UsersListPage() {
    // State for data, loading, errors
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [actionLoading, setActionLoading] = useState(null); // Loading state for specific user actions (e.g., activate/deactivate)
    const [error, setError] = useState(null);
    const [paginationMeta, setPaginationMeta] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');

    // State for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [executiveSearchTerm, setExecutiveSearchTerm] = useState('');
    const [selectedPlanId, setSelectedPlanId] = useState(''); // Empty string for 'All Plans'
    const [selectedState, setSelectedState] = useState(''); // Empty string for 'All States'
    const [selectedStatus, setSelectedStatus] = useState(''); // Empty string for 'All Statuses'
    const [plans, setPlans] = useState([]); // For filter dropdown
    const [plansLoading, setPlansLoading] = useState(false);

    // Import Indian states for filter
    const { indianStates } = require('../../data/indianStates');

    // Debounce search terms to avoid excessive API calls
    const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms delay
    const debouncedExecutiveSearchTerm = useDebounce(executiveSearchTerm, 500); // 500ms delay

    const location = useLocation();
    const navigate = useNavigate();
    const isMounted = useRef(true); // Ref to track mount status for async cleanup

     // Cleanup ref on unmount
     useEffect(() => {
        isMounted.current = true;
        return () => { isMounted.current = false; };
    }, []);

    // Effect to clear success message on navigation or filter change
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            navigate(location.pathname, { replace: true, state: {} });
        } else {
             // Clear message if search/filter changes and no new message arrives
             // setSuccessMessage(''); // Or keep until next success
        }
    }, [location.state, navigate, location.pathname, debouncedSearchTerm, debouncedExecutiveSearchTerm, selectedPlanId]);


    // Fetch Plans for Filter Dropdown
    const fetchPlansForFilter = useCallback(async () => {
        if (!isMounted.current) return; // Prevent state update on unmounted component
        setPlansLoading(true);
        try {
            const response = await getPlans({ per_page: -1 }); // Fetch all
            if (isMounted.current) setPlans(response.data.data || []);
        } catch (err) {
            console.error("Failed to fetch plans for filter:", err);
            // setError('Could not load plans for filtering.'); // Or just log
        } finally {
            if (isMounted.current) setPlansLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchPlansForFilter();
    }, [fetchPlansForFilter]);


    // Fetch Users Function (now includes search and filter)
    const fetchUsers = useCallback(async (
        page = 1,
        search = debouncedSearchTerm,
        executiveSearch = debouncedExecutiveSearchTerm,
        planId = selectedPlanId,
        state = selectedState,
        status = selectedStatus
    ) => {
        if (!isMounted.current) return;
        setLoading(true);
        setError(null); // Clear previous errors on new fetch

        // Build query parameters dynamically
        const params = { page };
        if (search) params.search = search;
        if (executiveSearch) params.executive_search = executiveSearch;
        if (planId) params.plan_id = planId;
        if (state) params.state = state;
        if (status !== '') params.is_active = status === 'active' ? 1 : 0;

        console.log("Fetching users with params:", params); // Debug log

        try {
            const response = await getUsers(params);
            if (isMounted.current) {
                setUsers(response.data.data);
                setPaginationMeta(response.data.meta);
            }
        } catch (err) {
            console.error("Failed to fetch users:", err);
             if (isMounted.current) setError('Failed to load users. Please try again.');
        } finally {
            if (isMounted.current) setLoading(false);
        }
    }, [debouncedSearchTerm, debouncedExecutiveSearchTerm, selectedPlanId, selectedState, selectedStatus]); // Dependencies include all filters


    // Effect to fetch users when page, search term (debounced), or filter changes
    useEffect(() => {
        // Fetch users based on current state (page comes from paginationMeta or defaults to 1)
        fetchUsers(paginationMeta?.current_page || 1);
    }, [fetchUsers, paginationMeta?.current_page]); // Rerun fetchUsers only when its definition changes


    // Handle Deleting a User
    const handleDelete = async (id) => {
         if (actionLoading === id) return; // Prevent double clicks
        if (window.confirm('Are you sure you want to permanently delete this user?')) {
             setActionLoading(id); // Indicate loading for this specific user row
            try {
                await deleteUser(id);
                 if (isMounted.current) {
                    setSuccessMessage('User deleted successfully!');
                    // Refetch users on the current page
                    fetchUsers(paginationMeta?.current_page || 1);
                 }
            } catch (err) {
                console.error("Failed to delete user:", err);
                 if (isMounted.current) setError('Failed to delete user.');
            } finally {
                 if (isMounted.current) setActionLoading(null);
            }
        }
    };

    // Handle Toggling User Status (Activate/Deactivate)
    const handleToggleStatus = async (user) => {
        if (actionLoading === user.id) return; // Prevent double clicks
        const newStatus = !user.is_active; // Assuming boolean is_active field
        const actionText = newStatus ? 'activate' : 'deactivate';

        if (window.confirm(`Are you sure you want to ${actionText} user "${user.name}"?`)) {
            setActionLoading(user.id); // Indicate loading for this specific user row
            setError(null);
            setSuccessMessage('');
            try {
                await updateUserStatus(user.id, newStatus);
                if (isMounted.current) {
                    setSuccessMessage(`User ${actionText}d successfully!`);
                    // OPTION 1: Refetch the whole list (simplest)
                    fetchUsers(paginationMeta?.current_page || 1);

                    // OPTION 2: Update local state (more responsive, complex)
                    // setUsers(prevUsers => prevUsers.map(u =>
                    //     u.id === user.id ? { ...u, is_active: newStatus } : u
                    // ));
                 }

            } catch (err) {
                console.error(`Failed to ${actionText} user:`, err);
                 if (isMounted.current) setError(`Failed to ${actionText} user.`);
            } finally {
                 if (isMounted.current) setActionLoading(null);
            }
        }
    };


    const handlePageChange = (newPage) => {
        // Fetch data for the new page using current filters
        fetchUsers(
            newPage,
            debouncedSearchTerm,
            debouncedExecutiveSearchTerm,
            selectedPlanId,
            selectedState,
            selectedStatus
        );
    };

    return (
        <div>
            {/* Content Header */}
            <div className="content-header">
                <div className="container-fluid">
                    <div className="row mb-2">
                        <div className="col-sm-6"><h1 className="m-0">Users</h1></div>
                        <div className="col-sm-6">
                            <ol className="breadcrumb float-sm-right">
                                <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                <li className="breadcrumb-item active">Users</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <section className="content">
                <div className="card card-outline card-primary"> {/* Added card outline */}
                    <div className="card-header">
                        <h3 className="card-title"><i className="fas fa-users mr-1"></i>User Management</h3>
                        <div className="card-tools">
                            <Link to="/users/create" className="btn btn-success btn-sm">
                                <i className="fas fa-plus mr-1"></i> Add New User
                            </Link>
                        </div>
                    </div>
                     {/* Search and Filter Row */}
                    <div className="card-body pb-2"> {/* Less padding-bottom */}
                         {error && <div className="alert alert-danger">{error}</div>}
                         {successMessage && <div className="alert alert-success">{successMessage}</div>}
                         <div className="row mb-2">
                            <div className="col-md-3 mb-2">
                                <input
                                    type="text"
                                    className="form-control form-control-sm"
                                    placeholder="Search by Name or Email..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="col-md-3 mb-2">
                                <input
                                    type="text"
                                    className="form-control form-control-sm"
                                    placeholder="Search by Executive..."
                                    value={executiveSearchTerm}
                                    onChange={(e) => setExecutiveSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="col-md-2 mb-2">
                                <select
                                    className="form-control form-control-sm"
                                    value={selectedPlanId}
                                    onChange={(e) => setSelectedPlanId(e.target.value)}
                                    disabled={plansLoading}
                                >
                                    <option value="">-- Filter by Plan --</option>
                                    {plansLoading ? (
                                        <option disabled>Loading plans...</option>
                                    ) : (
                                        plans.map(plan => (
                                            <option key={plan.id} value={plan.id}>
                                                {plan.plan_name} ({plan.months} months)
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>
                            <div className="col-md-2 mb-2">
                                <select
                                    className="form-control form-control-sm"
                                    value={selectedState}
                                    onChange={(e) => setSelectedState(e.target.value)}
                                >
                                    <option value="">-- Filter by State --</option>
                                    {indianStates.map(state => (
                                        <option key={state} value={state}>
                                            {state}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="col-md-2 mb-2">
                                <select
                                    className="form-control form-control-sm"
                                    value={selectedStatus}
                                    onChange={(e) => setSelectedStatus(e.target.value)}
                                >
                                    <option value="">-- Filter by Status --</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                         </div>
                    </div>

                    <div className="card-body pt-0"> {/* Remove padding-top */}
                        {loading ? (
                            <LoadingSpinner />
                        ) : (
                            <>
                                <div className="table-responsive">
                                    <table className="table table-bordered table-striped table-hover table-sm"> {/* Added table-sm */}
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Plan</th>
                                                <th>State</th>
                                                <th>Executive</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {users.length > 0 ? users.map(user => (
                                                <tr key={user.id}>
                                                    <td>{user.id}</td>
                                                    <td>{user.name}</td>
                                                    <td>{user.email}</td>
                                                    <td>{user.plan ? user.plan.plan_name : <span className="text-muted">N/A</span>}</td>
                                                    <td>{user.state || <span className="text-muted">N/A</span>}</td>
                                                    <td>
                                                        {user.executive ? (
                                                            <span className="badge badge-info">{user.executive}</span>
                                                        ) : (
                                                            <span className="text-muted">No</span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        {user.is_active ? (
                                                            <span className="badge badge-success">Active</span>
                                                        ) : (
                                                            <span className="badge badge-secondary">Inactive</span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        {/* Activate/Deactivate Button */}
                                                        <button
                                                            onClick={() => handleToggleStatus(user)}
                                                            className={`btn btn-${user.is_active ? 'warning' : 'success'} btn-xs mr-1`} // xs size
                                                            title={user.is_active ? 'Deactivate' : 'Activate'}
                                                            disabled={actionLoading === user.id} // Disable while action is processing
                                                        >
                                                            {actionLoading === user.id ? (
                                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            ) : (
                                                                <i className={`fas fa-${user.is_active ? 'toggle-off' : 'toggle-on'}`}></i>
                                                            )}
                                                        </button>

                                                        {/* Edit Button */}
                                                        <Link to={`/users/edit/${user.id}`} className="btn btn-info btn-xs mr-1" title="Edit">
                                                            <i className="fas fa-edit"></i>
                                                        </Link>

                                                        {/* Delete Button */}
                                                        <button
                                                            onClick={() => handleDelete(user.id)}
                                                            className="btn btn-danger btn-xs"
                                                            title="Delete"
                                                            disabled={actionLoading === user.id} // Disable while action is processing
                                                        >
                                                             {actionLoading === user.id && user.is_active ? ( // Show spinner only if delete is loading
                                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            ) : (
                                                                <i className="fas fa-trash"></i>
                                                            )}
                                                        </button>
                                                    </td>
                                                </tr>
                                            )) : (
                                                <tr>
                                                    <td colSpan="8" className="text-center text-muted">No users found matching criteria.</td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-3">
                                    {/* Only show pagination if there are multiple pages */}
                                    {paginationMeta && paginationMeta.last_page > 1 && (
                                        <Pagination meta={paginationMeta} onPageChange={handlePageChange} />
                                    )}
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </section>
        </div>
    );
}

export default UsersListPage;