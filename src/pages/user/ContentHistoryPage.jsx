import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { getContentHistory, deleteContent } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import styles from '../editor/main.module.css'; // Re-using editor styles for consistency
import { useAuth } from '../../context/AuthContext';

function ContentHistoryPage() {
    const [contentList, setContentList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);
    const [hasMore, setHasMore] = useState(true);
    const [nextPageUrl, setNextPageUrl] = useState(null);
    const [isFetchingMore, setIsFetchingMore] = useState(false); // New state for loading more
    const navigate = useNavigate();
    const { logoutUser } = useAuth();

    const observer = useRef(); // For IntersectionObserver
    const lastContentElementRef = useCallback(node => {
        if (loading || isFetchingMore) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && hasMore && nextPageUrl && !isFetchingMore) {
                fetchContent(nextPageUrl, true); // Pass true to append
            }
        });
        if (node) observer.current.observe(node);
    }, [loading, hasMore, nextPageUrl, isFetchingMore]);


    const fetchContent = useCallback(async (url = null, append = false) => {
        if (append) {
            setIsFetchingMore(true);
        } else {
            setLoading(true);
            setContentList([]); // Clear list on initial load
        }
        setError(null);
        setSuccessMessage(null);

        try {
            const response = await getContentHistory(url); // API call now takes a direct URL
            const newContent = response.data.data;
            
            setContentList(prevContent => append ? [...prevContent, ...newContent] : newContent);
            setNextPageUrl(response.data.links.next);
            setHasMore(!!response.data.links.next); // True if next link exists
        } catch (err) {
            console.error("Error fetching content history:", err);
            const errorMessage = err.response?.data?.message || err.message || "Failed to load content history.";
            setError(errorMessage);
            if (err.response?.status === 401 || err.response?.status === 403) {
                logoutUser();
                navigate('/user/login');
            }
        } finally {
            setLoading(false);
            setIsFetchingMore(false);
        }
    }, [navigate, logoutUser]);

    useEffect(() => {
        fetchContent(); // Initial fetch
    }, [fetchContent]);

    // Removed handlePageChange as it's no longer needed for infinite scroll

    useEffect(() => {
        fetchContent();
    }, [fetchContent]);

    const handleDelete = useCallback(async (id) => {
        if (!window.confirm("Are you sure you want to delete this content?")) {
            return;
        }
        setLoading(true);
        setError(null);
        setSuccessMessage(null);
        try {
            await deleteContent(id);
            setSuccessMessage("Content deleted successfully!");
            // After deletion, re-fetch the content from the beginning to ensure correct pagination/infinite scroll state
            // This is important because deleting an item might shift subsequent items to previous pages.
            fetchContent(null, false); // Fetch from the start, not appending
        } catch (err) {
            console.error("Error deleting content:", err);
            setLoading(false);
            const errorMessage = err.response?.data?.message || err.message || "Failed to delete content.";
            setError(errorMessage);
        }
    }, [fetchContent]);


    const handleImport = useCallback((contentHtml) => {
        // This is a simplified import. In a real app, you might navigate to the editor
        // and pass the HTML content as state or a query parameter for editing.
        // For now, we'll just log it and provide a placeholder for navigation.
        console.log("Importing content to editor:", contentHtml);
        // Example: navigate('/user/editor', { state: { importedHtml: contentHtml } });
        // For this task, we will just navigate to the editor and let the user know to paste it.
        navigate('/user/editor', { state: { importedHtml: contentHtml } });
    }, [navigate]);

    return (
        <div className={styles.pageWrapper}>
            <main className={styles.mainContent}>
                <header className={styles.topNavbar}>
                    <div className={styles.navbarBrand}>Content History</div>
                    <div className={styles.navbarInfo}>
                        <button onClick={() => navigate('/user/editor')} className={styles.logoutButton}>
                            <i className="fas fa-edit mr-1"></i> Back to Editor
                        </button>
                    </div>
                </header>

                <div className={styles.alertContainer}>
                    {error && <div className={`${styles.alert} ${styles.alertDanger}`}>{error} <button onClick={() => setError(null)} className={styles.alertClose}>×</button></div>}
                    {successMessage && <div className={`${styles.alert} ${styles.alertSuccess}`}>{successMessage} <button onClick={() => setSuccessMessage(null)} className={styles.alertClose}>×</button></div>}
                </div>

                <div className={styles.contentPageContainer}>
                    <h2 className={styles.pageHeader}>Your Saved Content</h2>
                    {loading && !isFetchingMore ? ( // Show full spinner only on initial load
                        <LoadingSpinner />
                    ) : contentList.length === 0 ? (
                        <p className={styles.emptyState}>No saved content found. Start by saving something in the editor!</p>
                    ) : (
                        <div className={styles.contentGrid}>
                            {contentList.map((content, index) => {
                                if (contentList.length === index + 1) { // Attach ref to the last element
                                    return (
                                        <div ref={lastContentElementRef} key={content.id} className={styles.contentCard}>
                                            <div className={styles.contentInfo}>
                                                <h3>{content.title}</h3>
                                                {content.description && <p className={styles.contentDescription}>{content.description}</p>}
                                                <p className={styles.contentDate}>Saved: {new Date(content.updated_at).toLocaleString()}</p>
                                            </div>
                                            <div className={styles.contentCardActions}>
                                                <button onClick={() => handleImport(content.html_content)} className={`${styles.actionButton} ${styles.actionButtonPrimary} ${styles.actionButtonSmall}`}>
                                                    <i className="fas fa-file-import mr-1"></i> Import
                                                </button>
                                                <button onClick={() => handleDelete(content.id)} className={`${styles.actionButton} ${styles.actionButtonDanger} ${styles.actionButtonSmall}`}>
                                                    <i className="fas fa-trash mr-1"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    );
                                }
                                return (
                                    <div key={content.id} className={styles.contentCard}>
                                        <div className={styles.contentInfo}>
                                            <h3>{content.title}</h3>
                                            {content.description && <p className={styles.contentDescription}>{content.description}</p>}
                                            <p className={styles.contentDate}>Saved: {new Date(content.updated_at).toLocaleString()}</p>
                                        </div>
                                        <div className={styles.contentCardActions}>
                                            <button onClick={() => handleImport(content.html_content)} className={`${styles.actionButton} ${styles.actionButtonPrimary} ${styles.actionButtonSmall}`}>
                                                <i className="fas fa-file-import mr-1"></i> Import
                                            </button>
                                            <button onClick={() => handleDelete(content.id)} className={`${styles.actionButton} ${styles.actionButtonDanger} ${styles.actionButtonSmall}`}>
                                                <i className="fas fa-trash mr-1"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                );
                            })}
                            {isFetchingMore && ( // Show small spinner when fetching more
                                <div style={{ textAlign: 'center', padding: '20px' }}>
                                    <LoadingSpinner small />
                                </div>
                            )}
                            {!hasMore && contentList.length > 0 && ( // Message when all content is loaded
                                <p style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>You've reached the end of your saved content.</p>
                            )}
                        </div>
                    )}
                </div>
            </main>
        </div>
    );
}

export default ContentHistoryPage;