// src/pages/user/UserCreatePage.js
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { createUser } from '../../services/api'; // Correct path
import UserForm from './UserForm'; // Import the reusable form

function UserCreatePage() {
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState({}); // Store validation errors
    const [generalError, setGeneralError] = useState(null); // Store non-field errors

    const handleSubmit = async (formData) => {
        setIsLoading(true);
        setErrors({});
        setGeneralError(null);
        try {
            await createUser(formData);
            // Redirect to user list with a success message
            navigate('/users', { state: { message: 'User created successfully!' } });
        } catch (err) {
            console.error("Failed to create user:", err.response?.data);
            if (err.response && err.response.status === 422) {
                 // Handle validation errors
                 setErrors(err.response.data.errors);
                 setGeneralError('Please check the form for errors.'); // User feedback
            } else {
                // Handle other errors (network, server error, etc.)
                setGeneralError(err.response?.data?.message || 'An unexpected error occurred. Please try again.');
            }
            setIsLoading(false); // Ensure loading stops on error
        }
        // No finally block needed if navigating away on success
    };

    return (
        <div>
            {/* Content Header */}
            <div className="content-header">
                 <div className="container-fluid">
                     <div className="row mb-2">
                         <div className="col-sm-6"><h1 className="m-0">Add New User</h1></div>
                         <div className="col-sm-6">
                             <ol className="breadcrumb float-sm-right">
                                 <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                 <li className="breadcrumb-item"><Link to="/users">Users</Link></li>
                                 <li className="breadcrumb-item active">Add User</li>
                             </ol>
                         </div>
                     </div>
                 </div>
             </div>

            {/* Main Content */}
            <section className="content">
                <div className="card card-primary">
                    <div className="card-header">
                         <h3 className="card-title">User Details</h3>
                    </div>
                    <div className="card-body">
                       {generalError && <div className="alert alert-danger">{generalError}</div>}
                       <UserForm
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            errors={errors}
                            isEditing={false} // Explicitly set to false
                       />
                    </div>
                </div>
            </section>
        </div>
    );
}

export default UserCreatePage;