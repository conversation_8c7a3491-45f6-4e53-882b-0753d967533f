// src/pages/plan/PlansListPage.js
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getPlans, deletePlan } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';

function PlansListPage() {
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paginationMeta, setPaginationMeta] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');

    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            navigate(location.pathname, { replace: true, state: {} });
        }
    }, [location.state, navigate, location.pathname]);

    const fetchPlans = useCallback(async (page = 1) => {
        setLoading(true);
        setError(null);
        try {
            const response = await getPlans({ page });
            setPlans(response.data.data);
            setPaginationMeta(response.data.meta);
        } catch (err) {
            console.error("Failed to fetch plans:", err);
            setError('Failed to load plans.');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchPlans(paginationMeta?.current_page || 1);
    }, [fetchPlans]);

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this plan? Users assigned to it will lose their plan association.')) {
            try {
                await deletePlan(id);
                setSuccessMessage('Plan deleted successfully!');
                fetchPlans(paginationMeta?.current_page || 1); // Refresh list
            } catch (err) {
                console.error("Failed to delete plan:", err);
                // Handle specific error from API (like 409 Conflict if prevented)
                if (err.response && err.response.status === 409) {
                     setError(err.response.data.error || 'Cannot delete plan with assigned users.');
                } else {
                    setError('Failed to delete plan.');
                }
                setSuccessMessage('');
            }
        }
    };

    const handlePageChange = (newPage) => {
        fetchPlans(newPage);
    };

    return (
        <div>
            {/* Content Header */}
            <div className="content-header">
                <div className="container-fluid">
                    <div className="row mb-2">
                        <div className="col-sm-6"><h1 className="m-0">Plans</h1></div>
                        <div className="col-sm-6">
                            <ol className="breadcrumb float-sm-right">
                                <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                <li className="breadcrumb-item active">Plans</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <section className="content">
                <div className="card">
                    <div className="card-header">
                        <h3 className="card-title">Plan Management</h3>
                        <div className="card-tools">
                            <Link to="/plans/create" className="btn btn-success btn-sm">
                                <i className="fas fa-plus mr-1"></i> Add New Plan
                            </Link>
                        </div>
                    </div>
                    <div className="card-body">
                        {error && <div className="alert alert-danger">{error}</div>}
                        {successMessage && <div className="alert alert-success">{successMessage}</div>}

                        {loading ? (
                            <LoadingSpinner />
                        ) : (
                            <>
                                <div className="table-responsive">
                                    <table className="table table-bordered table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Plan Name</th>
                                                <th>Months</th>
                                                <th>Created At</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {plans.length > 0 ? plans.map(plan => (
                                                <tr key={plan.id}>
                                                    <td>{plan.id}</td>
                                                    <td>{plan.plan_name}</td>
                                                    <td>{plan.months}</td>
                                                    <td>{new Date(plan.created_at).toLocaleDateString()}</td>
                                                    <td>
                                                        <Link to={`/plans/edit/${plan.id}`} className="btn btn-info btn-sm mr-1" title="Edit">
                                                            <i className="fas fa-edit"></i>
                                                        </Link>
                                                        <button
                                                            onClick={() => handleDelete(plan.id)}
                                                            className="btn btn-danger btn-sm"
                                                            title="Delete"
                                                        >
                                                            <i className="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            )) : (
                                                <tr>
                                                    <td colSpan="5" className="text-center text-muted">No plans found.</td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-3">
                                    <Pagination meta={paginationMeta} onPageChange={handlePageChange} />
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </section>
        </div>
    );
}

export default PlansListPage;