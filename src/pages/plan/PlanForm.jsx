// src/pages/plan/PlanForm.js
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

function PlanForm({ onSubmit, initialData = null, isEditing = false, isLoading = false, errors = {} }) {
    const initialFormData = { plan_name: '', months: '' };
    const [formData, setFormData] = useState(initialFormData);

    // Populate form when initialData is provided (for editing)
    useEffect(() => {
        if (isEditing && initialData) {
            setFormData({
                plan_name: initialData.plan_name || '',
                months: initialData.months || '',
            });
        } else if (!isEditing) {
            setFormData(initialFormData);
        }
    }, [initialData, isEditing]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (isLoading) return;
        onSubmit(formData);
    };

    const displayError = (field) => {
        return errors?.[field] ? <span className="invalid-feedback d-block">{errors[field].join(', ')}</span> : null;
    }

    return (
        <form onSubmit={handleSubmit} noValidate>
            <div className="form-group mb-3">
                <label htmlFor="plan_name">Plan Name <span className="text-danger">*</span></label>
                <input
                    type="text"
                    id="plan_name"
                    name="plan_name"
                    className={`form-control ${errors?.plan_name ? 'is-invalid' : ''}`}
                    value={formData.plan_name}
                    onChange={handleChange}
                    required
                    disabled={isLoading}
                />
                {displayError('plan_name')}
            </div>

            <div className="form-group mb-3">
                <label htmlFor="months">Duration (Months) <span className="text-danger">*</span></label>
                <input
                    type="text" // Keep as text to match schema varchar, or change to number if schema changes
                    id="months"
                    name="months"
                    className={`form-control ${errors?.months ? 'is-invalid' : ''}`}
                    value={formData.months}
                    onChange={handleChange}
                    required
                    disabled={isLoading}
                    placeholder="e.g., 12 or 6"
                />
                {displayError('months')}
            </div>

            <div className="row mt-3">
                <div className="col-12">
                    <Link to="/plans" className="btn btn-secondary mr-2" disabled={isLoading}>Cancel</Link>
                    <button type="submit" className="btn btn-primary" disabled={isLoading}>
                        {isLoading ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Plan' : 'Create Plan')}
                    </button>
                </div>
            </div>
        </form>
    );
}

export default PlanForm;