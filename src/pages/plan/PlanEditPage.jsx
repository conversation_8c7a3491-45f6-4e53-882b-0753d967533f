// src/pages/plan/PlanEditPage.js
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { getPlan, updatePlan } from '../../services/api';
import PlanForm from './PlanForm';
import LoadingSpinner from '../../components/common/LoadingSpinner';

function PlanEditPage() {
    const { planId } = useParams();
    const navigate = useNavigate();

    const [plan, setPlan] = useState(null);
    const [loading, setLoading] = useState(true);
    const [submitLoading, setSubmitLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [generalError, setGeneralError] = useState(null);

    const fetchPlanData = useCallback(async () => {
        setLoading(true);
        setErrors({});
        setGeneralError(null);
        try {
            const response = await getPlan(planId);
            setPlan(response.data.data); // Assuming API returns { data: plan }
        } catch (err) {
            console.error("Failed to fetch plan:", err);
            setGeneralError('Failed to load plan data.');
            setPlan(null);
        } finally {
            setLoading(false);
        }
    }, [planId]);

    useEffect(() => {
        fetchPlanData();
    }, [fetchPlanData]);

    const handleSubmit = async (formData) => {
        setSubmitLoading(true);
        setErrors({});
        setGeneralError(null);
        try {
            await updatePlan(planId, formData);
            navigate('/plans', { state: { message: 'Plan updated successfully!' } });
        } catch (err) {
             console.error("Failed to update plan:", err.response?.data);
             if (err.response && err.response.status === 422) {
                  setErrors(err.response.data.errors);
                  setGeneralError('Please check the form for errors.');
             } else {
                 setGeneralError(err.response?.data?.message || 'An unexpected error occurred while updating.');
             }
             setSubmitLoading(false);
        }
    };

    if (loading) {
        return <LoadingSpinner />;
    }

    if (generalError && !plan) {
        return (
             <div>
                <div className="content-header">
                    <div className="container-fluid">
                         <h1 className="m-0">Edit Plan</h1>
                         <div className="alert alert-danger mt-3">{generalError}</div>
                         <Link to="/plans" className="btn btn-secondary mt-2">Back to Plans List</Link>
                     </div>
                </div>
            </div>
        );
    }

    return (
        <div>
            {/* Content Header */}
             <div className="content-header">
                 <div className="container-fluid">
                     <div className="row mb-2">
                         <div className="col-sm-6"><h1 className="m-0">Edit Plan: {plan?.plan_name}</h1></div>
                         <div className="col-sm-6">
                             <ol className="breadcrumb float-sm-right">
                                 <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                 <li className="breadcrumb-item"><Link to="/plans">Plans</Link></li>
                                 <li className="breadcrumb-item active">Edit Plan</li>
                             </ol>
                         </div>
                     </div>
                 </div>
             </div>

             {/* Main Content */}
             <section className="content">
                <div className="card card-primary">
                     <div className="card-header"><h3 className="card-title">Edit Plan Details</h3></div>
                     <div className="card-body">
                        {generalError && !errors.length && <div className="alert alert-danger">{generalError}</div>}
                        {plan && (
                            <PlanForm
                                onSubmit={handleSubmit}
                                initialData={plan}
                                isEditing={true}
                                isLoading={submitLoading}
                                errors={errors}
                            />
                        )}
                     </div>
                 </div>
             </section>
        </div>
    );
}

export default PlanEditPage;