// src/pages/plan/PlanCreatePage.js
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { createPlan } from '../../services/api';
import PlanForm from './PlanForm';

function PlanCreatePage() {
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [generalError, setGeneralError] = useState(null);

    const handleSubmit = async (formData) => {
        setIsLoading(true);
        setErrors({});
        setGeneralError(null);
        try {
            await createPlan(formData);
            navigate('/plans', { state: { message: 'Plan created successfully!' } });
        } catch (err) {
            console.error("Failed to create plan:", err.response?.data);
            if (err.response && err.response.status === 422) {
                 setErrors(err.response.data.errors);
                 setGeneralError('Please check the form for errors.');
            } else {
                setGeneralError(err.response?.data?.message || 'An unexpected error occurred.');
            }
            setIsLoading(false);
        }
    };

    return (
        <div>
            {/* Content Header */}
            <div className="content-header">
                 <div className="container-fluid">
                     <div className="row mb-2">
                         <div className="col-sm-6"><h1 className="m-0">Add New Plan</h1></div>
                         <div className="col-sm-6">
                             <ol className="breadcrumb float-sm-right">
                                 <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                 <li className="breadcrumb-item"><Link to="/plans">Plans</Link></li>
                                 <li className="breadcrumb-item active">Add Plan</li>
                             </ol>
                         </div>
                     </div>
                 </div>
             </div>

            {/* Main Content */}
            <section className="content">
                <div className="card card-primary">
                    <div className="card-header"><h3 className="card-title">Plan Details</h3></div>
                    <div className="card-body">
                       {generalError && <div className="alert alert-danger">{generalError}</div>}
                       <PlanForm
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            errors={errors}
                            isEditing={false}
                       />
                    </div>
                </div>
            </section>
        </div>
    );
}

export default PlanCreatePage;