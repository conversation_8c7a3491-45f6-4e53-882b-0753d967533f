export const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// This function wraps text nodes in spans, preserving the HTML structure.
export const wrapWordsInSpans = (node, wordCounter) => {
    if (node.nodeType === Node.TEXT_NODE) {
        const words = node.textContent.split(/(\s+)/); // Split by spaces, keeping them
        const fragment = document.createDocumentFragment();
        words.forEach(word => {
            if (word.trim().length > 0) {
                const span = document.createElement('span');
                span.id = `word-${wordCounter.count++}`;
                span.textContent = word;
                fragment.appendChild(span);
            } else {
                fragment.appendChild(document.createTextNode(word)); // Preserve spaces
            }
        });
        return fragment;
    }

    const newNode = node.cloneNode(false);
    node.childNodes.forEach(child => {
        newNode.appendChild(wrapWordsInSpans(child, wordCounter));
    });
    return newNode;
};

export const insertRawTextIntoQuill = (quillRef, textToInsert) => {
    if (!quillRef.current || textToInsert === null || textToInsert === undefined || textToInsert === '') return;
    const quill = quillRef.current.getEditor();
    const range = quill.getSelection(true);
    let index = range ? range.index : quill.getLength() -1;
    quill.insertText(index, textToInsert, 'user');
    quill.setSelection(index + textToInsert.length, 0, 'user');
};