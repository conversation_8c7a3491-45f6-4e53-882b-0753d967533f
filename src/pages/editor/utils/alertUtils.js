import { useState, useCallback } from 'react';

export const useAlerts = () => {
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);

    const showSuccess = useCallback((message, duration = 3000) => {
        setError(null);
        setSuccessMessage(message);
        setTimeout(() => setSuccessMessage(null), duration);
    }, []);

    const showError = useCallback((message) => {
        setSuccessMessage(null);
        setError(message);
    }, []);

    const clearAlerts = useCallback(() => {
        setError(null);
        setSuccessMessage(null);
    }, []);

    return { error, successMessage, showSuccess, showError, clearAlerts };
};