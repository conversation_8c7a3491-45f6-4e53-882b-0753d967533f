import React from 'react';
import styles from '../main.module.css';

const FontConversionModal = ({
    isVisible,
    onClose,
    selectedFont,
    conversionOutput,
    onCopyAndClose,
}) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={styles.modalContentWrapper} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-font mr-2"></i> Converted to {selectedFont.toUpperCase()}</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.conversionOutputDisplay}>
                        <strong>Output:</strong>
                        <textarea className={styles.controlGroupTextarea} readOnly value={conversionOutput} rows="5"></textarea>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onCopyAndClose} className={`${styles.actionButton} ${styles.actionButtonSuccess}`}> <i className="fas fa-copy mr-1"></i> Copy and Close </button>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>Close</button>
                </div>
            </div>
        </div>
    );
};

export default FontConversionModal;