import React from 'react';
import LoadingSpinner from '../../../components/common/LoadingSpinner';
import styles from '../main.module.css';

const GenerateDraftModal = ({
    isVisible,
    onClose,
    draftIdea,
    setDraftIdea,
    onConfirmGenerateDraft,
    isGeneratingDraft
}) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={styles.modalContentWrapper} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-pencil-alt mr-2"></i> Generate Draft</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.controlGroup}>
                        <label htmlFor="draftIdea">Enter your idea or topic:</label>
                        <textarea
                            id="draftIdea"
                            className={styles.controlGroupTextarea}
                            value={draftIdea}
                            onChange={(e) => setDraftIdea(e.target.value)}
                            rows="5"
                            placeholder="e.g., 'A short story about a detective in a futuristic city' or 'An essay on the impact of AI on society'"
                        ></textarea>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onConfirmGenerateDraft} className={`${styles.actionButton} ${styles.actionButtonPrimary}`} disabled={isGeneratingDraft || !draftIdea.trim()}>
                        {isGeneratingDraft ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-magic mr-1"></i> Generate</>}
                    </button>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>Cancel</button>
                </div>
            </div>
        </div>
    );
};

export default GenerateDraftModal;