import React from 'react';
import styles from '../main.module.css';

const TranslationModal = ({
    isVisible,
    onClose,
    translatedText,
    onCopyAndClose,
}) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={styles.modalContentWrapper} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-language mr-2"></i> Translation Result</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.translationOutput}>
                        <strong>Translated Text:</strong>
                        <textarea className={styles.controlGroupTextarea} readOnly value={translatedText} rows="10"></textarea>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onCopyAndClose} className={`${styles.actionButton} ${styles.actionButtonSuccess}`}> <i className="fas fa-copy mr-1"></i> Copy and Close </button>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>Close</button>
                </div>
            </div>
        </div>
    );
};

export default TranslationModal;