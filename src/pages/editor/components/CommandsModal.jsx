import React from 'react';
import styles from '../main.module.css';
import { COMMANDS } from '../constants/speechConstants';

const CommandsModal = ({ isVisible, onClose, commandTab, selectCommandTab }) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={styles.modalContentWrapper} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-terminal mr-2"></i> Voice Commands</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.modalTabs}>
                        {Object.keys(COMMANDS).map(lang => (
                            <button key={lang} className={commandTab === lang ? styles.active : ''} onClick={() => selectCommandTab(lang)}> {lang.toUpperCase()} </button>
                        ))}
                    </div>
                    <div className={styles.tabContent}>
                        {COMMANDS[commandTab] && (
                            <table className={styles.modalTable}>
                                <thead><tr><th>Action/Symbol</th><th>Say this command</th></tr></thead>
                                <tbody>
                                    {COMMANDS[commandTab].map((command, index) => (
                                        <tr key={index}>
                                            <td><strong>{command.symbol || (command.action === 'newline' ? '(New Line)' : command.action === 'newparagraph' ? '(New Paragraph)' : command.action === 'stopMic' ? '(Stop Listening)' : command.action === 'clearEditor' ? '(Clear Editor)' : command.action || 'N/A')}</strong></td>
                                            <td>"{command.text}"</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                        <p style={{ marginTop: '15px', fontSize: '0.9rem', color: '#555' }}>
                            Commands must be spoken clearly. They may be more reliable when spoken as a distinct phrase.
                        </p>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>Close</button>
                </div>
            </div>
        </div>
    );
};

export default CommandsModal;