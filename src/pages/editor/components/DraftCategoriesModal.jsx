import React from 'react';
import styles from '../main.module.css';

const DRAFT_CATEGORIES = [
    { id: 'matrimonial', title: 'Matrimonial', icon: 'fas fa-heart' },
    { id: 'motor-vehicle', title: 'Motor Vehicle Act', icon: 'fas fa-car' },
    { id: 'negotiable-instrument', title: 'Negotiable Instrument', icon: 'fas fa-file-invoice-dollar' },
    { id: 'legal-notice', title: 'Legal Notice', icon: 'fas fa-gavel' },
    { id: 'power-attorney', title: 'Power of Attorney', icon: 'fas fa-handshake' },
    { id: 'special-leave', title: 'Special Leave Petition', icon: 'fas fa-balance-scale' },
    { id: 'adoption', title: 'Adoption Deeds', icon: 'fas fa-baby' },
    { id: 'specific-relief', title: 'Specific Relief Act', icon: 'fas fa-clipboard-check' },
    { id: 'affidavits', title: 'Affidavits', icon: 'fas fa-stamp' },
    { id: 'will-gift', title: 'Will & Gift Deeds', icon: 'fas fa-gift' },
    { id: 'agreements', title: 'Agreements', icon: 'fas fa-file-contract' },
    { id: 'appointments', title: 'Appointments', icon: 'fas fa-user-tie' },
    { id: 'criminal-pleadings', title: 'Criminal Pleadings', icon: 'fas fa-user-secret' },
    { id: 'civil-pleadings', title: 'Civil Pleadings', icon: 'fas fa-users' },
    { id: 'arbitration', title: 'Arbitration', icon: 'fas fa-handshake-angle' },
    { id: 'banking', title: 'Banking', icon: 'fas fa-university' },
    { id: 'bonds', title: 'Bonds', icon: 'fas fa-certificate' },
    { id: 'rent', title: 'Rent', icon: 'fas fa-home' },
    { id: 'writs', title: 'Writs', icon: 'fas fa-scroll' }
];

const DraftCategoriesModal = ({ isVisible, onClose, onCategorySelect }) => {
    if (!isVisible) return null;

    const handleCategoryClick = (category) => {
        onCategorySelect(category);
        onClose();
    };

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={`${styles.modalContentWrapper} ${styles.draftCategoriesModal}`} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-file-alt mr-2"></i> Select Draft Category</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.draftCategoriesGrid}>
                        {DRAFT_CATEGORIES.map((category) => (
                            <div
                                key={category.id}
                                className={styles.draftCategoryCard}
                                onClick={() => handleCategoryClick(category)}
                            >
                                <div className={styles.draftCategoryIcon}>
                                    <i className={category.icon}></i>
                                </div>
                                <div className={styles.draftCategoryTitle}>
                                    {category.title}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DraftCategoriesModal;