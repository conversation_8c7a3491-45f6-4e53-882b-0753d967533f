import React from 'react';
import LoadingSpinner from '../../../components/common/LoadingSpinner';
import styles from '../main.module.css';

const SaveContentModal = ({
    isVisible,
    onClose,
    contentTitle,
    setContentTitle,
    contentDescription,
    setContentDescription,
    onConfirmSave,
    isSavingContent
}) => {
    if (!isVisible) return null;

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={styles.modalContentWrapper} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className="fas fa-save mr-2"></i> Save Content</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.controlGroup}>
                        <label htmlFor="contentTitle">Title:</label>
                        <input
                            type="text"
                            id="contentTitle"
                            className={styles.formControl}
                            value={contentTitle}
                            onChange={(e) => setContentTitle(e.target.value)}
                            placeholder="Enter a title for your content"
                            required
                        />
                    </div>
                    <div className={styles.controlGroup} style={{ marginTop: '15px' }}>
                        <label htmlFor="contentDescription">Description (Optional):</label>
                        <textarea
                            id="contentDescription"
                            className={styles.controlGroupTextarea}
                            value={contentDescription}
                            onChange={(e) => setContentDescription(e.target.value)}
                            rows="3"
                            placeholder="Add a brief description"
                        ></textarea>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onConfirmSave} className={`${styles.actionButton} ${styles.actionButtonPrimary}`} disabled={isSavingContent || !contentTitle.trim()}>
                        {isSavingContent ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-save mr-1"></i> Save</>}
                    </button>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>Cancel</button>
                </div>
            </div>
        </div>
    );
};

export default SaveContentModal;