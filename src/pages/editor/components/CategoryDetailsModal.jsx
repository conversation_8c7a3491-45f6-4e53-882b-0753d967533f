import { useState, useEffect } from 'react';
import styles from '../main.module.css';

// Legal document templates with detailed formats
const CATEGORY_TEMPLATES = {
    'matrimonial': {
        title: 'Matrimonial: Mutual Consent Divorce Petition',
        description: 'Joint petition filed by both husband and wife for dissolution of marriage by mutual consent under Hindu Marriage Act, 1955.',
        template: `BEFORE THE HON'BLE PRINCIPAL JUDGE, FAMILY COURT AT [CITY]
H.M.A. PETITION NO. _______ OF 2024

IN THE MATTER OF:
[Husband Full Name] S/o [Husband Father Name], aged [Husband Age] years, R/o [Husband Address] ... Petitioner No. 1
AND
[Wife Full Name] D/o [Wife Father Name], aged [Wife Age] years, R/o [Wife Address] ... Petitioner No. 2

JOINT PETITION UNDER SECTION 13B(1) OF THE HINDU MARRIAGE ACT, 1955 FOR DISSOLUTION OF MARRIAGE BY MUTUAL CONSENT

TO THE HON'BLE COURT

The Petitioners above-named most respectfully submit as under:

1. MARRIAGE DETAILS:
   That the marriage between Petitioner No. 1 (Husband) and Petitioner No. 2 (Wife) was solemnized on [Date of Marriage] at [Place of Marriage] according to Hindu rites and ceremonies. The marriage was registered under the Hindu Marriage Act, 1955. A certified copy of the Marriage Certificate is annexed herewith as Annexure P-1.

2. COHABITATION:
   That after the marriage, both Petitioners cohabited as husband and wife at [Last Place of Residence]. [Children Details - e.g., "There are no children born out of this wedlock" or "One child namely [Child Name] aged [Child Age] years was born out of this wedlock whose custody has been mutually agreed upon"].

3. SEPARATION:
   That due to irreconcilable differences, temperamental incompatibility, and divergent views on various aspects of life, the Petitioners have been living separately since [Date of Separation]. Despite sincere efforts by family members and friends, all attempts at reconciliation have failed.

4. IRRETRIEVABLE BREAKDOWN:
   That for more than [Separation Period], there has been no cohabitation between the Petitioners, and the marriage has irretrievably broken down. There is no possibility of them living together as husband and wife again.

5. MUTUAL CONSENT:
   That the Petitioners have mutually and amicably agreed to dissolve their marriage. They have settled all their claims regarding:
   a) Alimony and maintenance (past, present, and future)
   b) Stridhan and personal belongings
   c) Any other matrimonial claims
   [Settlement Details - e.g., "It has been mutually agreed that no alimony shall be paid by either party" or "A lump sum settlement of Rs. [Amount] has been paid by Petitioner No. 1 to Petitioner No. 2 as full and final settlement"]

6. VOLUNTARY CONSENT:
   That this petition is not being filed in collusion, fraud, or with any coercion or undue influence. The consent for divorce is free, voluntary, and after due deliberation.

7. JURISDICTION:
   That this Hon'ble Court has the territorial and pecuniary jurisdiction to entertain and try this petition as:
   a) The marriage was solemnized within the jurisdiction of this court, and/or
   b) The parties last resided together within the jurisdiction of this court, and/or
   c) The Respondent resides within the jurisdiction of this court.

8. LIMITATION:
   That this petition is being filed within the period of limitation and there is no delay in filing the same.

PRAYER

In the premises aforesaid, it is most respectfully prayed that this Hon'ble Court may be pleased to:

a) Grant a decree of divorce by mutual consent under Section 13B of the Hindu Marriage Act, 1955, dissolving the marriage between Petitioner No. 1 and Petitioner No. 2;
b) Pass any other order or direction as this Hon'ble Court may deem fit and proper in the circumstances of the case.

PETITIONER NO. 1                    PETITIONER NO. 2
(Husband)                           (Wife)

[Husband Signature]                 [Wife Signature]
[Husband Full Name]                 [Wife Full Name]

THROUGH
[Advocate Signature]
[Advocate Name]
[Advocate Designation]
Advocate for Petitioners

VERIFICATION:
We, the Petitioners above-named, do hereby verify that the contents of paragraphs 1 to 8 of the above petition are true to our knowledge and belief and no part of it is false and nothing material has been concealed therefrom.

Verified at [City] on this [Date] day of [Month], [Year].

PETITIONER NO. 1                    PETITIONER NO. 2
[Husband Signature]                 [Wife Signature]
[Husband Full Name]                 [Wife Full Name]`,
        fields: [
            { label: 'City/Court Location', placeholder: 'e.g., Delhi, Mumbai, Bangalore' },
            { label: 'Husband Full Name', placeholder: 'Full name of husband' },
            { label: 'Husband Father Name', placeholder: 'Father\'s name of husband' },
            { label: 'Husband Age', placeholder: 'Age of husband' },
            { label: 'Husband Address', placeholder: 'Complete residential address of husband' },
            { label: 'Wife Full Name', placeholder: 'Full name of wife' },
            { label: 'Wife Father Name', placeholder: 'Father\'s name of wife' },
            { label: 'Wife Age', placeholder: 'Age of wife' },
            { label: 'Wife Address', placeholder: 'Complete residential address of wife' },
            { label: 'Date of Marriage', placeholder: 'DD/MM/YYYY' },
            { label: 'Place of Marriage', placeholder: 'City/Location where marriage took place' },
            { label: 'Last Place of Residence', placeholder: 'Last place where couple lived together' },
            { label: 'Children Details', placeholder: 'Details about children or mention no children' },
            { label: 'Date of Separation', placeholder: 'DD/MM/YYYY' },
            { label: 'Separation Period', placeholder: 'e.g., one year, two years' },
            { label: 'Settlement Details', placeholder: 'Details of financial settlement or mention no alimony claimed' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Advocate Designation', placeholder: 'e.g., Advocate, Senior Advocate' },
            { label: 'Date', placeholder: 'Day DD' },
            { label: 'Month', placeholder: 'Month name' },
            { label: 'Year', placeholder: 'YYYY' }
        ]
    },
    'motor-vehicle': {
        title: 'Motor Vehicle Act: Claim Petition',
        description: 'Petition for compensation under Motor Vehicle Act for accident claims.',
        template: `BEFORE THE MOTOR ACCIDENT CLAIMS TRIBUNAL AT [CITY]
M.A.C.P. NO. _______ OF 2024

IN THE MATTER OF:
[Claimant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Claimant
VERSUS
1. [Driver Name], R/o [Address] ... Respondent No. 1
2. [Owner Name], R/o [Address] ... Respondent No. 2
3. [Insurance Company Name] ... Respondent No. 3

PETITION UNDER SECTION 166 OF THE MOTOR VEHICLES ACT, 1988

The Claimant respectfully submits as under:

1. That on [Date of Accident] at about [Time] hours, the deceased/injured [Name] was [describe circumstances] when a [Vehicle Type] bearing registration no. [Registration Number] being driven by Respondent No. 1 in a rash and negligent manner hit the deceased/injured.

2. That due to the said accident, the deceased/injured sustained grievous injuries and was immediately rushed to [Hospital Name] where he/she was declared dead/treated for injuries.

3. That the deceased was [Age] years old and was earning Rs. [Monthly Income] per month as [Occupation].

4. That the vehicle was insured with Respondent No. 3 and the policy was valid at the time of accident.

5. That the accident occurred solely due to the rash and negligent driving of Respondent No. 1.

PRAYER
It is therefore prayed that this Hon'ble Tribunal may be pleased to:
a) Award compensation of Rs. [Amount] to the claimant.
b) Direct the respondents to pay the awarded amount with interest.
c) Pass any other order as deemed fit.

[Claimant Signature]
CLAIMANT

THROUGH
[Advocate Name]
Advocate for Claimant`,
        fields: [
            { label: 'City/Tribunal Location', placeholder: 'e.g., Delhi, Mumbai' },
            { label: 'Claimant Name', placeholder: 'Name of person filing claim' },
            { label: 'Claimant Address', placeholder: 'Complete address' },
            { label: 'Date of Accident', placeholder: 'DD/MM/YYYY' },
            { label: 'Time of Accident', placeholder: 'e.g., 10:30 AM' },
            { label: 'Deceased/Injured Name', placeholder: 'Name of victim' },
            { label: 'Vehicle Registration Number', placeholder: 'e.g., DL-01-AB-1234' },
            { label: 'Driver Name', placeholder: 'Name of driver' },
            { label: 'Vehicle Owner Name', placeholder: 'Name of vehicle owner' },
            { label: 'Insurance Company', placeholder: 'Name of insurance company' },
            { label: 'Hospital Name', placeholder: 'Where victim was taken' },
            { label: 'Victim Age', placeholder: 'Age at time of accident' },
            { label: 'Monthly Income', placeholder: 'Monthly earning of victim' },
            { label: 'Occupation', placeholder: 'Job/profession of victim' },
            { label: 'Compensation Amount', placeholder: 'Amount being claimed' }
        ]
    },
    'negotiable-instrument': {
        title: 'Negotiable Instrument Act: Cheque Bounce Case',
        description: 'Criminal complaint under Section 138 of the Negotiable Instruments Act for dishonor of cheque.',
        template: `BEFORE THE JUDICIAL MAGISTRATE FIRST CLASS AT [CITY]
CRIMINAL COMPLAINT CASE NO. _______ OF 2024

IN THE MATTER OF:
[Complainant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Complainant
VERSUS
[Accused Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Accused

CRIMINAL COMPLAINT UNDER SECTION 138 OF THE NEGOTIABLE INSTRUMENTS ACT, 1881

The Complainant respectfully submits as under:

1. That the complainant is a [Occupation/Business] and the accused is known to the complainant.

2. That on [Date of Transaction], the accused approached the complainant for financial assistance of Rs. [Amount] for [Purpose of Loan]. The complainant, believing in the good faith of the accused, advanced the said amount.

3. That in lieu of the said amount, the accused issued cheque no. [Cheque Number] dated [Cheque Date] for Rs. [Cheque Amount] drawn on [Bank Name], [Branch Name] in favor of the complainant.

4. That the complainant presented the said cheque for encashment on [Presentation Date] through his/her bank account, but the same was dishonored on [Dishonor Date] with the reason "[Reason for Dishonor]".

5. That immediately after the dishonor, the complainant issued a legal notice dated [Notice Date] through registered post to the accused demanding payment of the cheque amount within 15 days. The said notice was served on the accused on [Service Date].

6. That despite service of the legal notice and expiry of the statutory period of 15 days, the accused has failed to make payment of the cheque amount.

7. That the accused has thereby committed an offense punishable under Section 138 of the Negotiable Instruments Act, 1881.

PRAYER
It is therefore prayed that this Hon'ble Court may be pleased to:
a) Take cognizance of the offense committed by the accused.
b) Issue summons to the accused and try him/her for the offense.
c) Award appropriate punishment and compensation.
d) Pass any other order as deemed fit.

[Complainant Signature]
COMPLAINANT

THROUGH
[Advocate Name]
Advocate for Complainant

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above complaint are true to my knowledge and belief.

[Complainant Signature]
COMPLAINANT`,
        fields: [
            { label: 'City/Court Location', placeholder: 'e.g., Delhi, Mumbai' },
            { label: 'Complainant Name', placeholder: 'Name of person filing complaint' },
            { label: 'Complainant Address', placeholder: 'Complete address of complainant' },
            { label: 'Accused Name', placeholder: 'Name of person who issued cheque' },
            { label: 'Accused Address', placeholder: 'Complete address of accused' },
            { label: 'Complainant Occupation', placeholder: 'e.g., Businessman, Service' },
            { label: 'Date of Transaction', placeholder: 'DD/MM/YYYY' },
            { label: 'Loan Amount', placeholder: 'Amount given as loan' },
            { label: 'Purpose of Loan', placeholder: 'Reason for taking loan' },
            { label: 'Cheque Number', placeholder: 'Cheque number' },
            { label: 'Cheque Date', placeholder: 'Date on cheque DD/MM/YYYY' },
            { label: 'Cheque Amount', placeholder: 'Amount written on cheque' },
            { label: 'Bank Name', placeholder: 'Name of bank' },
            { label: 'Branch Name', placeholder: 'Bank branch name' },
            { label: 'Presentation Date', placeholder: 'Date cheque presented DD/MM/YYYY' },
            { label: 'Dishonor Date', placeholder: 'Date cheque dishonored DD/MM/YYYY' },
            { label: 'Reason for Dishonor', placeholder: 'e.g., Insufficient Funds, Account Closed' },
            { label: 'Notice Date', placeholder: 'Date of legal notice DD/MM/YYYY' },
            { label: 'Service Date', placeholder: 'Date notice served DD/MM/YYYY' }
        ]
    },
    'legal-notice': {
        title: 'Legal Notice',
        description: 'Comprehensive legal notice for civil matters, breach of contract, recovery of money, or other legal demands.',
        template: `LEGAL NOTICE

TO:
[Recipient Name]
S/o/D/o/W/o [Recipient Father/Husband Name]
Aged [Recipient Age] years
R/o [Recipient Address]

THROUGH: [Advocate Name]
[Advocate Address]
[Advocate Contact Details]

Subject: Legal Notice for [Subject Matter] - [Notice Type]

Sir/Madam,

I, [Client Name], S/o/D/o/W/o [Client Father/Husband Name], aged [Client Age] years, R/o [Client Address], through my advocate, do hereby serve upon you this Legal Notice under [Applicable Law] and state as under:

1. PARTIES AND RELATIONSHIP:
   That my client is [Client Description] and you are [Recipient Description]. [Relationship Description].

2. BACKGROUND FACTS:
   That [Background Facts - describe the relationship/transaction between parties in detail].

3. TRANSACTION/AGREEMENT DETAILS:
   That on [Transaction Date], [Transaction Details]. [Agreement/Contract Details if any].

4. BREACH/DEFAULT:
   That [Main Issue - describe the specific problem/breach/default in detail]. You were legally bound to [Legal Obligation] but you have [Specific Breach].

5. CONSEQUENCES AND DAMAGES:
   That due to your [Breach/Default], my client has suffered [Consequences - describe the loss/damage caused] amounting to Rs. [Damage Amount].

6. LEGAL POSITION:
   That [Legal Position - mention relevant law/contract clause/legal provisions]. Your act/omission constitutes [Legal Violation].

7. DEMAND:
   That you are hereby called upon to [Specific Demand - what action is required] within [Time Period] days from the receipt of this notice, failing which my client shall be constrained to initiate appropriate legal proceedings against you for:
   a) Recovery of Rs. [Amount] with interest @ [Interest Rate]% per annum
   b) Damages and compensation
   c) Costs of legal proceedings
   d) Any other relief as deemed fit

8. CONSEQUENCES OF NON-COMPLIANCE:
   That this notice is served upon you with a bona fide intention to avoid unnecessary litigation. However, if you fail to comply with the demand made herein within the stipulated time, my client reserves the right to take appropriate legal action against you without any further notice.

TAKE NOTICE that if you fail to [Specific Action Required] within the aforesaid period of [Time Period] days, my client will be left with no option but to approach the competent Court of Law for appropriate relief including recovery of the amount, damages, interest, and costs, and you will be liable for all legal consequences.

This notice is issued without prejudice to any other rights and remedies available to my client under law.

Dated: [Date]
Place: [Place]

[Advocate Signature]
[Advocate Name]
[Advocate Designation]
Advocate for [Client Name]

ACKNOWLEDGMENT:
Received the above notice on ____________
Signature: _______________`,
        fields: [
            { label: 'Recipient Name', placeholder: 'Name of person receiving notice' },
            { label: 'Recipient Father/Husband Name', placeholder: 'Father\'s or husband\'s name of recipient' },
            { label: 'Recipient Age', placeholder: 'Age of recipient' },
            { label: 'Recipient Address', placeholder: 'Complete address of recipient' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Advocate Address', placeholder: 'Address of advocate' },
            { label: 'Advocate Contact Details', placeholder: 'Phone/email of advocate' },
            { label: 'Subject Matter', placeholder: 'Brief subject of notice' },
            { label: 'Notice Type', placeholder: 'e.g., Recovery, Breach of Contract, Defamation' },
            { label: 'Client Name', placeholder: 'Name of person sending notice' },
            { label: 'Client Father/Husband Name', placeholder: 'Father\'s or husband\'s name of client' },
            { label: 'Client Age', placeholder: 'Age of client' },
            { label: 'Client Address', placeholder: 'Complete address of client' },
            { label: 'Applicable Law', placeholder: 'e.g., Contract Act, Consumer Protection Act' },
            { label: 'Client Description', placeholder: 'Description/occupation of client' },
            { label: 'Recipient Description', placeholder: 'Description/occupation of recipient' },
            { label: 'Relationship Description', placeholder: 'Relationship between parties' },
            { label: 'Background Facts', placeholder: 'Detailed background facts' },
            { label: 'Transaction Date', placeholder: 'Date of transaction/agreement DD/MM/YYYY' },
            { label: 'Transaction Details', placeholder: 'Details of transaction/agreement' },
            { label: 'Legal Obligation', placeholder: 'What recipient was legally bound to do' },
            { label: 'Specific Breach', placeholder: 'Specific breach or default' },
            { label: 'Damage Amount', placeholder: 'Amount of loss/damage' },
            { label: 'Legal Violation', placeholder: 'Legal violation committed' },
            { label: 'Specific Demand', placeholder: 'What action is required from recipient' },
            { label: 'Time Period', placeholder: 'Number of days to comply (e.g., 15, 30)' },
            { label: 'Amount', placeholder: 'Amount to be recovered' },
            { label: 'Interest Rate', placeholder: 'Interest rate (e.g., 12, 18)' },
            { label: 'Specific Action Required', placeholder: 'Specific action required for compliance' },
            { label: 'Date', placeholder: 'Date of notice DD/MM/YYYY' },
            { label: 'Place', placeholder: 'Place where notice is issued' },
            { label: 'Advocate Designation', placeholder: 'e.g., Advocate, Senior Advocate' }
        ]
    },
    'power-attorney': {
        title: 'Power of Attorney',
        description: 'General Power of Attorney document authorizing someone to act on your behalf.',
        template: `GENERAL POWER OF ATTORNEY

KNOW ALL MEN BY THESE PRESENTS that I, [Principal Name], S/o/D/o/W/o [Father's/Husband's Name], aged [Age] years, R/o [Principal Address], do hereby nominate, constitute and appoint [Attorney Name], S/o/D/o/W/o [Attorney Father's/Husband's Name], aged [Attorney Age] years, R/o [Attorney Address], as my true and lawful Attorney to act for me and on my behalf and in my name to do all or any of the following acts, deeds and things:

1. To purchase, sell, exchange, lease, mortgage, transfer or otherwise deal with any movable or immovable property belonging to me.

2. To execute, sign and deliver any deed, document, agreement, contract or instrument relating to any property.

3. To appear before any Court, Tribunal, Authority or Office and represent me in any legal proceedings.

4. To receive, collect and give receipts for any money, rent, income or other dues payable to me.

5. To operate my bank accounts, withdraw money, sign cheques and conduct banking transactions.

6. To pay taxes, bills, dues and discharge any liability on my behalf.

7. To engage advocates, counsel and other professionals for legal matters.

8. To do all other acts and things which I could do if personally present.

I hereby ratify and confirm all acts, deeds and things lawfully done by my said Attorney under this Power of Attorney.

This Power of Attorney shall remain in force until [Validity Period] unless revoked earlier by me in writing.

IN WITNESS WHEREOF, I have hereunto set my hand on this [Date] day of [Month], [Year] at [Place].

PRINCIPAL:
[Principal Signature]
[Principal Name]

ATTORNEY:
[Attorney Signature]  
[Attorney Name]

WITNESSES:
1. [Witness 1 Name]
   [Witness 1 Address]
   [Witness 1 Signature]

2. [Witness 2 Name]
   [Witness 2 Address]
   [Witness 2 Signature]

NOTARIZATION:
Verified and signed before me on [Date] at [Place].

[Notary Signature]
Notary Public`,
        fields: [
            { label: 'Principal Name', placeholder: 'Name of person giving power' },
            { label: 'Principal Address', placeholder: 'Complete address of principal' },
            { label: 'Principal Age', placeholder: 'Age of principal' },
            { label: 'Attorney Name', placeholder: 'Name of person receiving power' },
            { label: 'Attorney Address', placeholder: 'Complete address of attorney' },
            { label: 'Attorney Age', placeholder: 'Age of attorney' },
            { label: 'Validity Period', placeholder: 'e.g., revoked by me, one year, indefinite' },
            { label: 'Date', placeholder: 'Day DD' },
            { label: 'Month', placeholder: 'Month name' },
            { label: 'Year', placeholder: 'YYYY' },
            { label: 'Place', placeholder: 'City where executed' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 1 Address', placeholder: 'Address of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' },
            { label: 'Witness 2 Address', placeholder: 'Address of second witness' }
        ]
    },
    'affidavits': {
        title: 'Affidavit',
        description: 'Sworn statement of facts for legal or official purposes.',
        template: `AFFIDAVIT

I, [Deponent Name], S/o/D/o/W/o [Father's/Husband's Name], aged [Age] years, R/o [Address], do hereby solemnly affirm and state on oath as under:

1. That I am the deponent herein and I am competent to swear this affidavit.

2. That [Statement of Facts - First fact/circumstance].

3. That [Statement of Facts - Second fact/circumstance].

4. That [Statement of Facts - Third fact/circumstance].

5. That [Purpose of Affidavit - why this affidavit is being made].

6. That the facts stated above are true to the best of my knowledge and belief and nothing material has been concealed therefrom.

7. That I am making this affidavit for the purpose of [Specific Purpose] and for no other collateral purpose.

DEPONENT

VERIFICATION:
Verified at [Place] on this [Date] that the contents of the above affidavit are true to my knowledge and belief and no part of it is false and nothing material has been concealed therefrom.

DEPONENT
[Deponent Signature]
[Deponent Name]

Sworn before me on this [Date] at [Place].

[Oath Commissioner/Notary Signature]
Oath Commissioner/Notary Public`,
        fields: [
            { label: 'Deponent Name', placeholder: 'Name of person making affidavit' },
            { label: 'Deponent Address', placeholder: 'Complete address of deponent' },
            { label: 'Age', placeholder: 'Age of deponent' },
            { label: 'First Fact', placeholder: 'First statement of fact' },
            { label: 'Second Fact', placeholder: 'Second statement of fact' },
            { label: 'Third Fact', placeholder: 'Third statement of fact' },
            { label: 'Purpose of Affidavit', placeholder: 'Why this affidavit is being made' },
            { label: 'Specific Purpose', placeholder: 'Specific legal/official purpose' },
            { label: 'Date', placeholder: 'Date of affidavit DD/MM/YYYY' },
            { label: 'Place', placeholder: 'Place where affidavit is made' }
        ]
    },
    'agreements': {
        title: 'Agreement',
        description: 'General agreement document between two or more parties.',
        template: `AGREEMENT

THIS AGREEMENT is made on [Date] between [Party 1 Name], S/o/D/o/W/o [Party 1 Father's/Husband's Name], R/o [Party 1 Address] (hereinafter called "Party 1") of the ONE PART and [Party 2 Name], S/o/D/o/W/o [Party 2 Father's/Husband's Name], R/o [Party 2 Address] (hereinafter called "Party 2") of the OTHER PART.

WHEREAS [Background/Recitals - describe the background and purpose of agreement];

NOW THEREFORE, in consideration of the mutual covenants and agreements contained herein, the parties agree as follows:

1. SCOPE OF AGREEMENT:
   [Scope Description - what the agreement covers]

2. OBLIGATIONS OF PARTY 1:
   [Party 1 Obligations - what Party 1 must do]

3. OBLIGATIONS OF PARTY 2:
   [Party 2 Obligations - what Party 2 must do]

4. CONSIDERATION:
   [Consideration Details - payment/exchange terms]

5. DURATION:
   This agreement shall remain in force from [Start Date] to [End Date].

6. TERMINATION:
   [Termination Clause - how agreement can be terminated]

7. DISPUTE RESOLUTION:
   Any dispute arising out of this agreement shall be resolved through [Dispute Resolution Method].

8. GOVERNING LAW:
   This agreement shall be governed by the laws of India.

9. MISCELLANEOUS:
   [Additional Terms - any other specific terms]

IN WITNESS WHEREOF, the parties have executed this agreement on the date first written above.

PARTY 1:                           PARTY 2:
[Party 1 Signature]               [Party 2 Signature]
[Party 1 Name]                    [Party 2 Name]

WITNESSES:
1. [Witness 1 Name]               2. [Witness 2 Name]
   [Witness 1 Signature]             [Witness 2 Signature]`,
        fields: [
            { label: 'Date', placeholder: 'Date of agreement DD/MM/YYYY' },
            { label: 'Party 1 Name', placeholder: 'Name of first party' },
            { label: 'Party 1 Address', placeholder: 'Address of first party' },
            { label: 'Party 2 Name', placeholder: 'Name of second party' },
            { label: 'Party 2 Address', placeholder: 'Address of second party' },
            { label: 'Background/Recitals', placeholder: 'Background and purpose of agreement' },
            { label: 'Scope Description', placeholder: 'What the agreement covers' },
            { label: 'Party 1 Obligations', placeholder: 'What Party 1 must do' },
            { label: 'Party 2 Obligations', placeholder: 'What Party 2 must do' },
            { label: 'Consideration Details', placeholder: 'Payment/exchange terms' },
            { label: 'Start Date', placeholder: 'Agreement start date DD/MM/YYYY' },
            { label: 'End Date', placeholder: 'Agreement end date DD/MM/YYYY' },
            { label: 'Termination Clause', placeholder: 'How agreement can be terminated' },
            { label: 'Dispute Resolution Method', placeholder: 'e.g., arbitration, mediation, courts' },
            { label: 'Additional Terms', placeholder: 'Any other specific terms' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' }
        ]
    },
    'rent': {
        title: 'Rent Agreement',
        description: 'Rental agreement between landlord and tenant for residential or commercial property.',
        template: `RENT AGREEMENT

THIS RENT AGREEMENT is made on [Date] between [Landlord Name], S/o/D/o/W/o [Landlord Father's/Husband's Name], R/o [Landlord Address] (hereinafter called "LANDLORD") of the ONE PART and [Tenant Name], S/o/D/o/W/o [Tenant Father's/Husband's Name], R/o [Tenant Address] (hereinafter called "TENANT") of the OTHER PART.

WHEREAS the Landlord is the absolute owner of the property described below and is willing to let out the same to the Tenant on the terms and conditions mentioned hereinafter:

PROPERTY DETAILS:
[Property Description] situated at [Property Address], measuring approximately [Property Size].

NOW THEREFORE, the parties agree as follows:

1. LEASE PERIOD:
   The lease period shall be for [Lease Duration] commencing from [Start Date] and ending on [End Date].

2. RENT:
   The monthly rent shall be Rs. [Monthly Rent] payable on or before [Payment Date] of each month.

3. SECURITY DEPOSIT:
   The Tenant has paid Rs. [Security Deposit] as security deposit which shall be refunded at the time of vacating the premises after adjusting any dues.

4. MAINTENANCE:
   [Maintenance Responsibility - who is responsible for what maintenance]

5. UTILITIES:
   [Utility Responsibility - electricity, water, gas bills responsibility]

6. USE OF PREMISES:
   The premises shall be used only for [Purpose of Use] and not for any other purpose.

7. RESTRICTIONS:
   [Restrictions - any specific restrictions on tenant]

8. TERMINATION:
   Either party may terminate this agreement by giving [Notice Period] days written notice.

9. DEFAULT:
   In case of default in payment of rent for [Default Period], the Landlord may terminate the agreement.

10. REGISTRATION:
    [Registration Clause - whether agreement will be registered]

IN WITNESS WHEREOF, the parties have executed this agreement on the date first written above.

LANDLORD:                         TENANT:
[Landlord Signature]             [Tenant Signature]
[Landlord Name]                  [Tenant Name]

WITNESSES:
1. [Witness 1 Name]              2. [Witness 2 Name]
   [Witness 1 Signature]            [Witness 2 Signature]`,
        fields: [
            { label: 'Date', placeholder: 'Date of agreement DD/MM/YYYY' },
            { label: 'Landlord Name', placeholder: 'Name of property owner' },
            { label: 'Landlord Address', placeholder: 'Address of landlord' },
            { label: 'Tenant Name', placeholder: 'Name of tenant' },
            { label: 'Tenant Address', placeholder: 'Address of tenant' },
            { label: 'Property Description', placeholder: 'Type of property (flat, house, shop, etc.)' },
            { label: 'Property Address', placeholder: 'Complete address of rental property' },
            { label: 'Property Size', placeholder: 'Size/area of property' },
            { label: 'Lease Duration', placeholder: 'e.g., 11 months, 2 years' },
            { label: 'Start Date', placeholder: 'Lease start date DD/MM/YYYY' },
            { label: 'End Date', placeholder: 'Lease end date DD/MM/YYYY' },
            { label: 'Monthly Rent', placeholder: 'Monthly rent amount' },
            { label: 'Payment Date', placeholder: 'Date of month for rent payment (e.g., 5th)' },
            { label: 'Security Deposit', placeholder: 'Security deposit amount' },
            { label: 'Maintenance Responsibility', placeholder: 'Who handles maintenance' },
            { label: 'Utility Responsibility', placeholder: 'Who pays utility bills' },
            { label: 'Purpose of Use', placeholder: 'e.g., residential, commercial' },
            { label: 'Restrictions', placeholder: 'Any restrictions on tenant' },
            { label: 'Notice Period', placeholder: 'Notice period for termination (days)' },
            { label: 'Default Period', placeholder: 'Default period before termination' },
            { label: 'Registration Clause', placeholder: 'Registration details if applicable' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' }
        ]
    },
    'special-leave': {
        title: 'Special Leave Petition',
        description: 'Special Leave Petition under Article 136 of the Constitution of India before the Supreme Court.',
        template: `BEFORE THE HON'BLE SUPREME COURT OF INDIA
CIVIL/CRIMINAL APPELLATE JURISDICTION

SPECIAL LEAVE PETITION (CIVIL/CRIMINAL) NO. _______ OF 2024

IN THE MATTER OF:
[Petitioner Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Petitioner Address] ... Petitioner
VERSUS
[Respondent Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Respondent Address] ... Respondent

SPECIAL LEAVE PETITION UNDER ARTICLE 136 OF THE CONSTITUTION OF INDIA

TO THE HON'BLE CHIEF JUSTICE OF INDIA AND HIS COMPANION JUSTICES OF THE HON'BLE SUPREME COURT OF INDIA

The humble petition of the Petitioner above-named

MOST RESPECTFULLY SHOWETH:

1. That the Petitioner seeks special leave to appeal against the judgment and order dated [Judgment Date] passed by the [Court Name] in [Case Details] whereby [Brief Description of Impugned Order].

2. That the facts leading to the present petition are as follows:
   [Factual Background - describe the chronology of events]

3. That the [Lower Court/Tribunal] vide its order dated [Lower Court Date] had [Lower Court Decision].

4. That aggrieved by the said order, the Petitioner preferred an appeal before the [Appellate Court] which vide the impugned judgment dated [Judgment Date] [Decision of Appellate Court].

5. That the impugned judgment is liable to be set aside on the following grounds:
   a) [Ground 1 - Legal/factual ground]
   b) [Ground 2 - Legal/factual ground]
   c) [Ground 3 - Legal/factual ground]

6. That the case involves [Substantial Question of Law/Public Importance] which requires consideration by this Hon'ble Court.

7. That the Petitioner has no other efficacious remedy except to approach this Hon'ble Court under Article 136 of the Constitution.

8. That the petition is within limitation and there is no delay in filing the same.

9. That the Petitioner undertakes to file the certified copies of the impugned judgment and other relevant documents.

PRAYER

In the premises aforesaid, it is most respectfully prayed that this Hon'ble Court may be pleased to:

a) Grant special leave to appeal against the impugned judgment and order dated [Judgment Date];
b) Set aside the impugned judgment and order;
c) [Specific Relief Sought];
d) Pass any other order as this Hon'ble Court may deem fit and proper.

AND FOR THIS ACT OF KINDNESS, THE PETITIONER AS IN DUTY BOUND SHALL EVER PRAY.

[Petitioner Signature]
PETITIONER

THROUGH
[Advocate Name]
[Advocate Designation]
Advocate for Petitioner

FILED ON: [Filing Date]`,
        fields: [
            { label: 'Petitioner Name', placeholder: 'Name of person filing petition' },
            { label: 'Petitioner Address', placeholder: 'Complete address of petitioner' },
            { label: 'Respondent Name', placeholder: 'Name of opposing party' },
            { label: 'Respondent Address', placeholder: 'Complete address of respondent' },
            { label: 'Judgment Date', placeholder: 'Date of impugned judgment DD/MM/YYYY' },
            { label: 'Court Name', placeholder: 'Name of court that passed impugned order' },
            { label: 'Case Details', placeholder: 'Case number and details' },
            { label: 'Brief Description of Impugned Order', placeholder: 'Brief description of the order being challenged' },
            { label: 'Factual Background', placeholder: 'Chronology of events leading to petition' },
            { label: 'Lower Court Date', placeholder: 'Date of lower court order DD/MM/YYYY' },
            { label: 'Lower Court Decision', placeholder: 'Decision of lower court/tribunal' },
            { label: 'Appellate Court', placeholder: 'Name of appellate court' },
            { label: 'Decision of Appellate Court', placeholder: 'Decision of appellate court' },
            { label: 'Ground 1', placeholder: 'First legal/factual ground for appeal' },
            { label: 'Ground 2', placeholder: 'Second legal/factual ground for appeal' },
            { label: 'Ground 3', placeholder: 'Third legal/factual ground for appeal' },
            { label: 'Substantial Question of Law/Public Importance', placeholder: 'Important legal question involved' },
            { label: 'Specific Relief Sought', placeholder: 'What specific relief is being sought' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Advocate Designation', placeholder: 'e.g., Senior Advocate, Advocate-on-Record' },
            { label: 'Filing Date', placeholder: 'Date of filing DD/MM/YYYY' }
        ]
    },
    'adoption': {
        title: 'Adoption Deed',
        description: 'Legal document for adoption of a child under Hindu Adoption and Maintenance Act or Juvenile Justice Act.',
        template: `ADOPTION DEED

THIS DEED OF ADOPTION is made on [Date] between [Adoptive Father Name], S/o [Adoptive Father's Father Name], aged [Adoptive Father Age] years, and [Adoptive Mother Name], D/o [Adoptive Mother's Father Name], aged [Adoptive Mother Age] years, both R/o [Adoptive Parents Address] (hereinafter called "ADOPTIVE PARENTS") of the ONE PART and [Natural Father Name], S/o [Natural Father's Father Name], aged [Natural Father Age] years, and [Natural Mother Name], D/o [Natural Mother's Father Name], aged [Natural Mother Age] years, both R/o [Natural Parents Address] (hereinafter called "NATURAL PARENTS") of the OTHER PART.

WHEREAS the Natural Parents are the natural and legal parents of [Child Name], [Gender], born on [Child DOB] at [Place of Birth];

AND WHEREAS the Natural Parents are unable to maintain and bring up the said child due to [Reason for Adoption];

AND WHEREAS the Adoptive Parents are desirous of adopting the said child and are capable of providing proper care, maintenance, education and upbringing to the child;

AND WHEREAS both parties are Hindus and this adoption is being made in accordance with the provisions of the Hindu Adoption and Maintenance Act, 1956;

NOW THEREFORE, in consideration of the mutual covenants and agreements contained herein, the parties agree as follows:

1. ADOPTION:
   The Natural Parents hereby give the said child [Child Name] in adoption to the Adoptive Parents who hereby accept the child in adoption.

2. TRANSFER OF RIGHTS:
   From the date of this deed, all rights, duties and obligations of the Natural Parents towards the child shall cease and the same shall vest in the Adoptive Parents.

3. LEGAL STATUS:
   The adopted child shall have the same rights and status as a natural born child of the Adoptive Parents including rights of inheritance.

4. NAME CHANGE:
   The child shall henceforth be known as [New Name] and shall bear the surname of the Adoptive Father.

5. MAINTENANCE AND EDUCATION:
   The Adoptive Parents undertake to provide proper maintenance, education, medical care and upbringing to the child.

6. IRREVOCABILITY:
   This adoption is irrevocable and the Natural Parents shall have no claim over the child hereafter.

7. REGISTRATION:
   The parties agree to complete all legal formalities for registration of this adoption with appropriate authorities.

8. WELFARE OF CHILD:
   The primary consideration in this adoption is the welfare and best interest of the child.

IN WITNESS WHEREOF, the parties have executed this deed on the date first written above.

ADOPTIVE PARENTS:                    NATURAL PARENTS:
[Adoptive Father Signature]         [Natural Father Signature]
[Adoptive Father Name]               [Natural Father Name]

[Adoptive Mother Signature]         [Natural Mother Signature]
[Adoptive Mother Name]               [Natural Mother Name]

WITNESSES:
1. [Witness 1 Name]                  2. [Witness 2 Name]
   [Witness 1 Address]                  [Witness 2 Address]
   [Witness 1 Signature]               [Witness 2 Signature]

NOTARIZATION:
Verified and signed before me on [Date] at [Place].

[Notary Signature]
Notary Public`,
        fields: [
            { label: 'Date', placeholder: 'Date of adoption deed DD/MM/YYYY' },
            { label: 'Adoptive Father Name', placeholder: 'Name of adoptive father' },
            { label: 'Adoptive Father Age', placeholder: 'Age of adoptive father' },
            { label: 'Adoptive Mother Name', placeholder: 'Name of adoptive mother' },
            { label: 'Adoptive Mother Age', placeholder: 'Age of adoptive mother' },
            { label: 'Adoptive Parents Address', placeholder: 'Address of adoptive parents' },
            { label: 'Natural Father Name', placeholder: 'Name of natural father' },
            { label: 'Natural Father Age', placeholder: 'Age of natural father' },
            { label: 'Natural Mother Name', placeholder: 'Name of natural mother' },
            { label: 'Natural Mother Age', placeholder: 'Age of natural mother' },
            { label: 'Natural Parents Address', placeholder: 'Address of natural parents' },
            { label: 'Child Name', placeholder: 'Current name of child' },
            { label: 'Gender', placeholder: 'Male/Female' },
            { label: 'Child DOB', placeholder: 'Date of birth of child DD/MM/YYYY' },
            { label: 'Place of Birth', placeholder: 'Place where child was born' },
            { label: 'Reason for Adoption', placeholder: 'Reason why natural parents are giving child for adoption' },
            { label: 'New Name', placeholder: 'New name for the child after adoption' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 1 Address', placeholder: 'Address of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' },
            { label: 'Witness 2 Address', placeholder: 'Address of second witness' },
            { label: 'Place', placeholder: 'Place where deed is executed' }
        ]
    },
    'specific-relief': {
        title: 'Specific Relief Act: Suit for Specific Performance',
        description: 'Suit for specific performance of contract under the Specific Relief Act, 1963.',
        template: `BEFORE THE HON'BLE DISTRICT JUDGE AT [CITY]
CIVIL SUIT NO. _______ OF 2024

IN THE MATTER OF:
[Plaintiff Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Plaintiff Address] ... Plaintiff
VERSUS
[Defendant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Defendant Address] ... Defendant

SUIT FOR SPECIFIC PERFORMANCE OF CONTRACT UNDER THE SPECIFIC RELIEF ACT, 1963

TO THE HON'BLE COURT

The Plaintiff respectfully submits as under:

1. That the Plaintiff and Defendant entered into a [Type of Contract] on [Contract Date] whereby the Defendant agreed to [Contract Terms].

2. That as per the terms of the contract, the Defendant was to [Defendant's Obligations] on or before [Performance Date].

3. That the Plaintiff has performed his part of the contract by [Plaintiff's Performance].

4. That the consideration for the contract was Rs. [Contract Amount] which has been [Payment Status].

5. That the time was essence of the contract and the Defendant was bound to perform his obligations within the stipulated time.

6. That despite repeated requests and the expiry of the stipulated time, the Defendant has failed and refused to perform his part of the contract.

7. That the Plaintiff has suffered damages due to the non-performance by the Defendant and is ready and willing to perform his remaining obligations under the contract.

8. That the contract is valid, legal and enforceable and the Plaintiff is entitled to specific performance of the same.

9. That the Plaintiff has no adequate remedy at law and specific performance is the only appropriate remedy.

10. That this Hon'ble Court has jurisdiction to entertain and try this suit as [Jurisdiction Basis].

11. That the suit is within limitation and there is no delay in filing the same.

RELIEF CLAIMED:

The Plaintiff therefore prays that this Hon'ble Court may be pleased to:

a) Decree that the Defendant do specifically perform the contract dated [Contract Date];
b) Direct the Defendant to [Specific Performance Required];
c) Award damages of Rs. [Damage Amount] for delay and breach;
d) Award costs of the suit;
e) Grant such other relief as this Hon'ble Court may deem fit.

[Plaintiff Signature]
PLAINTIFF

THROUGH
[Advocate Name]
Advocate for Plaintiff

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above plaint are true to my knowledge and belief.

[Plaintiff Signature]
PLAINTIFF`,
        fields: [
            { label: 'City', placeholder: 'City where court is located' },
            { label: 'Plaintiff Name', placeholder: 'Name of person filing suit' },
            { label: 'Plaintiff Address', placeholder: 'Complete address of plaintiff' },
            { label: 'Defendant Name', placeholder: 'Name of person being sued' },
            { label: 'Defendant Address', placeholder: 'Complete address of defendant' },
            { label: 'Type of Contract', placeholder: 'e.g., Sale Agreement, Service Contract' },
            { label: 'Contract Date', placeholder: 'Date of contract DD/MM/YYYY' },
            { label: 'Contract Terms', placeholder: 'Main terms of the contract' },
            { label: 'Defendant\'s Obligations', placeholder: 'What defendant was supposed to do' },
            { label: 'Performance Date', placeholder: 'Date by which performance was due DD/MM/YYYY' },
            { label: 'Plaintiff\'s Performance', placeholder: 'What plaintiff has already done' },
            { label: 'Contract Amount', placeholder: 'Value of the contract' },
            { label: 'Payment Status', placeholder: 'Payment status (paid/partially paid)' },
            { label: 'Jurisdiction Basis', placeholder: 'Why this court has jurisdiction' },
            { label: 'Specific Performance Required', placeholder: 'Exactly what defendant must do' },
            { label: 'Damage Amount', placeholder: 'Amount of damages claimed' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Date', placeholder: 'Date of verification DD/MM/YYYY' }
        ]
    },
    'will-gift': {
        title: 'Will & Gift Deed',
        description: 'Legal document for making a will or gift deed for transfer of property.',
        template: `WILL / GIFT DEED

THIS [WILL/GIFT DEED] is made on [Date] by [Testator/Donor Name], S/o/D/o/W/o [Father's/Husband's Name], aged [Age] years, R/o [Address] (hereinafter called "[TESTATOR/DONOR]").

WHEREAS the [Testator/Donor] is the absolute owner of the property described hereinbelow;

AND WHEREAS the [Testator/Donor] is of sound mind and is making this [will/gift] of his/her own free will without any coercion or undue influence;

NOW THEREFORE, I, [Testator/Donor Name], do hereby [bequeath/gift] the following property:

PROPERTY DESCRIPTION:
[Property Details] situated at [Property Address], measuring [Property Measurements], bounded by [Property Boundaries].

BENEFICIARY/DONEE:
I hereby [bequeath/gift] the above property to [Beneficiary Name], S/o/D/o/W/o [Beneficiary Father's/Husband's Name], R/o [Beneficiary Address], who is my [Relationship].

TERMS AND CONDITIONS:
1. [Condition 1]
2. [Condition 2]
3. [Condition 3]

[FOR WILL ONLY:]
EXECUTOR:
I hereby appoint [Executor Name], R/o [Executor Address], as the executor of this will to carry out the provisions herein.

REVOCATION:
This will revokes all previous wills and codicils made by me.

[FOR GIFT DEED ONLY:]
DELIVERY OF POSSESSION:
I hereby deliver possession of the said property to the donee who accepts the same.

CONSIDERATION:
This gift is made out of natural love and affection without any monetary consideration.

IN WITNESS WHEREOF, I have hereunto set my hand on this [Date] day of [Month], [Year] at [Place].

[TESTATOR/DONOR]:
[Signature]
[Name]

[FOR GIFT DEED - DONEE ACCEPTANCE:]
I, [Beneficiary Name], do hereby accept the above gift.

[Beneficiary Signature]
[Beneficiary Name]

WITNESSES:
1. [Witness 1 Name]               2. [Witness 2 Name]
   [Witness 1 Address]               [Witness 2 Address]
   [Witness 1 Signature]            [Witness 2 Signature]

NOTARIZATION:
Verified and signed before me on [Date] at [Place].

[Notary Signature]
Notary Public`,
        fields: [
            { label: 'Document Type', placeholder: 'WILL or GIFT DEED' },
            { label: 'Date', placeholder: 'Date of document DD/MM/YYYY' },
            { label: 'Testator/Donor Name', placeholder: 'Name of person making will/gift' },
            { label: 'Age', placeholder: 'Age of testator/donor' },
            { label: 'Address', placeholder: 'Address of testator/donor' },
            { label: 'Property Details', placeholder: 'Description of property being transferred' },
            { label: 'Property Address', placeholder: 'Address of the property' },
            { label: 'Property Measurements', placeholder: 'Size/measurements of property' },
            { label: 'Property Boundaries', placeholder: 'Boundaries of the property' },
            { label: 'Beneficiary Name', placeholder: 'Name of person receiving property' },
            { label: 'Beneficiary Address', placeholder: 'Address of beneficiary' },
            { label: 'Relationship', placeholder: 'Relationship to testator/donor' },
            { label: 'Condition 1', placeholder: 'First condition/term' },
            { label: 'Condition 2', placeholder: 'Second condition/term' },
            { label: 'Condition 3', placeholder: 'Third condition/term' },
            { label: 'Executor Name', placeholder: 'Name of executor (for will only)' },
            { label: 'Executor Address', placeholder: 'Address of executor (for will only)' },
            { label: 'Month', placeholder: 'Month name' },
            { label: 'Year', placeholder: 'YYYY' },
            { label: 'Place', placeholder: 'Place where document is executed' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 1 Address', placeholder: 'Address of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' },
            { label: 'Witness 2 Address', placeholder: 'Address of second witness' }
        ]
    },
    'appointments': {
        title: 'Appointment Letter/Order',
        description: 'Official appointment letter or order for employment or designation.',
        template: `APPOINTMENT LETTER/ORDER

[Organization Name]
[Organization Address]
[City, State - PIN Code]

Date: [Date]

To,
[Appointee Name]
[Appointee Address]

Subject: Appointment as [Designation]

Dear [Mr./Ms. Appointee Name],

We are pleased to inform you that you have been selected for the position of [Designation] in our [Department/Division] based on your performance in the [Selection Process].

TERMS AND CONDITIONS OF APPOINTMENT:

1. DESIGNATION: You are appointed as [Designation] in the [Department/Division].

2. DATE OF JOINING: You are required to join duty on [Joining Date].

3. PROBATION PERIOD: You will be on probation for a period of [Probation Period] from the date of joining.

4. SALARY AND BENEFITS:
   - Basic Salary: Rs. [Basic Salary] per month
   - Allowances: [Allowance Details]
   - Total CTC: Rs. [Total CTC] per annum

5. WORKING HOURS: [Working Hours] from [Start Time] to [End Time], [Working Days].

6. REPORTING: You will report to [Reporting Manager] and will be based at [Work Location].

7. DUTIES AND RESPONSIBILITIES:
   [Job Responsibilities]

8. CONFIDENTIALITY: You shall maintain strict confidentiality of all organizational information.

9. TERMINATION: Either party may terminate this appointment by giving [Notice Period] notice in writing.

10. GOVERNING LAW: This appointment shall be governed by the laws of India and subject to the jurisdiction of [Jurisdiction].

Please confirm your acceptance of this appointment by signing and returning the duplicate copy of this letter within [Response Time] days.

We look forward to your association with our organization.

Yours sincerely,

[Authorized Signatory Name]
[Designation]
[Organization Name]

ACCEPTANCE:

I, [Appointee Name], hereby accept the above appointment on the terms and conditions mentioned.

Date: [Acceptance Date]
Place: [Place]

[Appointee Signature]
[Appointee Name]`,
        fields: [
            { label: 'Organization Name', placeholder: 'Name of organization/company' },
            { label: 'Organization Address', placeholder: 'Complete address of organization' },
            { label: 'City, State - PIN Code', placeholder: 'City, State and PIN code' },
            { label: 'Date', placeholder: 'Date of appointment letter DD/MM/YYYY' },
            { label: 'Appointee Name', placeholder: 'Name of person being appointed' },
            { label: 'Appointee Address', placeholder: 'Address of appointee' },
            { label: 'Designation', placeholder: 'Job title/designation' },
            { label: 'Department/Division', placeholder: 'Department or division name' },
            { label: 'Selection Process', placeholder: 'e.g., interview, written test, etc.' },
            { label: 'Joining Date', placeholder: 'Date of joining DD/MM/YYYY' },
            { label: 'Probation Period', placeholder: 'e.g., 6 months, 1 year' },
            { label: 'Basic Salary', placeholder: 'Basic salary amount' },
            { label: 'Allowance Details', placeholder: 'Details of allowances' },
            { label: 'Total CTC', placeholder: 'Total cost to company' },
            { label: 'Working Hours', placeholder: 'Number of working hours' },
            { label: 'Start Time', placeholder: 'Start time of work' },
            { label: 'End Time', placeholder: 'End time of work' },
            { label: 'Working Days', placeholder: 'Working days (e.g., Monday to Friday)' },
            { label: 'Reporting Manager', placeholder: 'Name/designation of reporting manager' },
            { label: 'Work Location', placeholder: 'Work location/office address' },
            { label: 'Job Responsibilities', placeholder: 'Key duties and responsibilities' },
            { label: 'Notice Period', placeholder: 'Notice period for termination' },
            { label: 'Jurisdiction', placeholder: 'Legal jurisdiction (e.g., Delhi courts)' },
            { label: 'Response Time', placeholder: 'Time to respond (e.g., 7, 15)' },
            { label: 'Authorized Signatory Name', placeholder: 'Name of person signing' },
            { label: 'Acceptance Date', placeholder: 'Date of acceptance DD/MM/YYYY' },
            { label: 'Place', placeholder: 'Place of acceptance' }
        ]
    },
    'criminal-pleadings': {
        title: 'Criminal Pleadings',
        description: 'Criminal complaint or application in criminal proceedings.',
        template: `BEFORE THE HON'BLE [COURT NAME] AT [CITY]
CRIMINAL CASE NO. _______ OF 2024

IN THE MATTER OF:
[Complainant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Complainant Address] ... Complainant
VERSUS
[Accused Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Accused Address] ... Accused

CRIMINAL COMPLAINT UNDER SECTION [SECTION NUMBERS] OF THE [ACT NAME]

TO THE HON'BLE COURT

The Complainant respectfully submits as under:

1. That the complainant is a [Occupation] and is a law-abiding citizen.

2. That on [Date of Incident] at about [Time] hours at [Place of Incident], the accused [Description of Criminal Act].

3. That the accused has thereby committed offenses punishable under:
   - Section [Section 1] of [Act 1]
   - Section [Section 2] of [Act 2]
   - Section [Section 3] of [Act 3]

4. That [Detailed Facts of the Case].

5. That the complainant immediately [Action Taken] and reported the matter to [Authority].

6. That [Evidence Available] supports the allegations made herein.

7. That the accused has [Previous Criminal History/Character].

8. That the complainant has suffered [Loss/Damage/Injury] due to the criminal act of the accused.

9. That this Hon'ble Court has jurisdiction to try this case as [Jurisdiction Basis].

10. That the complaint is being filed within limitation and there is no delay.

PRAYER

It is therefore most respectfully prayed that this Hon'ble Court may be pleased to:

a) Take cognizance of the offenses committed by the accused;
b) Issue summons/warrant against the accused;
c) Try the accused for the offenses and award appropriate punishment;
d) Direct the accused to pay compensation of Rs. [Compensation Amount];
e) Pass any other order as this Hon'ble Court may deem fit.

[Complainant Signature]
COMPLAINANT

THROUGH
[Advocate Name]
Advocate for Complainant

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above complaint are true to my knowledge and belief.

[Complainant Signature]
COMPLAINANT`,
        fields: [
            { label: 'Court Name', placeholder: 'e.g., Chief Judicial Magistrate, Sessions Judge' },
            { label: 'City', placeholder: 'City where court is located' },
            { label: 'Complainant Name', placeholder: 'Name of person filing complaint' },
            { label: 'Complainant Address', placeholder: 'Complete address of complainant' },
            { label: 'Accused Name', placeholder: 'Name of accused person' },
            { label: 'Accused Address', placeholder: 'Complete address of accused' },
            { label: 'Section Numbers', placeholder: 'e.g., 420, 406, 506' },
            { label: 'Act Name', placeholder: 'e.g., Indian Penal Code, 1860' },
            { label: 'Occupation', placeholder: 'Occupation of complainant' },
            { label: 'Date of Incident', placeholder: 'Date of criminal incident DD/MM/YYYY' },
            { label: 'Time', placeholder: 'Time of incident' },
            { label: 'Place of Incident', placeholder: 'Location where incident occurred' },
            { label: 'Description of Criminal Act', placeholder: 'Description of what accused did' },
            { label: 'Section 1', placeholder: 'First section number' },
            { label: 'Act 1', placeholder: 'First act name' },
            { label: 'Section 2', placeholder: 'Second section number' },
            { label: 'Act 2', placeholder: 'Second act name' },
            { label: 'Section 3', placeholder: 'Third section number' },
            { label: 'Act 3', placeholder: 'Third act name' },
            { label: 'Detailed Facts of the Case', placeholder: 'Detailed description of the incident' },
            { label: 'Action Taken', placeholder: 'What complainant did after incident' },
            { label: 'Authority', placeholder: 'Police station/authority where reported' },
            { label: 'Evidence Available', placeholder: 'Evidence supporting the case' },
            { label: 'Previous Criminal History/Character', placeholder: 'Any previous criminal record of accused' },
            { label: 'Loss/Damage/Injury', placeholder: 'Loss or damage suffered' },
            { label: 'Jurisdiction Basis', placeholder: 'Why this court has jurisdiction' },
            { label: 'Compensation Amount', placeholder: 'Amount of compensation sought' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Date', placeholder: 'Date of verification DD/MM/YYYY' }
        ]
    },
    'civil-pleadings': {
        title: 'Civil Pleadings',
        description: 'Civil suit plaint for recovery, damages, or other civil remedies.',
        template: `BEFORE THE HON'BLE [COURT NAME] AT [CITY]
CIVIL SUIT NO. _______ OF 2024

IN THE MATTER OF:
[Plaintiff Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Plaintiff Address] ... Plaintiff
VERSUS
[Defendant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Defendant Address] ... Defendant

SUIT FOR [NATURE OF SUIT]

TO THE HON'BLE COURT

The Plaintiff respectfully submits as under:

1. That the Plaintiff is [Plaintiff Description] and the Defendant is [Defendant Description].

2. That [Background Facts] leading to the present dispute.

3. That on [Date of Transaction/Event], [Description of Transaction/Event].

4. That the Defendant was under a legal obligation to [Defendant's Obligation].

5. That despite [Plaintiff's Performance/Demand], the Defendant has [Defendant's Default/Breach].

6. That due to the [Breach/Default] by the Defendant, the Plaintiff has suffered damages to the tune of Rs. [Damage Amount].

7. That the Plaintiff has [Legal Right/Title] to [Subject Matter of Suit].

8. That the Defendant has [Wrongful Act/Omission] which has caused [Loss/Damage] to the Plaintiff.

9. That the Plaintiff has no adequate remedy except to approach this Hon'ble Court.

10. That this Hon'ble Court has jurisdiction to entertain and try this suit as [Jurisdiction Basis].

11. That the suit is within limitation and there is no delay in filing the same.

12. That the value of the suit for the purpose of jurisdiction and court fees is Rs. [Suit Value].

RELIEF CLAIMED:

The Plaintiff therefore prays that this Hon'ble Court may be pleased to:

a) [Primary Relief Sought];
b) Award damages of Rs. [Damage Amount] with interest @ [Interest Rate]% per annum;
c) Award costs of the suit;
d) Grant interim relief as prayed for;
e) Pass any other order as this Hon'ble Court may deem fit.

[Plaintiff Signature]
PLAINTIFF

THROUGH
[Advocate Name]
Advocate for Plaintiff

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above plaint are true to my knowledge and belief.

[Plaintiff Signature]
PLAINTIFF`,
        fields: [
            { label: 'Court Name', placeholder: 'e.g., District Judge, Civil Judge' },
            { label: 'City', placeholder: 'City where court is located' },
            { label: 'Plaintiff Name', placeholder: 'Name of person filing suit' },
            { label: 'Plaintiff Address', placeholder: 'Complete address of plaintiff' },
            { label: 'Defendant Name', placeholder: 'Name of person being sued' },
            { label: 'Defendant Address', placeholder: 'Complete address of defendant' },
            { label: 'Nature of Suit', placeholder: 'e.g., Recovery of Money, Damages, Declaration' },
            { label: 'Plaintiff Description', placeholder: 'Description/occupation of plaintiff' },
            { label: 'Defendant Description', placeholder: 'Description/occupation of defendant' },
            { label: 'Background Facts', placeholder: 'Background facts leading to dispute' },
            { label: 'Date of Transaction/Event', placeholder: 'Date of relevant transaction/event DD/MM/YYYY' },
            { label: 'Description of Transaction/Event', placeholder: 'Description of the transaction or event' },
            { label: 'Defendant\'s Obligation', placeholder: 'What defendant was supposed to do' },
            { label: 'Plaintiff\'s Performance/Demand', placeholder: 'What plaintiff did or demanded' },
            { label: 'Defendant\'s Default/Breach', placeholder: 'How defendant breached/defaulted' },
            { label: 'Damage Amount', placeholder: 'Amount of damages claimed' },
            { label: 'Legal Right/Title', placeholder: 'Plaintiff\'s legal right or title' },
            { label: 'Subject Matter of Suit', placeholder: 'What the suit is about' },
            { label: 'Wrongful Act/Omission', placeholder: 'Defendant\'s wrongful act' },
            { label: 'Loss/Damage', placeholder: 'Loss or damage caused' },
            { label: 'Jurisdiction Basis', placeholder: 'Why this court has jurisdiction' },
            { label: 'Suit Value', placeholder: 'Value of suit for court fees' },
            { label: 'Primary Relief Sought', placeholder: 'Main relief being sought' },
            { label: 'Interest Rate', placeholder: 'Interest rate claimed (e.g., 12, 18)' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Date', placeholder: 'Date of verification DD/MM/YYYY' }
        ]
    },
    'arbitration': {
        title: 'Arbitration Agreement/Application',
        description: 'Arbitration agreement or application for arbitration proceedings under Arbitration and Conciliation Act.',
        template: `ARBITRATION AGREEMENT / APPLICATION FOR ARBITRATION

[For Agreement:]
THIS ARBITRATION AGREEMENT is made on [Date] between [Party 1 Name], R/o [Party 1 Address] (hereinafter called "Party 1") and [Party 2 Name], R/o [Party 2 Address] (hereinafter called "Party 2").

WHEREAS disputes have arisen between the parties regarding [Dispute Description];

AND WHEREAS the parties desire to resolve their disputes through arbitration;

NOW THEREFORE, the parties agree as follows:

1. ARBITRATION CLAUSE:
   All disputes arising out of or in connection with [Subject Matter] shall be settled by arbitration under the Arbitration and Conciliation Act, 2015.

2. ARBITRATOR:
   The arbitration shall be conducted by [Arbitrator Name/Method of Appointment].

3. SEAT OF ARBITRATION:
   The seat of arbitration shall be [Arbitration Seat].

4. GOVERNING LAW:
   The arbitration shall be governed by Indian law.

5. LANGUAGE:
   The proceedings shall be conducted in [Language].

[For Application:]
APPLICATION FOR ARBITRATION UNDER SECTION 21 OF THE ARBITRATION AND CONCILIATION ACT, 2015

TO THE ARBITRAL TRIBUNAL

The Claimant respectfully submits:

1. PARTIES:
   Claimant: [Claimant Name], R/o [Claimant Address]
   Respondent: [Respondent Name], R/o [Respondent Address]

2. ARBITRATION AGREEMENT:
   The parties entered into an arbitration agreement dated [Agreement Date] whereby disputes were to be resolved through arbitration.

3. DISPUTE:
   [Detailed Description of Dispute]

4. CLAIMS:
   The Claimant claims:
   a) [Claim 1]
   b) [Claim 2]
   c) [Claim 3]

5. RELIEF SOUGHT:
   [Relief Details]

PRAYER:
The Claimant prays for [Specific Relief] and costs.

[Signature]
CLAIMANT`,
        fields: [
            { label: 'Document Type', placeholder: 'Agreement or Application' },
            { label: 'Date', placeholder: 'Date of document DD/MM/YYYY' },
            { label: 'Party 1 Name', placeholder: 'Name of first party' },
            { label: 'Party 1 Address', placeholder: 'Address of first party' },
            { label: 'Party 2 Name', placeholder: 'Name of second party' },
            { label: 'Party 2 Address', placeholder: 'Address of second party' },
            { label: 'Dispute Description', placeholder: 'Brief description of dispute' },
            { label: 'Subject Matter', placeholder: 'Subject matter of agreement/contract' },
            { label: 'Arbitrator Name/Method of Appointment', placeholder: 'Name of arbitrator or method of appointment' },
            { label: 'Arbitration Seat', placeholder: 'City where arbitration will be held' },
            { label: 'Language', placeholder: 'Language of proceedings' },
            { label: 'Claimant Name', placeholder: 'Name of claimant (for application)' },
            { label: 'Claimant Address', placeholder: 'Address of claimant' },
            { label: 'Respondent Name', placeholder: 'Name of respondent' },
            { label: 'Respondent Address', placeholder: 'Address of respondent' },
            { label: 'Agreement Date', placeholder: 'Date of arbitration agreement DD/MM/YYYY' },
            { label: 'Detailed Description of Dispute', placeholder: 'Detailed facts of the dispute' },
            { label: 'Claim 1', placeholder: 'First claim' },
            { label: 'Claim 2', placeholder: 'Second claim' },
            { label: 'Claim 3', placeholder: 'Third claim' },
            { label: 'Relief Details', placeholder: 'Details of relief sought' },
            { label: 'Specific Relief', placeholder: 'Specific relief being prayed for' }
        ]
    },
    'banking': {
        title: 'Banking Documents',
        description: 'Banking related legal documents including loan agreements, guarantees, and recovery notices.',
        template: `BANKING DOCUMENT - [DOCUMENT TYPE]

[For Loan Agreement:]
LOAN AGREEMENT

THIS LOAN AGREEMENT is made on [Date] between [Bank Name], a banking company incorporated under the Banking Regulation Act, 1949, having its branch at [Bank Address] (hereinafter called "LENDER") and [Borrower Name], S/o/D/o/W/o [Father's/Husband's Name], R/o [Borrower Address] (hereinafter called "BORROWER").

WHEREAS the Borrower has applied for a loan of Rs. [Loan Amount] for [Purpose of Loan];

AND WHEREAS the Lender has agreed to grant the said loan on the terms and conditions hereinafter mentioned;

NOW THEREFORE, the parties agree as follows:

1. LOAN AMOUNT: Rs. [Loan Amount]
2. INTEREST RATE: [Interest Rate]% per annum
3. TENURE: [Loan Tenure]
4. EMI: Rs. [EMI Amount] per month
5. SECURITY: [Security Details]
6. GUARANTOR: [Guarantor Details]

[For Recovery Notice:]
RECOVERY NOTICE UNDER SECTION 13(2) OF SARFAESI ACT, 2002

TO: [Borrower Name]

TAKE NOTICE that you have committed default in repayment of loan amount of Rs. [Outstanding Amount] as on [Default Date].

You are hereby required to discharge your liabilities within 60 days from the date of this notice, failing which the secured assets will be taken possession of.

[Bank Signature]
AUTHORIZED OFFICER`,
        fields: [
            { label: 'Document Type', placeholder: 'e.g., Loan Agreement, Recovery Notice, Guarantee' },
            { label: 'Date', placeholder: 'Date of document DD/MM/YYYY' },
            { label: 'Bank Name', placeholder: 'Name of bank' },
            { label: 'Bank Address', placeholder: 'Address of bank branch' },
            { label: 'Borrower Name', placeholder: 'Name of borrower' },
            { label: 'Borrower Address', placeholder: 'Address of borrower' },
            { label: 'Loan Amount', placeholder: 'Amount of loan' },
            { label: 'Purpose of Loan', placeholder: 'Purpose for which loan is taken' },
            { label: 'Interest Rate', placeholder: 'Rate of interest' },
            { label: 'Loan Tenure', placeholder: 'Duration of loan' },
            { label: 'EMI Amount', placeholder: 'Monthly EMI amount' },
            { label: 'Security Details', placeholder: 'Details of security/collateral' },
            { label: 'Guarantor Details', placeholder: 'Details of guarantor if any' },
            { label: 'Outstanding Amount', placeholder: 'Outstanding loan amount' },
            { label: 'Default Date', placeholder: 'Date of default DD/MM/YYYY' }
        ]
    },
    'bonds': {
        title: 'Bonds and Indemnity',
        description: 'Indemnity bonds, performance bonds, and other bond documents.',
        template: `[BOND TYPE] BOND

KNOW ALL MEN BY THESE PRESENTS that we, [Principal Name], S/o/D/o/W/o [Father's/Husband's Name], R/o [Principal Address] (hereinafter called "PRINCIPAL") and [Surety Name], S/o/D/o/W/o [Father's/Husband's Name], R/o [Surety Address] (hereinafter called "SURETY") are held and firmly bound unto [Obligee Name], R/o [Obligee Address] (hereinafter called "OBLIGEE") in the sum of Rs. [Bond Amount] for the payment of which we bind ourselves, our heirs, executors and administrators jointly and severally by these presents.

WHEREAS the Principal has [Background/Reason for Bond];

AND WHEREAS the Obligee requires security for [Purpose of Bond];

NOW THE CONDITION of this obligation is such that if the Principal shall [Condition to be Fulfilled], then this obligation shall be void, otherwise it shall remain in full force and effect.

TERMS AND CONDITIONS:

1. BOND AMOUNT: Rs. [Bond Amount]

2. VALIDITY: This bond shall remain valid from [Start Date] to [End Date].

3. CONDITIONS:
   a) [Condition 1]
   b) [Condition 2]
   c) [Condition 3]

4. DEFAULT: In case of default by the Principal, the Surety shall be liable to pay the bond amount.

5. DISCHARGE: This bond shall be discharged upon [Discharge Condition].

6. JURISDICTION: This bond shall be subject to the jurisdiction of [Jurisdiction].

IN WITNESS WHEREOF, we have hereunto set our hands on this [Date] day of [Month], [Year].

PRINCIPAL:                           SURETY:
[Principal Signature]               [Surety Signature]
[Principal Name]                    [Surety Name]

WITNESSES:
1. [Witness 1 Name]                 2. [Witness 2 Name]
   [Witness 1 Signature]              [Witness 2 Signature]

ACCEPTED:
[Obligee Signature]
[Obligee Name]`,
        fields: [
            { label: 'Bond Type', placeholder: 'e.g., INDEMNITY, PERFORMANCE, FIDELITY' },
            { label: 'Principal Name', placeholder: 'Name of principal (main obligor)' },
            { label: 'Principal Address', placeholder: 'Address of principal' },
            { label: 'Surety Name', placeholder: 'Name of surety (guarantor)' },
            { label: 'Surety Address', placeholder: 'Address of surety' },
            { label: 'Obligee Name', placeholder: 'Name of obligee (beneficiary)' },
            { label: 'Obligee Address', placeholder: 'Address of obligee' },
            { label: 'Bond Amount', placeholder: 'Amount of bond' },
            { label: 'Background/Reason for Bond', placeholder: 'Background or reason for bond' },
            { label: 'Purpose of Bond', placeholder: 'Purpose for which bond is required' },
            { label: 'Condition to be Fulfilled', placeholder: 'Main condition to be fulfilled' },
            { label: 'Start Date', placeholder: 'Bond validity start date DD/MM/YYYY' },
            { label: 'End Date', placeholder: 'Bond validity end date DD/MM/YYYY' },
            { label: 'Condition 1', placeholder: 'First specific condition' },
            { label: 'Condition 2', placeholder: 'Second specific condition' },
            { label: 'Condition 3', placeholder: 'Third specific condition' },
            { label: 'Discharge Condition', placeholder: 'Condition for discharge of bond' },
            { label: 'Jurisdiction', placeholder: 'Legal jurisdiction' },
            { label: 'Date', placeholder: 'Day DD' },
            { label: 'Month', placeholder: 'Month name' },
            { label: 'Year', placeholder: 'YYYY' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' }
        ]
    },
    'writs': {
        title: 'Writ Petitions',
        description: 'Writ petitions under Article 226/32 of the Constitution including habeas corpus, mandamus, certiorari, etc.',
        template: `BEFORE THE HON'BLE HIGH COURT OF [STATE] AT [CITY]
WRIT PETITION NO. _______ OF 2024

IN THE MATTER OF:
[Petitioner Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Petitioner Address] ... Petitioner
VERSUS
[Respondent 1 Name] and Others ... Respondents

WRIT PETITION UNDER ARTICLE 226 OF THE CONSTITUTION OF INDIA FOR ISSUANCE OF WRIT OF [WRIT TYPE]

TO THE HON'BLE CHIEF JUSTICE AND HIS LORDSHIP'S COMPANION JUDGES OF THE HON'BLE HIGH COURT OF [STATE]

The humble petition of the Petitioner above-named

MOST RESPECTFULLY SHOWETH:

1. That the Petitioner is [Petitioner Description] and is aggrieved by [Grievance Description].

2. That the facts leading to the present petition are as follows:
   [Detailed Facts]

3. That the Respondent No. 1 is [Respondent 1 Description] and Respondent No. 2 is [Respondent 2 Description].

4. That on [Date of Impugned Action], the Respondents [Description of Impugned Action].

5. That the said action of the Respondents is [Legal Grounds for Challenge]:
   a) [Ground 1]
   b) [Ground 2]
   c) [Ground 3]

6. That the Petitioner has [Legal Right/Interest] which has been violated by the Respondents.

7. That the Petitioner has no other adequate remedy except to approach this Hon'ble Court under Article 226 of the Constitution.

8. That the petition is within limitation and there is no delay in filing the same.

9. That [Additional Grounds/Facts].

PRAYER

In the premises aforesaid, it is most respectfully prayed that this Hon'ble Court may be pleased to:

a) Issue a writ of [Writ Type] directing the Respondents to [Specific Direction];
b) [Additional Relief 1];
c) [Additional Relief 2];
d) Award costs of the petition;
e) Pass any other order as this Hon'ble Court may deem fit.

AND FOR THIS ACT OF KINDNESS, THE PETITIONER AS IN DUTY BOUND SHALL EVER PRAY.

[Petitioner Signature]
PETITIONER

THROUGH
[Advocate Name]
Advocate for Petitioner

FILED ON: [Filing Date]`,
        fields: [
            { label: 'State', placeholder: 'State name (e.g., Delhi, Maharashtra)' },
            { label: 'City', placeholder: 'City where High Court is located' },
            { label: 'Petitioner Name', placeholder: 'Name of person filing petition' },
            { label: 'Petitioner Address', placeholder: 'Complete address of petitioner' },
            { label: 'Respondent 1 Name', placeholder: 'Name of first respondent' },
            { label: 'Writ Type', placeholder: 'e.g., MANDAMUS, CERTIORARI, HABEAS CORPUS' },
            { label: 'Petitioner Description', placeholder: 'Description/occupation of petitioner' },
            { label: 'Grievance Description', placeholder: 'Brief description of grievance' },
            { label: 'Detailed Facts', placeholder: 'Detailed chronology of facts' },
            { label: 'Respondent 1 Description', placeholder: 'Description of first respondent' },
            { label: 'Respondent 2 Description', placeholder: 'Description of second respondent' },
            { label: 'Date of Impugned Action', placeholder: 'Date of action being challenged DD/MM/YYYY' },
            { label: 'Description of Impugned Action', placeholder: 'Description of the action being challenged' },
            { label: 'Legal Grounds for Challenge', placeholder: 'Legal basis for challenge' },
            { label: 'Ground 1', placeholder: 'First ground for challenge' },
            { label: 'Ground 2', placeholder: 'Second ground for challenge' },
            { label: 'Ground 3', placeholder: 'Third ground for challenge' },
            { label: 'Legal Right/Interest', placeholder: 'Petitioner\'s legal right or interest' },
            { label: 'Additional Grounds/Facts', placeholder: 'Any additional grounds or facts' },
            { label: 'Specific Direction', placeholder: 'Specific direction sought from respondents' },
            { label: 'Additional Relief 1', placeholder: 'First additional relief sought' },
            { label: 'Additional Relief 2', placeholder: 'Second additional relief sought' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Filing Date', placeholder: 'Date of filing DD/MM/YYYY' }
        ]
    }
};

const CategoryDetailsModal = ({ isVisible, onClose, category, onSubmit }) => {
    const [formData, setFormData] = useState({});
    const [additionalDetails, setAdditionalDetails] = useState('');

    useEffect(() => {
        if (isVisible && category) {
            // Reset form when modal opens
            setFormData({});
            setAdditionalDetails('');
        }
    }, [isVisible, category]);

    if (!isVisible || !category) return null;

    const template = CATEGORY_TEMPLATES[category.id];
    if (!template) {
        // Fallback for categories without templates yet
        return (
            <div className={styles.modalBackdrop} onClick={onClose}>
                <div className={`${styles.modalContentWrapper} ${styles.categoryDetailsModal}`} onClick={(e) => e.stopPropagation()}>
                    <div className={styles.modalHeader}>
                        <h3><i className={category.icon + ' mr-2'}></i> {category.title}</h3>
                        <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                    </div>
                    <div className={styles.modalBody}>
                        <p>Template for this category is being prepared. Please provide details below:</p>
                        <div className={styles.formGroup}>
                            <label>Additional Details</label>
                            <textarea
                                className={styles.formControl}
                                rows="6"
                                value={additionalDetails}
                                onChange={(e) => setAdditionalDetails(e.target.value)}
                                placeholder="Please provide specific details for your document..."
                            />
                        </div>
                    </div>
                    <div className={styles.modalFooter}>
                        <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                            Cancel
                        </button>
                        <button 
                            onClick={() => onSubmit(category, {}, additionalDetails)}
                            className={`${styles.actionButton} ${styles.actionButtonPrimary}`}
                        >
                            Generate Draft
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    const handleInputChange = (index, value) => {
        setFormData(prev => ({
            ...prev,
            [index]: value
        }));
    };

    const handleSubmit = () => {
        onSubmit(category, formData, additionalDetails, template);
        onClose();
    };

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={`${styles.modalContentWrapper} ${styles.categoryDetailsModal}`} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className={category.icon + ' mr-2'}></i> {template.title}</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.templateDescription}>
                        <p>{template.description}</p>
                    </div>
                    
                    <div className={styles.formSection}>
                        <h4>Document Details</h4>
                        <div className={styles.formGrid}>
                            {template.fields.map((field, index) => (
                                <div key={index} className={styles.formGroup}>
                                    <label>{field.label}</label>
                                    <input
                                        type="text"
                                        className={styles.formControl}
                                        placeholder={field.placeholder}
                                        value={formData[index] || ''}
                                        onChange={(e) => handleInputChange(index, e.target.value)}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className={styles.formSection}>
                        <h4>Additional Details</h4>
                        <div className={styles.formGroup}>
                            <label>Any specific details or modifications needed</label>
                            <textarea
                                className={styles.formControl}
                                rows="4"
                                value={additionalDetails}
                                onChange={(e) => setAdditionalDetails(e.target.value)}
                                placeholder="Provide any additional information or specific requirements for your document..."
                            />
                        </div>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                        Cancel
                    </button>
                    <button 
                        onClick={handleSubmit}
                        className={`${styles.actionButton} ${styles.actionButtonPrimary}`}
                    >
                        Generate Draft
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CategoryDetailsModal;