import React, { useState, useEffect } from 'react';
import styles from '../main.module.css';

// Legal document templates with detailed formats
const CATEGORY_TEMPLATES = {
    'matrimonial': {
        title: 'Matrimonial: Mutual Consent Divorce Petition',
        description: 'This petition is filed jointly by both husband and wife who have agreed to dissolve their marriage amicably.',
        template: `BEFORE THE PRINCIPAL JUDGE, FAMILY COURT AT [CITY]
H.M.A. PETITION NO. _______ OF 2024

IN THE MATTER OF:
[Full Name of Husband] S/o [Father's Name], R/o [Full Address] ... Petitioner No. 1
AND
[Full Name of Wife] D/o [Father's Name], R/o [Full Address] ... Petitioner No. 2

JOINT PETITION UNDER SECTION 13B(1) OF THE HINDU MARRIAGE ACT, 1955 FOR DISSOLUTION OF MARRIAGE BY MUTUAL CONSENT

The Petitioners respectfully submit as under:

1. That the marriage between Petitioner No. 1 and Petitioner No. 2 was solemnized on [Date of Marriage] at [Place of Marriage] according to Hindu rites and ceremonies. A certified copy of the Marriage Certificate is annexed herewith as Annexure P-1.

2. That after the marriage, both Petitioners cohabited as husband and wife at [Last Place of Residence]. There are no children born out of this wedlock (or state details of children if any).

3. That due to irreconcilable differences and temperamental incompatibility, the Petitioners have been living separately since [Date of Separation].

4. That for the last [e.g., one year or more], there has been no cohabitation between the Petitioners, and all attempts at reconciliation have failed. There is no possibility of them living together as husband and wife again.

5. That the Petitioners have mutually agreed to dissolve their marriage. They have settled all their claims regarding alimony, maintenance (past, present, and future), stridhan, and any other claims against each other. It has been agreed that [e.g., a full and final settlement of Rs. ______ has been paid by Petitioner No. 1 to Petitioner No. 2, or state that no alimony is being claimed].

6. That this petition is not being filed in collusion or with any coercion or undue influence. The consent for divorce is free and voluntary.

7. That this Hon'ble Court has the jurisdiction to entertain and try this petition as the marriage was solemnized and the parties last resided together within the jurisdiction of this court.

PRAYER
It is, therefore, most respectfully prayed that this Hon'ble Court may be pleased to:
a) Grant a decree of divorce by mutual consent, dissolving the marriage between Petitioner No. 1 and Petitioner No. 2.
b) Pass any other order as this Hon'ble Court may deem fit.

(Signature of Petitioner 1) | (Signature of Petitioner 2)
PETITIONER NO. 1 | PETITIONER NO. 2

THROUGH
(Signature of Advocate)
[Advocate's Name & Details]

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above petition are true to our knowledge.

(Signature of Petitioner 1) | (Signature of Petitioner 2)`,
        fields: [
            { label: 'City/Court Location', placeholder: 'e.g., Delhi, Mumbai, Bangalore' },
            { label: 'Husband\'s Full Name', placeholder: 'Full name of husband' },
            { label: 'Husband\'s Father Name', placeholder: 'Father\'s name' },
            { label: 'Husband\'s Address', placeholder: 'Complete residential address' },
            { label: 'Wife\'s Full Name', placeholder: 'Full name of wife' },
            { label: 'Wife\'s Father Name', placeholder: 'Father\'s name' },
            { label: 'Wife\'s Address', placeholder: 'Complete residential address' },
            { label: 'Date of Marriage', placeholder: 'DD/MM/YYYY' },
            { label: 'Place of Marriage', placeholder: 'City/Location where marriage took place' },
            { label: 'Date of Separation', placeholder: 'DD/MM/YYYY' },
            { label: 'Settlement Details', placeholder: 'Details of financial settlement or mention no alimony claimed' }
        ]
    },
    'motor-vehicle': {
        title: 'Motor Vehicle Act: Claim Petition',
        description: 'Petition for compensation under Motor Vehicle Act for accident claims.',
        template: `BEFORE THE MOTOR ACCIDENT CLAIMS TRIBUNAL AT [CITY]
M.A.C.P. NO. _______ OF 2024

IN THE MATTER OF:
[Claimant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Claimant
VERSUS
1. [Driver Name], R/o [Address] ... Respondent No. 1
2. [Owner Name], R/o [Address] ... Respondent No. 2
3. [Insurance Company Name] ... Respondent No. 3

PETITION UNDER SECTION 166 OF THE MOTOR VEHICLES ACT, 1988

The Claimant respectfully submits as under:

1. That on [Date of Accident] at about [Time] hours, the deceased/injured [Name] was [describe circumstances] when a [Vehicle Type] bearing registration no. [Registration Number] being driven by Respondent No. 1 in a rash and negligent manner hit the deceased/injured.

2. That due to the said accident, the deceased/injured sustained grievous injuries and was immediately rushed to [Hospital Name] where he/she was declared dead/treated for injuries.

3. That the deceased was [Age] years old and was earning Rs. [Monthly Income] per month as [Occupation].

4. That the vehicle was insured with Respondent No. 3 and the policy was valid at the time of accident.

5. That the accident occurred solely due to the rash and negligent driving of Respondent No. 1.

PRAYER
It is therefore prayed that this Hon'ble Tribunal may be pleased to:
a) Award compensation of Rs. [Amount] to the claimant.
b) Direct the respondents to pay the awarded amount with interest.
c) Pass any other order as deemed fit.

[Claimant Signature]
CLAIMANT

THROUGH
[Advocate Name]
Advocate for Claimant`,
        fields: [
            { label: 'City/Tribunal Location', placeholder: 'e.g., Delhi, Mumbai' },
            { label: 'Claimant Name', placeholder: 'Name of person filing claim' },
            { label: 'Claimant Address', placeholder: 'Complete address' },
            { label: 'Date of Accident', placeholder: 'DD/MM/YYYY' },
            { label: 'Time of Accident', placeholder: 'e.g., 10:30 AM' },
            { label: 'Deceased/Injured Name', placeholder: 'Name of victim' },
            { label: 'Vehicle Registration Number', placeholder: 'e.g., DL-01-AB-1234' },
            { label: 'Driver Name', placeholder: 'Name of driver' },
            { label: 'Vehicle Owner Name', placeholder: 'Name of vehicle owner' },
            { label: 'Insurance Company', placeholder: 'Name of insurance company' },
            { label: 'Hospital Name', placeholder: 'Where victim was taken' },
            { label: 'Victim Age', placeholder: 'Age at time of accident' },
            { label: 'Monthly Income', placeholder: 'Monthly earning of victim' },
            { label: 'Occupation', placeholder: 'Job/profession of victim' },
            { label: 'Compensation Amount', placeholder: 'Amount being claimed' }
        ]
    },
    'negotiable-instrument': {
        title: 'Negotiable Instrument Act: Cheque Bounce Case',
        description: 'Criminal complaint under Section 138 of the Negotiable Instruments Act for dishonor of cheque.',
        template: `BEFORE THE JUDICIAL MAGISTRATE FIRST CLASS AT [CITY]
CRIMINAL COMPLAINT CASE NO. _______ OF 2024

IN THE MATTER OF:
[Complainant Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Complainant
VERSUS
[Accused Name] S/o/D/o/W/o [Father's/Husband's Name], R/o [Address] ... Accused

CRIMINAL COMPLAINT UNDER SECTION 138 OF THE NEGOTIABLE INSTRUMENTS ACT, 1881

The Complainant respectfully submits as under:

1. That the complainant is a [Occupation/Business] and the accused is known to the complainant.

2. That on [Date of Transaction], the accused approached the complainant for financial assistance of Rs. [Amount] for [Purpose of Loan]. The complainant, believing in the good faith of the accused, advanced the said amount.

3. That in lieu of the said amount, the accused issued cheque no. [Cheque Number] dated [Cheque Date] for Rs. [Cheque Amount] drawn on [Bank Name], [Branch Name] in favor of the complainant.

4. That the complainant presented the said cheque for encashment on [Presentation Date] through his/her bank account, but the same was dishonored on [Dishonor Date] with the reason "[Reason for Dishonor]".

5. That immediately after the dishonor, the complainant issued a legal notice dated [Notice Date] through registered post to the accused demanding payment of the cheque amount within 15 days. The said notice was served on the accused on [Service Date].

6. That despite service of the legal notice and expiry of the statutory period of 15 days, the accused has failed to make payment of the cheque amount.

7. That the accused has thereby committed an offense punishable under Section 138 of the Negotiable Instruments Act, 1881.

PRAYER
It is therefore prayed that this Hon'ble Court may be pleased to:
a) Take cognizance of the offense committed by the accused.
b) Issue summons to the accused and try him/her for the offense.
c) Award appropriate punishment and compensation.
d) Pass any other order as deemed fit.

[Complainant Signature]
COMPLAINANT

THROUGH
[Advocate Name]
Advocate for Complainant

VERIFICATION:
Verified at [City] on this [Date] that the contents of the above complaint are true to my knowledge and belief.

[Complainant Signature]
COMPLAINANT`,
        fields: [
            { label: 'City/Court Location', placeholder: 'e.g., Delhi, Mumbai' },
            { label: 'Complainant Name', placeholder: 'Name of person filing complaint' },
            { label: 'Complainant Address', placeholder: 'Complete address of complainant' },
            { label: 'Accused Name', placeholder: 'Name of person who issued cheque' },
            { label: 'Accused Address', placeholder: 'Complete address of accused' },
            { label: 'Complainant Occupation', placeholder: 'e.g., Businessman, Service' },
            { label: 'Date of Transaction', placeholder: 'DD/MM/YYYY' },
            { label: 'Loan Amount', placeholder: 'Amount given as loan' },
            { label: 'Purpose of Loan', placeholder: 'Reason for taking loan' },
            { label: 'Cheque Number', placeholder: 'Cheque number' },
            { label: 'Cheque Date', placeholder: 'Date on cheque DD/MM/YYYY' },
            { label: 'Cheque Amount', placeholder: 'Amount written on cheque' },
            { label: 'Bank Name', placeholder: 'Name of bank' },
            { label: 'Branch Name', placeholder: 'Bank branch name' },
            { label: 'Presentation Date', placeholder: 'Date cheque presented DD/MM/YYYY' },
            { label: 'Dishonor Date', placeholder: 'Date cheque dishonored DD/MM/YYYY' },
            { label: 'Reason for Dishonor', placeholder: 'e.g., Insufficient Funds, Account Closed' },
            { label: 'Notice Date', placeholder: 'Date of legal notice DD/MM/YYYY' },
            { label: 'Service Date', placeholder: 'Date notice served DD/MM/YYYY' }
        ]
    },
    'legal-notice': {
        title: 'Legal Notice',
        description: 'Legal notice for civil or criminal matters demanding specific action or payment.',
        template: `LEGAL NOTICE

TO:
[Recipient Name]
S/o/D/o/W/o [Father's/Husband's Name]
R/o [Recipient Address]

THROUGH: [Advocate Name]
[Advocate Address]

Subject: Legal Notice under Section 138 of Negotiable Instruments Act / Civil matter

Sir/Madam,

I, [Client Name], S/o/D/o/W/o [Father's/Husband's Name], R/o [Client Address], through my advocate, do hereby serve upon you this Legal Notice and state as under:

1. That [Background Facts - describe the relationship/transaction between parties].

2. That [Main Issue - describe the specific problem/breach/default].

3. That [Consequences - describe the loss/damage caused].

4. That [Legal Position - mention relevant law/contract clause].

5. That you are hereby called upon to [Specific Demand - what action is required] within [Time Period] days from the receipt of this notice, failing which my client shall be constrained to initiate appropriate legal proceedings against you for recovery of the aforesaid amount along with interest, costs and compensation.

6. That this notice is served upon you with a view to avoid unnecessary litigation, however, if you fail to comply with the demand made herein, my client reserves the right to take appropriate legal action against you.

Take Notice that if you fail to [Specific Action Required] within the aforesaid period, my client will be left with no option but to approach the competent Court of Law for appropriate relief and you will be liable for all costs and consequences.

Dated: [Date]
Place: [Place]

[Advocate Signature]
[Advocate Name]
Advocate for [Client Name]`,
        fields: [
            { label: 'Recipient Name', placeholder: 'Name of person receiving notice' },
            { label: 'Recipient Address', placeholder: 'Complete address of recipient' },
            { label: 'Client Name', placeholder: 'Name of person sending notice' },
            { label: 'Client Address', placeholder: 'Complete address of client' },
            { label: 'Advocate Name', placeholder: 'Name of advocate' },
            { label: 'Advocate Address', placeholder: 'Address of advocate' },
            { label: 'Background Facts', placeholder: 'Describe relationship/transaction between parties' },
            { label: 'Main Issue', placeholder: 'Describe the specific problem/breach' },
            { label: 'Consequences', placeholder: 'Describe loss/damage caused' },
            { label: 'Legal Position', placeholder: 'Mention relevant law/contract clause' },
            { label: 'Specific Demand', placeholder: 'What action is required from recipient' },
            { label: 'Time Period', placeholder: 'Number of days to comply (e.g., 15, 30)' },
            { label: 'Date', placeholder: 'Date of notice DD/MM/YYYY' },
            { label: 'Place', placeholder: 'Place where notice is issued' }
        ]
    },
    'power-attorney': {
        title: 'Power of Attorney',
        description: 'General Power of Attorney document authorizing someone to act on your behalf.',
        template: `GENERAL POWER OF ATTORNEY

KNOW ALL MEN BY THESE PRESENTS that I, [Principal Name], S/o/D/o/W/o [Father's/Husband's Name], aged [Age] years, R/o [Principal Address], do hereby nominate, constitute and appoint [Attorney Name], S/o/D/o/W/o [Attorney Father's/Husband's Name], aged [Attorney Age] years, R/o [Attorney Address], as my true and lawful Attorney to act for me and on my behalf and in my name to do all or any of the following acts, deeds and things:

1. To purchase, sell, exchange, lease, mortgage, transfer or otherwise deal with any movable or immovable property belonging to me.

2. To execute, sign and deliver any deed, document, agreement, contract or instrument relating to any property.

3. To appear before any Court, Tribunal, Authority or Office and represent me in any legal proceedings.

4. To receive, collect and give receipts for any money, rent, income or other dues payable to me.

5. To operate my bank accounts, withdraw money, sign cheques and conduct banking transactions.

6. To pay taxes, bills, dues and discharge any liability on my behalf.

7. To engage advocates, counsel and other professionals for legal matters.

8. To do all other acts and things which I could do if personally present.

I hereby ratify and confirm all acts, deeds and things lawfully done by my said Attorney under this Power of Attorney.

This Power of Attorney shall remain in force until [Validity Period] unless revoked earlier by me in writing.

IN WITNESS WHEREOF, I have hereunto set my hand on this [Date] day of [Month], [Year] at [Place].

PRINCIPAL:
[Principal Signature]
[Principal Name]

ATTORNEY:
[Attorney Signature]  
[Attorney Name]

WITNESSES:
1. [Witness 1 Name]
   [Witness 1 Address]
   [Witness 1 Signature]

2. [Witness 2 Name]
   [Witness 2 Address]
   [Witness 2 Signature]

NOTARIZATION:
Verified and signed before me on [Date] at [Place].

[Notary Signature]
Notary Public`,
        fields: [
            { label: 'Principal Name', placeholder: 'Name of person giving power' },
            { label: 'Principal Address', placeholder: 'Complete address of principal' },
            { label: 'Principal Age', placeholder: 'Age of principal' },
            { label: 'Attorney Name', placeholder: 'Name of person receiving power' },
            { label: 'Attorney Address', placeholder: 'Complete address of attorney' },
            { label: 'Attorney Age', placeholder: 'Age of attorney' },
            { label: 'Validity Period', placeholder: 'e.g., revoked by me, one year, indefinite' },
            { label: 'Date', placeholder: 'Day DD' },
            { label: 'Month', placeholder: 'Month name' },
            { label: 'Year', placeholder: 'YYYY' },
            { label: 'Place', placeholder: 'City where executed' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 1 Address', placeholder: 'Address of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' },
            { label: 'Witness 2 Address', placeholder: 'Address of second witness' }
        ]
    },
    'affidavits': {
        title: 'Affidavit',
        description: 'Sworn statement of facts for legal or official purposes.',
        template: `AFFIDAVIT

I, [Deponent Name], S/o/D/o/W/o [Father's/Husband's Name], aged [Age] years, R/o [Address], do hereby solemnly affirm and state on oath as under:

1. That I am the deponent herein and I am competent to swear this affidavit.

2. That [Statement of Facts - First fact/circumstance].

3. That [Statement of Facts - Second fact/circumstance].

4. That [Statement of Facts - Third fact/circumstance].

5. That [Purpose of Affidavit - why this affidavit is being made].

6. That the facts stated above are true to the best of my knowledge and belief and nothing material has been concealed therefrom.

7. That I am making this affidavit for the purpose of [Specific Purpose] and for no other collateral purpose.

DEPONENT

VERIFICATION:
Verified at [Place] on this [Date] that the contents of the above affidavit are true to my knowledge and belief and no part of it is false and nothing material has been concealed therefrom.

DEPONENT
[Deponent Signature]
[Deponent Name]

Sworn before me on this [Date] at [Place].

[Oath Commissioner/Notary Signature]
Oath Commissioner/Notary Public`,
        fields: [
            { label: 'Deponent Name', placeholder: 'Name of person making affidavit' },
            { label: 'Deponent Address', placeholder: 'Complete address of deponent' },
            { label: 'Age', placeholder: 'Age of deponent' },
            { label: 'First Fact', placeholder: 'First statement of fact' },
            { label: 'Second Fact', placeholder: 'Second statement of fact' },
            { label: 'Third Fact', placeholder: 'Third statement of fact' },
            { label: 'Purpose of Affidavit', placeholder: 'Why this affidavit is being made' },
            { label: 'Specific Purpose', placeholder: 'Specific legal/official purpose' },
            { label: 'Date', placeholder: 'Date of affidavit DD/MM/YYYY' },
            { label: 'Place', placeholder: 'Place where affidavit is made' }
        ]
    },
    'agreements': {
        title: 'Agreement',
        description: 'General agreement document between two or more parties.',
        template: `AGREEMENT

THIS AGREEMENT is made on [Date] between [Party 1 Name], S/o/D/o/W/o [Party 1 Father's/Husband's Name], R/o [Party 1 Address] (hereinafter called "Party 1") of the ONE PART and [Party 2 Name], S/o/D/o/W/o [Party 2 Father's/Husband's Name], R/o [Party 2 Address] (hereinafter called "Party 2") of the OTHER PART.

WHEREAS [Background/Recitals - describe the background and purpose of agreement];

NOW THEREFORE, in consideration of the mutual covenants and agreements contained herein, the parties agree as follows:

1. SCOPE OF AGREEMENT:
   [Scope Description - what the agreement covers]

2. OBLIGATIONS OF PARTY 1:
   [Party 1 Obligations - what Party 1 must do]

3. OBLIGATIONS OF PARTY 2:
   [Party 2 Obligations - what Party 2 must do]

4. CONSIDERATION:
   [Consideration Details - payment/exchange terms]

5. DURATION:
   This agreement shall remain in force from [Start Date] to [End Date].

6. TERMINATION:
   [Termination Clause - how agreement can be terminated]

7. DISPUTE RESOLUTION:
   Any dispute arising out of this agreement shall be resolved through [Dispute Resolution Method].

8. GOVERNING LAW:
   This agreement shall be governed by the laws of India.

9. MISCELLANEOUS:
   [Additional Terms - any other specific terms]

IN WITNESS WHEREOF, the parties have executed this agreement on the date first written above.

PARTY 1:                           PARTY 2:
[Party 1 Signature]               [Party 2 Signature]
[Party 1 Name]                    [Party 2 Name]

WITNESSES:
1. [Witness 1 Name]               2. [Witness 2 Name]
   [Witness 1 Signature]             [Witness 2 Signature]`,
        fields: [
            { label: 'Date', placeholder: 'Date of agreement DD/MM/YYYY' },
            { label: 'Party 1 Name', placeholder: 'Name of first party' },
            { label: 'Party 1 Address', placeholder: 'Address of first party' },
            { label: 'Party 2 Name', placeholder: 'Name of second party' },
            { label: 'Party 2 Address', placeholder: 'Address of second party' },
            { label: 'Background/Recitals', placeholder: 'Background and purpose of agreement' },
            { label: 'Scope Description', placeholder: 'What the agreement covers' },
            { label: 'Party 1 Obligations', placeholder: 'What Party 1 must do' },
            { label: 'Party 2 Obligations', placeholder: 'What Party 2 must do' },
            { label: 'Consideration Details', placeholder: 'Payment/exchange terms' },
            { label: 'Start Date', placeholder: 'Agreement start date DD/MM/YYYY' },
            { label: 'End Date', placeholder: 'Agreement end date DD/MM/YYYY' },
            { label: 'Termination Clause', placeholder: 'How agreement can be terminated' },
            { label: 'Dispute Resolution Method', placeholder: 'e.g., arbitration, mediation, courts' },
            { label: 'Additional Terms', placeholder: 'Any other specific terms' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' }
        ]
    },
    'rent': {
        title: 'Rent Agreement',
        description: 'Rental agreement between landlord and tenant for residential or commercial property.',
        template: `RENT AGREEMENT

THIS RENT AGREEMENT is made on [Date] between [Landlord Name], S/o/D/o/W/o [Landlord Father's/Husband's Name], R/o [Landlord Address] (hereinafter called "LANDLORD") of the ONE PART and [Tenant Name], S/o/D/o/W/o [Tenant Father's/Husband's Name], R/o [Tenant Address] (hereinafter called "TENANT") of the OTHER PART.

WHEREAS the Landlord is the absolute owner of the property described below and is willing to let out the same to the Tenant on the terms and conditions mentioned hereinafter:

PROPERTY DETAILS:
[Property Description] situated at [Property Address], measuring approximately [Property Size].

NOW THEREFORE, the parties agree as follows:

1. LEASE PERIOD:
   The lease period shall be for [Lease Duration] commencing from [Start Date] and ending on [End Date].

2. RENT:
   The monthly rent shall be Rs. [Monthly Rent] payable on or before [Payment Date] of each month.

3. SECURITY DEPOSIT:
   The Tenant has paid Rs. [Security Deposit] as security deposit which shall be refunded at the time of vacating the premises after adjusting any dues.

4. MAINTENANCE:
   [Maintenance Responsibility - who is responsible for what maintenance]

5. UTILITIES:
   [Utility Responsibility - electricity, water, gas bills responsibility]

6. USE OF PREMISES:
   The premises shall be used only for [Purpose of Use] and not for any other purpose.

7. RESTRICTIONS:
   [Restrictions - any specific restrictions on tenant]

8. TERMINATION:
   Either party may terminate this agreement by giving [Notice Period] days written notice.

9. DEFAULT:
   In case of default in payment of rent for [Default Period], the Landlord may terminate the agreement.

10. REGISTRATION:
    [Registration Clause - whether agreement will be registered]

IN WITNESS WHEREOF, the parties have executed this agreement on the date first written above.

LANDLORD:                         TENANT:
[Landlord Signature]             [Tenant Signature]
[Landlord Name]                  [Tenant Name]

WITNESSES:
1. [Witness 1 Name]              2. [Witness 2 Name]
   [Witness 1 Signature]            [Witness 2 Signature]`,
        fields: [
            { label: 'Date', placeholder: 'Date of agreement DD/MM/YYYY' },
            { label: 'Landlord Name', placeholder: 'Name of property owner' },
            { label: 'Landlord Address', placeholder: 'Address of landlord' },
            { label: 'Tenant Name', placeholder: 'Name of tenant' },
            { label: 'Tenant Address', placeholder: 'Address of tenant' },
            { label: 'Property Description', placeholder: 'Type of property (flat, house, shop, etc.)' },
            { label: 'Property Address', placeholder: 'Complete address of rental property' },
            { label: 'Property Size', placeholder: 'Size/area of property' },
            { label: 'Lease Duration', placeholder: 'e.g., 11 months, 2 years' },
            { label: 'Start Date', placeholder: 'Lease start date DD/MM/YYYY' },
            { label: 'End Date', placeholder: 'Lease end date DD/MM/YYYY' },
            { label: 'Monthly Rent', placeholder: 'Monthly rent amount' },
            { label: 'Payment Date', placeholder: 'Date of month for rent payment (e.g., 5th)' },
            { label: 'Security Deposit', placeholder: 'Security deposit amount' },
            { label: 'Maintenance Responsibility', placeholder: 'Who handles maintenance' },
            { label: 'Utility Responsibility', placeholder: 'Who pays utility bills' },
            { label: 'Purpose of Use', placeholder: 'e.g., residential, commercial' },
            { label: 'Restrictions', placeholder: 'Any restrictions on tenant' },
            { label: 'Notice Period', placeholder: 'Notice period for termination (days)' },
            { label: 'Default Period', placeholder: 'Default period before termination' },
            { label: 'Registration Clause', placeholder: 'Registration details if applicable' },
            { label: 'Witness 1 Name', placeholder: 'Name of first witness' },
            { label: 'Witness 2 Name', placeholder: 'Name of second witness' }
        ]
    }
    // We'll add more categories as requested
};

const CategoryDetailsModal = ({ isVisible, onClose, category, onSubmit }) => {
    const [formData, setFormData] = useState({});
    const [additionalDetails, setAdditionalDetails] = useState('');

    useEffect(() => {
        if (isVisible && category) {
            // Reset form when modal opens
            setFormData({});
            setAdditionalDetails('');
        }
    }, [isVisible, category]);

    if (!isVisible || !category) return null;

    const template = CATEGORY_TEMPLATES[category.id];
    if (!template) {
        // Fallback for categories without templates yet
        return (
            <div className={styles.modalBackdrop} onClick={onClose}>
                <div className={`${styles.modalContentWrapper} ${styles.categoryDetailsModal}`} onClick={(e) => e.stopPropagation()}>
                    <div className={styles.modalHeader}>
                        <h3><i className={category.icon + ' mr-2'}></i> {category.title}</h3>
                        <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                    </div>
                    <div className={styles.modalBody}>
                        <p>Template for this category is being prepared. Please provide details below:</p>
                        <div className={styles.formGroup}>
                            <label>Additional Details</label>
                            <textarea
                                className={styles.formControl}
                                rows="6"
                                value={additionalDetails}
                                onChange={(e) => setAdditionalDetails(e.target.value)}
                                placeholder="Please provide specific details for your document..."
                            />
                        </div>
                    </div>
                    <div className={styles.modalFooter}>
                        <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                            Cancel
                        </button>
                        <button 
                            onClick={() => onSubmit(category, {}, additionalDetails)}
                            className={`${styles.actionButton} ${styles.actionButtonPrimary}`}
                        >
                            Generate Draft
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    const handleInputChange = (index, value) => {
        setFormData(prev => ({
            ...prev,
            [index]: value
        }));
    };

    const handleSubmit = () => {
        onSubmit(category, formData, additionalDetails, template);
        onClose();
    };

    return (
        <div className={styles.modalBackdrop} onClick={onClose}>
            <div className={`${styles.modalContentWrapper} ${styles.categoryDetailsModal}`} onClick={(e) => e.stopPropagation()}>
                <div className={styles.modalHeader}>
                    <h3><i className={category.icon + ' mr-2'}></i> {template.title}</h3>
                    <button onClick={onClose} className={styles.modalCloseBtn}>×</button>
                </div>
                <div className={styles.modalBody}>
                    <div className={styles.templateDescription}>
                        <p>{template.description}</p>
                    </div>
                    
                    <div className={styles.formSection}>
                        <h4>Document Details</h4>
                        <div className={styles.formGrid}>
                            {template.fields.map((field, index) => (
                                <div key={index} className={styles.formGroup}>
                                    <label>{field.label}</label>
                                    <input
                                        type="text"
                                        className={styles.formControl}
                                        placeholder={field.placeholder}
                                        value={formData[index] || ''}
                                        onChange={(e) => handleInputChange(index, e.target.value)}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className={styles.formSection}>
                        <h4>Additional Details</h4>
                        <div className={styles.formGroup}>
                            <label>Any specific details or modifications needed</label>
                            <textarea
                                className={styles.formControl}
                                rows="4"
                                value={additionalDetails}
                                onChange={(e) => setAdditionalDetails(e.target.value)}
                                placeholder="Provide any additional information or specific requirements for your document..."
                            />
                        </div>
                    </div>
                </div>
                <div className={styles.modalFooter}>
                    <button onClick={onClose} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                        Cancel
                    </button>
                    <button 
                        onClick={handleSubmit}
                        className={`${styles.actionButton} ${styles.actionButtonPrimary}`}
                    >
                        Generate Draft
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CategoryDetailsModal;