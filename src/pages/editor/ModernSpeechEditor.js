import { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import LoadingModal from '../../components/common/LoadingModal.jsx';
import styles from './main.module.css';
import { getUserProfile } from '../../services/api';

import {
    dictationLanguages,
    translationLanguages,
    transliterationLanguages,
    COMMANDS,
} from './constants/speechConstants';

import { useAlerts } from './utils/alertUtils';
import useSpeechRecognition from './hooks/useSpeechRecognition';
import useFontConversion from './hooks/useFontConversion';
import useTransliteration from './hooks/useTransliteration';
import useAIFeatures from './hooks/useAIFeatures';
import useEditorManagement from './hooks/useEditorManagement';
import useUserLicense from './hooks/useUserLicense';

import SaveContentModal from './components/SaveContentModal';
import CommandsModal from './components/CommandsModal';
import DraftCategoriesModal from './components/DraftCategoriesModal';
import CategoryDetailsModal from './components/CategoryDetailsModal';
import TranslationModal from './components/TranslationModal';
import FontConversionModal from './components/FontConversionModal';

function ModernSpeechEditor() {
    const { user, logoutUser } = useAuth();
    const navigate = useNavigate();
    const { state } = useLocation();
    const [editorHtml, setEditorHtml] = useState(state?.importedHtml || '');
    const [dictationLang, setDictationLang] = useState('en-in');
    const [translateLang, setTranslateLang] = useState('en');
    const [transliterationLang, setTransliterationLang] = useState('hi');
    const [isTransliterationEnabled, setIsTransliterationEnabled] = useState(false);
    const [commandTab, setCommandTab] = useState('en');
    const [isVerifyingToken, setIsVerifyingToken] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [categoryDetailsModalVisible, setCategoryDetailsModalVisible] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState(null);

    const quillRef = useRef(null);
    const micButtonRef = useRef(null);

    const { error, successMessage, showSuccess, showError, clearAlerts } = useAlerts();

    const handleClear = useCallback(() => {
        clearAlerts();
        if (window.confirm("Are you sure you want to clear the editor?")) {
            quillRef.current?.getEditor().setText('');
            setEditorHtml('');
            if (listening) {
                stopRecognition();
            }
            showSuccess("Editor cleared.");
        }
    }, [clearAlerts, showSuccess]);

    const {
        listening,
        setListening,
        browserSupportsSpeechRecognition,
        isMicrophoneAvailable,
        recognitionRef,
        startRecognition,
        stopRecognition,
    } = useSpeechRecognition(quillRef, dictationLang, showError, handleClear);

    const {
        isConvertingFont,
        conversionOutput,
        fontConversionModalVisible,
        selectedFont,
        setFontConversionModalVisible,
        handleFontConversion,
    } = useFontConversion(quillRef, showError, clearAlerts);

    const {
        isTransliterating,
        handleTransliteration,
    } = useTransliteration(quillRef, transliterationLang, isTransliterationEnabled, showError);

    const {
        isTranslating,
        translatedText,
        translationModalVisible,
        setTranslationModalVisible,
        isFixingGrammar,
        isExpandingContent,
        isGeneratingDraft,
        draftModalVisible,
        setDraftModalVisible,
        isLoadingModalVisible,
        loadingMessage,
        handleFixGrammar,
        handleExpandContent,
        handleGenerateDraft,
        handleConfirmGenerateDraft,
        handleTranslate,
    } = useAIFeatures(quillRef, showError, showSuccess, clearAlerts);

    const {
        isSavingContent,
        saveModalVisible,
        setSaveModalVisible,
        contentTitle,
        setContentTitle,
        contentDescription,
        setContentDescription,
        handleCopy,
        handlePrint,
        handleSave,
        handleConfirmSave,
    } = useEditorManagement(quillRef, showError, showSuccess, clearAlerts, handleClear);

    const { expiryDisplayText, actualExpiryDate } = useUserLicense(user);

    const handleEditorChange = (content, delta, source, editor) => {
        setEditorHtml(content);
        if (source === 'user' && isTransliterationEnabled) {
            const lastOp = delta.ops[delta.ops.length - 1];
            if (lastOp && lastOp.insert === ' ') {
                const range = editor.getSelection();
                if (range) {
                    const textBeforeCursor = editor.getText(0, range.index - 1);
                    const words = textBeforeCursor.trim().split(/\s+/);
                    const lastWord = words.pop();
                    if (lastWord) {
                        handleTransliteration(lastWord);
                    }
                }
            }
        }
    };

    const quillModules = {
        toolbar: [
            [{ 'font': [] }, { 'size': [] }], ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }], [{ 'script': 'sub' }, { 'script': 'super' }],
            [{ 'header': '1' }, { 'header': '2' }, 'blockquote', 'code-block'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }, { 'align': [] }], ['link'], ['clean']
        ],
    };
    
    useEffect(() => {
        const baseLangCode = dictationLang.split('-')[0].toLowerCase();
        if (COMMANDS[baseLangCode]) setCommandTab(baseLangCode);
        else setCommandTab('en');
    }, [dictationLang]);

    useEffect(() => {
        const button = micButtonRef.current;
        if (!button) return;

        const textSpan = button.querySelector('#button-text-span');
        const icon = button.querySelector('i.fas');

        if (isVerifyingToken) {
            if (textSpan) textSpan.textContent = 'Verifying...';
            button.classList.remove(styles.actionButtonListening);
        } else if (listening) {
            if (textSpan) textSpan.textContent = 'Stop Listening';
            if (icon) icon.className = 'fas fa-microphone-alt-slash mr-1';
            button.classList.add(styles.actionButtonListening);
        } else {
            if (textSpan) textSpan.textContent = 'Start Listening';
            if (icon) icon.className = 'fas fa-microphone-alt mr-1';
            button.classList.remove(styles.actionButtonListening);
        }
    }, [listening, isVerifyingToken]);


    const handleDictationLangChange = (event) => {
        const newLang = event.target.value;
        setDictationLang(newLang);
        if (listening) {
            stopRecognition();
        }
    };

    const handleMicClick = async () => {
        clearAlerts();
        if (!browserSupportsSpeechRecognition) { showError("Browser doesn't support speech recognition."); return; }
        if (!isMicrophoneAvailable) { showError("Microphone access denied or unavailable. Please allow access."); return; }
        if (!recognitionRef.current) { showError("Speech recognition is not ready. Please try refreshing."); return; }

        if (!listening) {
            setIsVerifyingToken(true);
            try {
                await getUserProfile();
                setListening(true);
                startRecognition();
            } catch (authError) {
                console.error("handleMicClick: Session verification failed or start error:", authError);
                const errorMessage = authError.response?.data?.message || authError.message || "Session error.";
                showError(`Failed to start: ${errorMessage}`);
                if (authError.response?.status === 401 || authError.response?.status === 403) {
                    logoutUser(); navigate('/user/login');
                }
            } finally {
                setIsVerifyingToken(false);
            }
        } else {
            setListening(false);
            stopRecognition();
        }
    };

    const handleLogout = useCallback(() => {
        clearAlerts();
        logoutUser();
        navigate('/user/login');
    }, [logoutUser, navigate, clearAlerts]);

    const showCommandsModal = useCallback(() => { clearAlerts(); setIsModalVisible(true); }, [clearAlerts]);
    const hideCommandsModal = useCallback(() => setIsModalVisible(false), []);
    const selectCommandTab = useCallback((tab) => setCommandTab(tab), []);

    const handleCategorySelect = useCallback((category) => {
        clearAlerts();
        setSelectedCategory(category);
        setDraftModalVisible(false); // Close the categories modal
        setCategoryDetailsModalVisible(true); // Open the details modal
    }, [clearAlerts, setDraftModalVisible]);

    const handleCategoryDetailsSubmit = useCallback((category, formData, additionalDetails, template) => {
        clearAlerts();
        setCategoryDetailsModalVisible(false);
        // Use the enhanced handleConfirmGenerateDraft with template support
        handleConfirmGenerateDraft(category, formData, additionalDetails, template);
    }, [clearAlerts, handleConfirmGenerateDraft]);

    const hasEditorText = quillRef.current?.getEditor().getLength() > 1;

    return (
        <div className={styles.pageWrapper}>
            <main className={styles.mainContent}>
                <header className={styles.topNavbar}>
                    <Link to="/user/editor" className={styles.navbarBrand}>Daily Speaking</Link>
                    <div className={styles.navbarInfo}>
                        <span title={actualExpiryDate || ''}><i className="far fa-calendar-alt mr-1"></i> License: {expiryDisplayText}</span>
                        <span><i className="fas fa-phone-alt mr-1"></i> Enquiry: +91-**********</span>
                        <button onClick={handleLogout} className={styles.logoutButton}>
                            <i className="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </div>
                </header>

                <div className={styles.alertContainer}>
                    {error && <div className={`${styles.alert} ${styles.alertDanger}`}>{error} <button onClick={clearAlerts} className={styles.alertClose}>×</button></div>}
                    {successMessage && <div className={`${styles.alert} ${styles.alertSuccess}`}>{successMessage} <button onClick={clearAlerts} className={styles.alertClose}>×</button></div>}
                </div>

                <div className={styles.actionsToolbar}>
                    <div className={styles.actionsToolbarContent}>
                        <button onClick={() => navigate('/user/content-history')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                            <i className="fas fa-history mr-1"></i> Content History
                        </button>
                        <button onClick={() => navigate('/user/chat-with-ai')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                            <i className="fas fa-comments mr-1"></i> Chat with AI
                        </button>
                        <button onClick={() => navigate('/user/converter')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`}>
                            <i className="fas fa-font mr-1"></i> Font Converter
                        </button>
                        <button onClick={handleCopy} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={!hasEditorText}><i className="fas fa-copy mr-1"></i> Copy</button>
                        <button onClick={handlePrint} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={!hasEditorText}><i className="fas fa-print mr-1"></i> Print</button>
                        <button onClick={handleFixGrammar} className={`${styles.actionButton} ${styles.actionButtonHighlight}`} disabled={isFixingGrammar || !hasEditorText}>
                            {isFixingGrammar ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-check-double mr-1"></i> Fix Grammar</>}
                        </button>
                        <button onClick={handleExpandContent} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isExpandingContent || !hasEditorText}>
                            {isExpandingContent ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-expand-alt mr-1"></i> Expand Content</>}
                        </button>
                        <button onClick={handleGenerateDraft} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isGeneratingDraft}>
                            {isGeneratingDraft ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-file-alt mr-1"></i> Generate Draft</>}
                        </button>
                        <button onClick={showCommandsModal} className={`${styles.actionButton} ${styles.actionButtonInfo}`}><i className="fas fa-terminal mr-1"></i> Commands</button>
                        <button onClick={handleClear} className={`${styles.actionButton} ${styles.actionButtonDanger}`} disabled={!hasEditorText || listening}><i className="fas fa-trash mr-1"></i> Clear</button>
                    </div>
                </div>

                <div className={styles.editorWrapper}>
                    <ReactQuill
                        ref={quillRef}
                        theme="snow"
                        value={editorHtml}
                        onChange={handleEditorChange}
                        modules={quillModules}
                        placeholder="Start speaking or typing here..."
                        className={`${styles.quillEditor} ${isFixingGrammar ? styles.grammarFixAnimation : ''}`}
                    />
                    <button
                        onClick={handleSave}
                        className={styles.fabButton}
                        disabled={isSavingContent || !hasEditorText}
                        title="Save Content"
                    >
                        {isSavingContent ? <LoadingSpinner small centered={false} /> : <i className="fas fa-save"></i>}
                    </button>
                </div>
            </main>

            <aside className={styles.sidebar}>
                <div className={`${styles.sidebarCard} ${styles.sidebarCardNarrow} ${styles.speechInputCard}`}>
                    <div className={styles.cardHeader}>
                        <h3 className={styles.sidebarTitle}><i className="fas fa-microphone-alt mr-2"></i> Speech Input</h3>
                    </div>
                    <div className={styles.cardBody}>
                        <div className={styles.controlGroup}>
                            <label htmlFor="dictationLang">Dictation Language</label>
                            <select id="dictationLang" className={`${styles.formControlSm} ${styles.formControlNarrow}`} value={dictationLang} onChange={handleDictationLangChange} disabled={listening || isVerifyingToken}>
                                {dictationLanguages.map(lang => <option key={lang.value} value={lang.value}>{lang.label}</option>)}
                            </select>
                            {!browserSupportsSpeechRecognition && (<p className={styles.micWarning}>Speech recognition not supported.</p>)}
                            {browserSupportsSpeechRecognition && !isMicrophoneAvailable && (<p className={styles.micWarning}>Microphone access needed.</p>)}
                        </div>
                        <button
                            ref={micButtonRef}
                            onClick={handleMicClick}
                            className={`${styles.actionButton} ${styles.actionButtonPrimary} ${styles.btnBlock} ${listening && !isVerifyingToken ? styles.actionButtonListening : ''}`}
                            disabled={!browserSupportsSpeechRecognition || !isMicrophoneAvailable || !recognitionRef.current || (isVerifyingToken && listening)}
                            aria-pressed={listening && !isVerifyingToken}
                            title={listening && !isVerifyingToken ? "Stop speech recognition" : "Start speech recognition"}
                            id="speech-recognition-button"
                        >
                            {isVerifyingToken ? (
                                <LoadingSpinner small centered={false} />
                            ) : (
                                <i className={`fas ${listening ? 'fa-microphone-alt-slash' : 'fa-microphone-alt'} mr-1`}></i>
                            )}
                            <span id="button-text-span">
                                {isVerifyingToken ? 'Verifying...' : listening ? 'Stop Listening' : 'Start Listening'}
                            </span>
                            {listening && !isVerifyingToken && <span className={`spinner-border spinner-border-sm ml-2 ${styles.listeningSpinner}`} role="status" aria-hidden="true"></span>}
                        </button>
                        {listening && !isVerifyingToken && (
                            <div id="listening-indicator" style={{ marginTop: '10px', fontSize: '0.85rem', color: '#4caf50' }}>
                                <i className="fas fa-wave-square mr-1"></i> Listening...
                            </div>
                        )}
                    </div>
                </div>

                <div className={`${styles.sidebarCard} ${styles.sidebarCardNarrow} ${styles.translationCard}`}>
                    <div className={styles.cardHeader}> <h3 className={styles.sidebarTitle}><i className="fas fa-language mr-2"></i> Translation</h3> </div>
                    <div className={styles.cardBody}>
                        <div className={styles.controlGroup}>
                            <label htmlFor="translateLang">Translate Editor Text To</label>
                            <select id="translateLang" className={`${styles.formControlSm} ${styles.formControlNarrow}`} value={translateLang} onChange={(e) => setTranslateLang(e.target.value)} disabled={isTranslating}>
                                {translationLanguages.map(lang => <option key={lang.value} value={lang.value}>{lang.label}</option>)}
                            </select>
                        </div>
                        <button onClick={() => handleTranslate(translateLang)} className={`${styles.actionButton} ${styles.actionButtonSecondary} ${styles.btnBlock}`} disabled={isTranslating || !hasEditorText}>
                            {isTranslating ? <LoadingSpinner small centered={false} /> : <><i className="fas fa-exchange-alt mr-1"></i> Translate</>}
                        </button>
                    </div>
                </div>

                <div className={`${styles.sidebarCard} ${styles.sidebarCardNarrow} ${styles.transliterationCard}`}>
                    <div className={styles.cardHeader}>
                        <h3 className={styles.sidebarTitle}><i className="fas fa-keyboard mr-2"></i> Transliterate</h3>
                    </div>
                    <div className={styles.cardBody}>
                        <div className={styles.controlGroup}>
                            <label htmlFor="transliterationLang">Transliterate From English To</label>
                            <select
                                id="transliterationLang"
                                className={`${styles.formControlSm} ${styles.formControlNarrow}`}
                                value={transliterationLang}
                                onChange={(e) => setTransliterationLang(e.target.value)}
                                disabled={isTransliterating}
                            >
                                {transliterationLanguages.map(lang => <option key={lang.value} value={lang.value}>{lang.label}</option>)}
                            </select>
                        </div>
                        <button
                            onClick={() => setIsTransliterationEnabled(!isTransliterationEnabled)}
                            className={`${styles.actionButton} ${isTransliterationEnabled ? styles.actionButtonDanger : styles.actionButtonSecondary} ${styles.btnBlock}`}
                            disabled={isTransliterating}
                        >
                            {isTransliterating ? <LoadingSpinner small centered={false} /> : (isTransliterationEnabled ? 'Disable' : 'Enable')}
                        </button>
                    </div>
                </div>

                <div className={styles.sidebarCard}>
                    <div className={styles.cardHeader}> <h3 className={styles.sidebarTitle}><i className="fas fa-font mr-2"></i> Font Conversion</h3> </div>
                    <div className={styles.cardBody}>
                        <div className={styles.fontConversionButtons}>
                            <button onClick={() => handleFontConversion('kruti')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isConvertingFont || !hasEditorText}>To KrutiDev</button>
                            <button onClick={() => handleFontConversion('preeti')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isConvertingFont || !hasEditorText}>To Preeti</button>
                            <button onClick={() => handleFontConversion('shree')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isConvertingFont || !hasEditorText}>To Shree</button>
                            <button onClick={() => handleFontConversion('shivaji')} className={`${styles.actionButton} ${styles.actionButtonSecondary}`} disabled={isConvertingFont || !hasEditorText}>To Shivaji</button>
                        </div>
                    </div>
                </div>


            </aside>

            <SaveContentModal
                isVisible={saveModalVisible}
                onClose={() => setSaveModalVisible(false)}
                contentTitle={contentTitle}
                setContentTitle={setContentTitle}
                contentDescription={contentDescription}
                setContentDescription={setContentDescription}
                onConfirmSave={handleConfirmSave}
                isSavingContent={isSavingContent}
            />

            <CommandsModal
                isVisible={isModalVisible}
                onClose={hideCommandsModal}
                commandTab={commandTab}
                selectCommandTab={selectCommandTab}
            />

            <DraftCategoriesModal
                isVisible={draftModalVisible}
                onClose={() => setDraftModalVisible(false)}
                onCategorySelect={handleCategorySelect}
            />

            <CategoryDetailsModal
                isVisible={categoryDetailsModalVisible}
                onClose={() => setCategoryDetailsModalVisible(false)}
                category={selectedCategory}
                onSubmit={handleCategoryDetailsSubmit}
            />

            <TranslationModal
                isVisible={translationModalVisible}
                onClose={() => setTranslationModalVisible(false)}
                translatedText={translatedText}
                onCopyAndClose={() => {
                    navigator.clipboard.writeText(translatedText)
                        .then(() => showSuccess("Translation copied!"))
                        .catch(() => showError("Copy failed."));
                    setTranslationModalVisible(false);
                }}
            />

            <FontConversionModal
                isVisible={fontConversionModalVisible}
                onClose={() => setFontConversionModalVisible(false)}
                selectedFont={selectedFont}
                conversionOutput={conversionOutput}
                onCopyAndClose={() => {
                    navigator.clipboard.writeText(conversionOutput)
                        .then(() => showSuccess("Conversion output copied!"))
                        .catch(() => showError("Copy failed."));
                    setFontConversionModalVisible(false);
                }}
            />

            <LoadingModal isVisible={isLoadingModalVisible} message={loadingMessage} />
        </div>
    );
}

export default ModernSpeechEditor;
