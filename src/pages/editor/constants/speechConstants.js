export const dictationLanguages = [
    { value: 'en-in', label: 'English (India)' }, { value: 'mr', label: 'मराठी (Marathi)' }, { value: 'hi', label: 'हिन्दी (Hindi)' },
    { value: 'bn', label: 'বাংলা (Bangla Bharat)' }, { value: 'gu', label: 'ગુજરાતી (Gujrati)' }, { value: 'te-in', label: 'తెలుగు (Telugu)' },
    { value: 'kn-in', label: 'ಕನ್ನಡ (Kannada)' }, { value: 'ml-in', label: 'മലയാളം (Malayalam)' }, { value: 'ur-in', label: 'اردू (ಭಾರತ) (Urdu)' },
    { value: 'en-us', label: 'English (US)' }, { value: 'es-es', label: 'Español (España)' }, { value: 'fr-fr', label: 'Français' },
    { value: 'zh', label: '中文 (普通话)' }, { value: 'ja', label: '日本語' }, { value: 'ru', label: 'Русский' }
];

export const translationLanguages = [
    { value: 'en', label: 'English' }, { value: 'hi', label: 'Hindi' }, { value: 'mr', label: 'Marathi' },
    { value: 'gu', label: 'Gujarati' }, { value: 'sd', label: 'Sindhi' }, { value: 'kn', label: 'Kannada' },
    { value: 'bn', label: 'Bengali' }, { value: 'ur', label: 'Urdu' }, { value: 'ta', label: 'Tamil' },
    { value: 'te', label: 'Telugu' }, { value: 'pa', label: 'Punjabi' },
    { value: 'zh-CN', label: 'Chinese (Simplified)' }, { value: 'ja', label: 'Japanese' }, { value: 'ru', label: 'Russian' },
];

export const transliterationLanguages = [
    { value: 'am', label: 'Amharic' },
    { value: 'ar', label: 'Arabic' },
    { value: 'bn', label: 'Bengali' },
    { value: 'el', label: 'Greek' },
    { value: 'gu', label: 'Gujarati' },
    { value: 'hi', label: 'Hindi' },
    { value: 'kn', label: 'Kannada' },
    { value: 'ml', label: 'Malayalam' },
    { value: 'mr', label: 'Marathi' },
    { value: 'ne', label: 'Nepali' },
    { value: 'or', label: 'Oriya' },
    { value: 'fa', label: 'Persian' },
    { value: 'pa', label: 'Punjabi' },
    { value: 'ru', label: 'Russian' },
    { value: 'sa', label: 'Sanskrit' },
    { value: 'si', label: 'Sinhalese' },
    { value: 'sr', label: 'Serbian' },
    { value: 'ta', label: 'Tamil' },
    { value: 'te', label: 'Telugu' },
    { value: 'ti', label: 'Tigrinya' },
    { value: 'ur', label: 'Urdu' }
];
export const COMMANDS = {
    'en': [
        { text: "comma", symbol: "," }, { text: "full stop", symbol: "." }, { text: "period", symbol: "." }, { text: "dot", symbol: "." },
        { text: "semi colon", symbol: ";" }, { text: "colon", symbol: ":" }, { text: "question mark", symbol: "?" },
        { text: "dash", symbol: "-" }, { text: "hyphen", symbol: "-" }, { text: "oblique", symbol: "/" }, { text: "slash", symbol: "/" },
        { text: "exclamation mark", symbol: "!" }, { text: "exclamation point", symbol: "!" }, { text: "at sign", symbol: "@" }, { text: "hash sign", symbol: "#" },
        { text: "dollar sign", symbol: "$" }, { text: "percent sign", symbol: "%" }, { text: "ampersand", symbol: "&" },
        { text: "asterisk", symbol: "*" }, { text: "plus sign", symbol: "+" }, { text: "equals sign", symbol: "=" },
        { text: "left parenthesis", symbol: "(" }, { text: "right parenthesis", symbol: ")" }, { text: "left bracket", symbol: "[" },
        { text: "right bracket", symbol: "]" }, { text: "left curly brace", symbol: "{" }, { text: "right curly brace", symbol: "}" },
        { text: "less than sign", symbol: "<" }, { text: "greater than sign", symbol: ">" }, { text: "underscore", symbol: "_" },
        { text: "new line", action: "newline" }, { text: "next line", action: "newline" },
        { text: "new paragraph", action: "newparagraph" }, { text: "next paragraph", action: "newparagraph" },
        { text: "stop listening", action: "stopMic" }, { text: "clear editor", action: "clearEditor" }
    ],
    'hi': [
        { text: "अल्पविराम", symbol: "," }, { text: "पूर्णविराम", symbol: "।" }, { text: "अर्धविराम", symbol: ";" },
        { text: "उपविराम", symbol: ":" }, { text: "प्रश्नवाचक चिन्ह", symbol: "?" }, { text: "योजक चिन्ह", symbol: "-" },
        { text: "निर्देशक चिन्ह", symbol: "—" }, { text: "तिरछा डंडा", symbol: "/" }, { text: "विस्मयादिबोधक चिन्ह", symbol: "!" },
        { text: "एट द रेट", symbol: "@" }, { text: "हैशटैग", symbol: "#" }, { text: "डॉलर", symbol: "$" },
        { text: "प्रतिशत", symbol: "%" }, { text: "एम परसेंट", symbol: "&" }, { text: "तारांकन", symbol: "*" },
        { text: "धन चिन्ह", symbol: "+" }, { text: "बराबर चिन्ह", symbol: "=" }, { text: "बायां कोष्ठक", symbol: "(" },
        { text: "दायां कोष्ठक", symbol: ")" }, { text: "बायां वर्ग कोष्ठक", symbol: "[" }, { text: "दायां वर्ग कोष्ठक", symbol: "]" },
        { text: "बायां सर्पाकार कोष्ठक", symbol: "{" }, { text: "दायां सर्पाकार कोष्ठक", symbol: "}" },
        { text: "अगली पंक्ति", action: "newline" }, { text: "नया अनुच्छेद", action: "newparagraph" },
        { text: "सुनना बंद करो", action: "stopMic" }, { text: "संपादक साफ करें", action: "clearEditor" }
    ],
    'mr': [
        { text: "स्वल्पविराम", symbol: "," }, { text: "पूर्णविराम", symbol: "।" }, { text: "अर्धविराम", symbol: ";" },
        { text: "अपूणविराम", symbol: ":" }, { text: "प्रश्न चिन्ह", symbol: "?" }, { text: "संयोग चिन्ह", symbol: "-" },
        { text: "अपसरण चिन्ह", symbol: "—" }, { text: "ऑब्लिक", symbol: "/" }, { text: "उद्गार चिन्ह", symbol: "!" },
        { text: "अॅट द रेट", symbol: "@" }, { text: "हॅशटॅग", symbol: "#" }, { text: "डॉलर", symbol: "$" },
        { text: "टक्केवारी", symbol: "%" }, { text: "अँपर्सँड", symbol: "&" }, { text: "अॅस्टरिस्क", symbol: "*" },
        { text: "धन चिन्ह", symbol: "+" }, { text: "बरोबर चिन्ह", symbol: "=" }, { text: "डावा कंस", symbol: "(" },
        { text: "उजवा कंस", symbol: ")" }, { text: "डावा चौकटी कंस", symbol: "[" }, { text: "उजवा चौकटी कंस", symbol: "]" },
        { text: "डावा महिरपी कंस", symbol: "{" }, { text: "उजवा महिरपी कंस", symbol: "}" },
        { text: "पुढील ओळ", action: "newline" }, { text: "नवीन परिच्छेद", action: "newparagraph" },
        { text: "ऐकणे थांबवा", action: "stopMic" }, { text: "संपादक साफ करा", action: "clearEditor" }
    ]
};

export const COMMANDS_FLAT = {};
Object.keys(COMMANDS).forEach(lang => {
    COMMANDS_FLAT[lang] = {};
    COMMANDS[lang].forEach(cmd => {
        COMMANDS_FLAT[lang][cmd.text.toLowerCase()] = cmd;
    });
});