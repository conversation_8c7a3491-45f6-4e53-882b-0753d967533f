import { useState, useCallback } from 'react';
import { unicode_to_kruti } from '../../../font-conv/unicode_to_kruti';
import { unicode_to_preeti } from '../../../font-conv/unicode_to_preeti';
import { unicode_to_shree } from '../../../font-conv/unicode_to_shree';
import { unicode_to_shivaji } from '../../../font-conv/unicode_to_shivaji';
import { convert_unicode_to_aps } from '../../../font-conv/uniaps';
import { sleep } from '../utils/editorUtils';

const useFontConversion = (quillRef, showError, clearAlerts) => {
    const [isConvertingFont, setIsConvertingFont] = useState(false);
    const [conversionOutput, setConversionOutput] = useState('');
    const [fontConversionModalVisible, setFontConversionModalVisible] = useState(false);
    const [selectedFont, setSelectedFont] = useState('');

    const convertToKrutidev = (text) => unicode_to_kruti(text);
    const convertToPreeti = (text) => unicode_to_preeti(text);
    const convertToShree = (text) => unicode_to_shree(text);
    const convertToShivaji = (text) => unicode_to_shivaji(text);
    const convertToAPS = (text) => convert_unicode_to_aps(text);
    const convertToISM = (text) => `(ISM Conversion Not Available): ${text}`;

    const handleFontConversion = useCallback(async (type) => {
        clearAlerts();
        const text = quillRef.current?.getEditor().getText()?.trim();
        if (!text || text.length <= 1) { showError("Please enter text in the editor first."); return; }
        setIsConvertingFont(true); setConversionOutput(''); setSelectedFont(type);
        await sleep(50);
        try {
            let output = '';
            switch (type) {
                case 'kruti': output = convertToKrutidev(text); break;
                case 'preeti': output = convertToPreeti(text); break;
                case 'shree': output = convertToShree(text); break;
                case 'shivaji': output = convertToShivaji(text); break;
                case 'aps': output = convertToAPS(text); break;
                case 'ism': output = convertToISM(text); break;
                default: throw new Error(`Unsupported font type: ${type}`);
            }
            setConversionOutput(output); setFontConversionModalVisible(true);
        } catch (e) { console.error("Font conversion error:", e); showError(`Font conversion to ${type} failed: ${e.message}`); }
        finally { setIsConvertingFont(false); }
    }, [quillRef, showError, clearAlerts]);

    return {
        isConvertingFont,
        conversionOutput,
        fontConversionModalVisible,
        selectedFont,
        setFontConversionModalVisible,
        handleFontConversion,
    };
};

export default useFontConversion;