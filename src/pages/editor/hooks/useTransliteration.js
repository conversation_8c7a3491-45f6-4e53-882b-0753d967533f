import { useState, useCallback } from 'react';
import axios from 'axios';

const useTransliteration = (quillRef, transliterationLang, isTransliterationEnabled, showError) => {
    const [isTransliterating, setIsTranslating] = useState(false);

    const handleTransliteration = useCallback(async (text) => {
        if (!isTransliterationEnabled || !text) return;

        setIsTranslating(true);
        try {
            const response = await axios.get(`https://www.google.com/inputtools/request?text=${text}&ime=transliteration_en_${transliterationLang}&num=1&cp=0&cs=0&ie=utf-8&oe=utf-8&app=jsapi`);
            const transliteratedText = response.data[1][0][1][0];

            if (transliteratedText) {
                const quill = quillRef.current.getEditor();
                const range = quill.getSelection();
                if (range) {
                    const textToDelete = range.index - (text.length + 1);
                    quill.deleteText(textToDelete, text.length);
                    quill.insertText(textToDelete, transliteratedText);
                }
            }
        } catch (error) {
            showError('Transliteration failed.');
            console.error('Transliteration error:', error);
        } finally {
            setIsTranslating(false);
        }
    }, [isTransliterationEnabled, transliterationLang, quillRef, showError]);

    return {
        isTransliterating,
        handleTransliteration,
    };
};

export default useTransliteration;