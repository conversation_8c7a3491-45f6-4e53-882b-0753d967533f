import { useState, useEffect } from 'react';
import { parseISO, isAfter, isValid, formatDistanceStrict } from 'date-fns';

const useUserLicense = (user) => {
    const [actualExpiryDate, setActualExpiryDate] = useState(null);
    const [expiryDisplayText, setExpiryDisplayText] = useState('Loading...');

    useEffect(() => {
        if (user?.expiry_date) setActualExpiryDate(user.expiry_date);
        else { setActualExpiryDate(null); setExpiryDisplayText('N/A'); }
    }, [user]);

    useEffect(() => {
        if (!actualExpiryDate) { setExpiryDisplayText('N/A'); return; }
        try {
            const today = new Date(); today.setHours(0, 0, 0, 0);
            const expiryDateObj = parseISO(actualExpiryDate); expiryDateObj.setHours(0, 0, 0, 0);
            if (!isValid(expiryDateObj)) { setExpiryDisplayText('Invalid Date'); return; }
            if (isAfter(today, expiryDateObj)) { setExpiryDisplayText('Expired'); }
            else { setExpiryDisplayText(`${formatDistanceStrict(expiryDateObj, today)} Remaining`); }
        } catch (error) { console.error("Error calculating expiry text:", error); setExpiryDisplayText('Error'); }
    }, [actualExpiryDate]);

    return { expiryDisplayText, actualExpiryDate };
};

export default useUserLicense;