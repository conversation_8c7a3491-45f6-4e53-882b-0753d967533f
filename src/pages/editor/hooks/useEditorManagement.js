import { useCallback, useState } from 'react';
import { saveContent } from '../../../services/api';

const useEditorManagement = (quillRef, showError, showSuccess, clearAlerts, handleClear) => {
    const [isSavingContent, setIsSavingContent] = useState(false);
    const [saveModalVisible, setSaveModalVisible] = useState(false);
    const [contentTitle, setContentTitle] = useState('');
    const [contentDescription, setContentDescription] = useState('');

    const handleCopy = useCallback(() => {
        clearAlerts();
        const text = quillRef.current?.getEditor().getText()?.trim();
        if (text && text.length > 1) {
            navigator.clipboard.writeText(text).then(() => showSuccess("Editor content copied!")).catch(() => showError("Copy failed."));
        } else { showError("Nothing to copy."); }
    }, [showError, showSuccess, clearAlerts, quillRef]);

    const handlePrint = useCallback(() => {
        clearAlerts();
        const quillInstance = quillRef.current?.getEditor();
        const content = quillInstance?.root.innerHTML || '';
        const plainText = quillInstance?.getText()?.trim();
        if (!plainText || plainText.length <= 1) { showError("Nothing to print."); return; }
        const printWindow = window.open('', '_blank');
        printWindow?.document.write(`
            <html>
                <head>
                    <title>Print</title>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css">
                    <style>
                        @media print {
                            body, html {
                                height: auto;
                                overflow: visible;
                            }
                            .ql-editor {
                                height: auto !important;
                                overflow: visible !important;
                                page-break-inside: avoid;
                            }
                            .ql-editor p {
                                page-break-inside: avoid;
                            }
                        }
                        body {
                            margin: 20px;
                        }
                        .ql-editor {
                            padding: 0;
                        }
                    </style>
                </head>
                <body>
                    <div class="ql-editor">${content}</div>
                </body>
            </html>
        `);
        printWindow?.document.close();
        setTimeout(() => { printWindow?.print(); printWindow?.close(); }, 300);
    }, [showError, clearAlerts, quillRef]);

    const handleSave = useCallback(() => {
        clearAlerts();
        const plainText = quillRef.current?.getEditor().getText()?.trim();
        if (!plainText || plainText.length <= 1) {
            showError("Please enter text in the editor to save.");
            return;
        }
        setContentTitle('');
        setContentDescription('');
        setSaveModalVisible(true);
    }, [clearAlerts, showError, quillRef]);

    const handleConfirmSave = useCallback(async () => {
        clearAlerts();
        const html_content = quillRef.current?.getEditor().root.innerHTML;
        const plainText = quillRef.current?.getEditor().getText()?.trim();

        if (!html_content || plainText.length <= 1) {
            showError("No content to save.");
            return;
        }

        if (!contentTitle.trim()) {
            showError("Please enter a title for your content.");
            return;
        }

        setIsSavingContent(true);
        try {
            await saveContent({
                title: contentTitle,
                description: contentDescription,
                html_content: html_content
            });
            showSuccess("Content saved successfully!");
            setSaveModalVisible(false);
        } catch (e) {
            console.error("Save content error:", e);
            showError(e.response?.data?.message || e.message || "Failed to save content. Please try again.");
        } finally {
            setIsSavingContent(false);
        }
    }, [contentTitle, contentDescription, showError, showSuccess, clearAlerts, quillRef]);

    return {
        isSavingContent,
        saveModalVisible,
        setSaveModalVisible,
        contentTitle,
        setContentTitle,
        contentDescription,
        setContentDescription,
        handleCopy,
        handlePrint,
        handleSave,
        handleConfirmSave,
    };
};

export default useEditorManagement;