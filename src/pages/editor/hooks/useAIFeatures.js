import { useState, useCallback } from 'react';
import { translateText, fixSpellingAndGrammar, expandContent, generateDraft } from '../../../services/gemini';
import { sleep, wrapWordsInSpans } from '../utils/editorUtils';
import { translationLanguages } from '../constants/speechConstants';

const useAIFeatures = (quillRef, showError, showSuccess, clearAlerts) => {
    const [isTranslating, setIsTranslating] = useState(false);
    const [translatedText, setTranslatedText] = useState('');
    const [translationModalVisible, setTranslationModalVisible] = useState(false);

    const [isFixingGrammar, setIsFixingGrammar] = useState(false);
    const [isExpandingContent, setIsExpandingContent] = useState(false);
    const [isGeneratingDraft, setIsGeneratingDraft] = useState(false);
    const [draftModalVisible, setDraftModalVisible] = useState(false);
    const [draftIdea, setDraftIdea] = useState('');

    const [isLoadingModalVisible, setIsLoadingModalVisible] = useState(false);
    const [loadingMessage, setLoadingMessage] = useState('');

    const handleFixGrammar = useCallback(async () => {
        clearAlerts();
        const quill = quillRef.current?.getEditor();
        const originalHtml = quill.root.innerHTML;
        if (!originalHtml || originalHtml.length <= 1) {
            showError("Please enter text in the editor first.");
            return;
        }

        setIsFixingGrammar(true);
        setLoadingMessage('Fixing grammar...');
        setIsLoadingModalVisible(true);

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalHtml;
        const wordCounter = { count: 0 };
        const newContent = wrapWordsInSpans(tempDiv, wordCounter);
        quill.root.innerHTML = newContent.innerHTML;

        for (let i = 0; i < wordCounter.count; i++) {
            const wordEl = quill.root.querySelector(`#word-${i}`);
            if (wordEl) {
                wordEl.classList.add('wordHighlight'); // Assuming 'wordHighlight' is a global class or passed via props
                await sleep(50);
                wordEl.classList.remove('wordHighlight');
            }
        }

        quill.root.innerHTML = originalHtml;
        
        try {
            const correctedHtml = await fixSpellingAndGrammar(originalHtml);
            quill.clipboard.dangerouslyPasteHTML(correctedHtml); // Directly update Quill content
            showSuccess("Spelling and grammar corrected!");
        } catch (e) {
            console.error("Grammar correction error:", e);
            quill.clipboard.dangerouslyPasteHTML(originalHtml); // Restore original on error
            showError(e.message || "Failed to correct spelling and grammar.");
        } finally {
            setIsFixingGrammar(false);
            setIsLoadingModalVisible(false);
        }
    }, [quillRef, showError, showSuccess, clearAlerts]);

    const handleExpandContent = useCallback(async () => {
        clearAlerts();
        const text = quillRef.current?.getEditor().getText()?.trim();
        if (!text || text.length <= 1) { showError("Please enter text in the editor first."); return; }

        setIsExpandingContent(true);
        setLoadingMessage('Expanding content...');
        setIsLoadingModalVisible(true);
        try {
            const expandedContentHtml = await expandContent(text);
            quillRef.current?.getEditor().clipboard.dangerouslyPasteHTML(expandedContentHtml);
            showSuccess("Content expanded successfully!");
        } catch (e) {
            console.error("Content expansion error:", e);
            showError(e.message || "Failed to expand content. Please try again.");
        } finally {
            setIsExpandingContent(false);
            setIsLoadingModalVisible(false);
        }
    }, [quillRef, showError, showSuccess, clearAlerts]);

    const handleGenerateDraft = useCallback(() => {
        clearAlerts();
        setDraftIdea('');
        setDraftModalVisible(true);
    }, [clearAlerts]);

    const handleConfirmGenerateDraft = useCallback(async (promptOrCategory, formData = {}, additionalDetails = '', template = null) => {
        clearAlerts();
        
        let finalPrompt = '';
        
        if (typeof promptOrCategory === 'string') {
            // Direct prompt (for backward compatibility)
            finalPrompt = `You are a professional legal document drafting assistant. Please create a well-structured, legally sound document based on the following request: ${promptOrCategory}`;
        } else {
            // Category-based generation with template
            if (template) {
                // Use template-based generation with intelligent placeholder replacement
                let filledTemplate = template.template;
                
                // Create a comprehensive mapping system for placeholder replacement
                const createPlaceholderMap = (fields, formData) => {
                    const map = new Map();
                    
                    fields.forEach((field, index) => {
                        const value = formData[index]?.trim() || `[${field.label}]`;
                        const label = field.label.toLowerCase();
                        
                        // Create multiple possible placeholder patterns for each field
                        const patterns = [];
                        
                        // Direct label match
                        patterns.push(`[${field.label}]`);
                        
                        // Common legal document placeholders
                        if (label.includes('city') || label.includes('location') || label.includes('court')) {
                            patterns.push('[CITY]', '[City]', '[Court Location]', '[Tribunal Location]');
                        }
                        if (label.includes('husband') && label.includes('name')) {
                            patterns.push('[Full Name of Husband]', '[Husband Name]');
                        }
                        if (label.includes('wife') && label.includes('name')) {
                            patterns.push('[Full Name of Wife]', '[Wife Name]');
                        }
                        if (label.includes('father') && label.includes('name')) {
                            patterns.push('[Father\'s Name]', '[Father Name]');
                        }
                        if (label.includes('address')) {
                            patterns.push('[Full Address]', '[Address]', '[Complete Address]');
                        }
                        if (label.includes('date') && label.includes('marriage')) {
                            patterns.push('[Date of Marriage]', '[Marriage Date]');
                        }
                        if (label.includes('place') && label.includes('marriage')) {
                            patterns.push('[Place of Marriage]', '[Marriage Place]');
                        }
                        if (label.includes('separation')) {
                            patterns.push('[Date of Separation]', '[Separation Date]');
                        }
                        if (label.includes('claimant') && label.includes('name')) {
                            patterns.push('[Claimant Name]', '[Claimant]');
                        }
                        if (label.includes('accused') && label.includes('name')) {
                            patterns.push('[Accused Name]', '[Accused]');
                        }
                        if (label.includes('complainant') && label.includes('name')) {
                            patterns.push('[Complainant Name]', '[Complainant]');
                        }
                        if (label.includes('date') && label.includes('accident')) {
                            patterns.push('[Date of Accident]', '[Accident Date]');
                        }
                        if (label.includes('time') && label.includes('accident')) {
                            patterns.push('[Time]', '[Time of Accident]');
                        }
                        if (label.includes('registration') && label.includes('number')) {
                            patterns.push('[Registration Number]', '[Vehicle Number]');
                        }
                        if (label.includes('hospital')) {
                            patterns.push('[Hospital Name]', '[Hospital]');
                        }
                        if (label.includes('monthly') && label.includes('income')) {
                            patterns.push('[Monthly Income]', '[Income]');
                        }
                        if (label.includes('amount') || label.includes('compensation')) {
                            patterns.push('[Amount]', '[Compensation Amount]', '[Cheque Amount]', '[Loan Amount]');
                        }
                        if (label.includes('cheque') && label.includes('number')) {
                            patterns.push('[Cheque Number]');
                        }
                        if (label.includes('bank') && label.includes('name')) {
                            patterns.push('[Bank Name]', '[Bank]');
                        }
                        if (label.includes('branch')) {
                            patterns.push('[Branch Name]', '[Branch]');
                        }
                        if (label.includes('occupation')) {
                            patterns.push('[Occupation]', '[Occupation/Business]');
                        }
                        if (label.includes('age')) {
                            patterns.push('[Age]', '[Victim Age]');
                        }
                        if (label.includes('driver')) {
                            patterns.push('[Driver Name]', '[Driver]');
                        }
                        if (label.includes('owner')) {
                            patterns.push('[Owner Name]', '[Vehicle Owner Name]');
                        }
                        if (label.includes('insurance')) {
                            patterns.push('[Insurance Company Name]', '[Insurance Company]');
                        }
                        
                        // Store all patterns for this value
                        patterns.forEach(pattern => {
                            map.set(pattern, value);
                        });
                    });
                    
                    return map;
                };
                
                // Replace placeholders with form data
                if (template.fields && formData) {
                    const placeholderMap = createPlaceholderMap(template.fields, formData);
                    
                    // Replace all placeholders in the template
                    placeholderMap.forEach((value, placeholder) => {
                        const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                        filledTemplate = filledTemplate.replace(regex, value);
                    });
                }
                
                // Create a comprehensive professional prompt
                finalPrompt = `You are a senior legal document drafting expert with extensive experience in Indian law. Please create a professionally formatted legal document based on the following template and requirements:

DOCUMENT TYPE: ${template.title}
DESCRIPTION: ${template.description}

TEMPLATE TO FORMAT:
${filledTemplate}

INSTRUCTIONS:
1. Ensure proper legal formatting with appropriate spacing, indentation, and structure
2. Use professional legal language and terminology
3. Maintain consistency in numbering and paragraph structure
4. Ensure all dates are properly formatted
5. Check that all placeholders are appropriately filled or marked for completion
6. Add proper HTML formatting for professional presentation
7. Ensure the document follows standard Indian legal document conventions
8. Include proper verification clauses and signature blocks
9. Use appropriate legal citations and references where applicable
10. Maintain professional tone throughout the document`;
                
                if (additionalDetails.trim()) {
                    finalPrompt += `\n\nADDITIONAL SPECIFIC REQUIREMENTS:
${additionalDetails}

Please incorporate these additional requirements while maintaining the professional legal structure and format.`;
                }
            } else {
                // Enhanced fallback for categories without templates
                finalPrompt = `You are a professional legal document drafting expert specializing in Indian law. Please create a comprehensive, legally sound ${promptOrCategory.title} document with the following specifications:

1. Use proper legal formatting and structure appropriate for Indian courts
2. Include all necessary legal clauses and provisions
3. Follow standard legal document conventions
4. Use professional legal terminology
5. Include proper verification and signature blocks
6. Ensure the document is court-ready and legally compliant`;
                
                if (additionalDetails.trim()) {
                    finalPrompt += `\n\nSPECIFIC REQUIREMENTS:
${additionalDetails}

Please incorporate these specific details while maintaining professional legal standards and formatting.`;
                }
            }
        }

        if (!finalPrompt.trim()) {
            showError("Please provide details to generate a draft.");
            return;
        }

        setDraftModalVisible(false);
        setIsGeneratingDraft(true);
        setLoadingMessage('Generating professional legal draft...');
        setIsLoadingModalVisible(true);
        try {
            const generatedDraftHtml = await generateDraft(finalPrompt);
            quillRef.current?.getEditor().clipboard.dangerouslyPasteHTML(generatedDraftHtml);
            showSuccess("Professional legal draft generated successfully!");
        } catch (e) {
            console.error("Draft generation error:", e);
            showError(e.message || "Failed to generate draft. Please try again.");
        } finally {
            setIsGeneratingDraft(false);
            setIsLoadingModalVisible(false);
        }
    }, [quillRef, showError, showSuccess, clearAlerts]);

    const handleTranslate = useCallback(async (translateLang) => {
        clearAlerts();
        const text = quillRef.current?.getEditor().getText()?.trim();
        if (!text || text.length <= 1) { showError("Please enter text in the editor first."); return; }
        
        const targetLanguageObject = translationLanguages.find(lang => lang.value === translateLang);
        const targetLanguageName = targetLanguageObject ? targetLanguageObject.label : translateLang;

        setIsTranslating(true);
        setLoadingMessage('Translating text...');
        setIsLoadingModalVisible(true);
        setTranslatedText('');

        try {
            const translatedResult = await translateText(text, targetLanguageName, translateLang);
            setTranslatedText(translatedResult);
            showSuccess("Text translated successfully!");
            setTranslationModalVisible(true); // Open modal only on success
        } catch (e) {
            console.error("Translation error:", e);
            showError(e.message || "Translation failed. Please try again.");
        } finally {
            setIsTranslating(false);
            setIsLoadingModalVisible(false);
        }
    }, [quillRef, showError, showSuccess, clearAlerts]);

    return {
        isTranslating,
        translatedText,
        translationModalVisible,
        setTranslationModalVisible,
        isFixingGrammar,
        isExpandingContent,
        isGeneratingDraft,
        draftModalVisible,
        setDraftModalVisible,
        draftIdea,
        setDraftIdea,
        isLoadingModalVisible,
        loadingMessage,
        setIsLoadingModalVisible,
        handleFixGrammar,
        handleExpandContent,
        handleGenerateDraft,
        handleConfirmGenerateDraft,
        handleTranslate,
    };
};

export default useAIFeatures;