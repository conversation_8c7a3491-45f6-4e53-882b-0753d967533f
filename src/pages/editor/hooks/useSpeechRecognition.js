import { useState, useEffect, useRef, useCallback } from 'react';
import { COMMANDS_FLAT } from '../constants/speechConstants';
import { insertRawTextIntoQuill } from '../utils/editorUtils';

const useSpeechRecognition = (quillRef, dictationLang, showError, handleClear) => {
    const [listening, setListening] = useState(false);
    const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);
    const [isMicrophoneAvailable, setIsMicrophoneAvailable] = useState(true);
    const isListeningRef = useRef(false); // Tracks if the recognition service is actively listening
    const listeningIntentRef = useRef(listening); // Tracks the user's *intent* to listen (from React state)
    const recognitionRef = useRef(null);
    const lastProcessedResultIndexRef = useRef(0);
    const interimRangeRef = useRef(null); // For tracking interim text in the editor
    const isManualStopRef = useRef(false);

    // Effect to keep listeningIntentRef in sync with listening state
    useEffect(() => {
        listeningIntentRef.current = listening;
    }, [listening]);

    const processSegmentForInsertion = useCallback((segment) => {
        if (!segment) return; // segment from STT
        
        // Use the raw segment for command checking, as STT might include spaces
        const rawCommandText = segment.trim().toLowerCase(); // Trim for command matching only

        const baseLangCode = dictationLang.split('-')[0].toLowerCase();
        let commandsForLang = COMMANDS_FLAT[baseLangCode];
        if (!commandsForLang) {
            if (baseLangCode === 'en' && COMMANDS_FLAT['en']) {
                commandsForLang = COMMANDS_FLAT['en'];
            } else {
                commandsForLang = {};
            }
        }

        let textToInsert = segment; // Start with the original segment from STT
        let commandHandled = false;
        const commandMatch = commandsForLang[rawCommandText];

        if (commandMatch) {
            commandHandled = true;
            if (commandMatch.symbol !== undefined) {
                textToInsert = commandMatch.symbol; // Replace segment with symbol
            } else if (commandMatch.action) {
                textToInsert = ''; // No text insertion for action commands
                switch (commandMatch.action) {
                    case 'stopMic':
                        if (isListeningRef.current && recognitionRef.current) {
                            isManualStopRef.current = true;
                            recognitionRef.current.stop();
                        }
                        break;
                    case 'newline': insertRawTextIntoQuill(quillRef, '\n'); break;
                    case 'newparagraph': insertRawTextIntoQuill(quillRef, '\n\n'); break;
                    case 'clearEditor': handleClear(); break;
                    default:
                        commandHandled = false; // Unrecognized action
                        textToInsert = segment; // Revert to original segment
                        break;
                }
            } else { // Command found but no symbol or action
                commandHandled = false;
                textToInsert = segment; // Revert to original segment
            }
        }

        if (commandHandled && commandMatch && commandMatch.action && 
            (commandMatch.action === 'stopMic' || commandMatch.action === 'clearEditor' || commandMatch.action === 'newline' || commandMatch.action === 'newparagraph')) {
            // Do nothing further for these actions, they've been handled.
        } else if (textToInsert.length > 0) { // Proceed if there's text to insert (original segment or a symbol)
            
            const trimmedTextToInsert = textToInsert.trim();

            if (trimmedTextToInsert.length > 0) { // Ensure there's actual content after trimming
                let finalText = trimmedTextToInsert;

                const quill = quillRef.current?.getEditor();
                const selection = quill?.getSelection();
                let precedingChar = '';
                if (quill && selection && selection.index > 0) {
                    precedingChar = quill.getText(selection.index - 1, 1);
                }

                const startsWithSpaceConsumingPunctuation = /^[.,;:?!)।\]}>]/.test(finalText); // Punctuation that "consumes" a preceding space
                const endsWithSpaceAddingPunctuation = /[([{<]$/.test(precedingChar); // Punctuation that "adds" a space after itself

                if (precedingChar && precedingChar !== ' ' && precedingChar !== '\n' && !startsWithSpaceConsumingPunctuation && !endsWithSpaceAddingPunctuation) {
                    finalText = ' ' + finalText;
                }
                
                const endsWithPunctuationOrNewline = /[.,;:?!-/—।\n]$/.test(finalText.trim()); // Includes Devanagari Danda
                const endsWithSpace = /\s$/.test(finalText);

                if (!endsWithPunctuationOrNewline && !endsWithSpace) {
                    finalText += ' ';
                }
                
                insertRawTextIntoQuill(quillRef, finalText);
            }
        }
    }, [dictationLang, quillRef, handleClear]);

    useEffect(() => {
        const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (SpeechRecognitionAPI) {
            setBrowserSupportsSpeechRecognition(true);
            recognitionRef.current = new SpeechRecognitionAPI();
            recognitionRef.current.continuous = true;
            recognitionRef.current.interimResults = true;
            recognitionRef.current.lang = dictationLang;

            recognitionRef.current.onresult = (event) => {
                if (!quillRef.current) return;
                const quill = quillRef.current.getEditor();

                let fullInterimForThisEvent = '';
                let finalizedTextForThisEvent = '';

                for (let i = lastProcessedResultIndexRef.current; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalizedTextForThisEvent += transcript;
                    } else {
                        fullInterimForThisEvent += transcript;
                    }
                }

                const selection = quill.getSelection(true);
                const baseIndex = interimRangeRef.current
                    ? interimRangeRef.current.index
                    : (selection ? selection.index : quill.getLength() - 1);

                if (interimRangeRef.current) {
                    quill.deleteText(interimRangeRef.current.index, interimRangeRef.current.length, 'silent');
                }

                if (finalizedTextForThisEvent.trim()) {
                    quill.setSelection(baseIndex, 0, 'silent');
                    processSegmentForInsertion(finalizedTextForThisEvent);

                    interimRangeRef.current = null;
                    lastProcessedResultIndexRef.current = event.results.length;
                }
                else if (fullInterimForThisEvent.trim()) {
                    quill.insertText(baseIndex, fullInterimForThisEvent, { 'color': 'gray' }, 'user');
                    
                    interimRangeRef.current = { index: baseIndex, length: fullInterimForThisEvent.length };
                    
                    quill.setSelection(baseIndex + fullInterimForThisEvent.length, 0, 'user');
                }
            };

            recognitionRef.current.onstart = () => {
                isListeningRef.current = true;
                lastProcessedResultIndexRef.current = 0;
                interimRangeRef.current = null;
            };

            recognitionRef.current.onerror = (event) => {
                console.error("Speech recognition error:", event.error, event);
                isListeningRef.current = false;
                interimRangeRef.current = null;

                let shouldSetListeningFalse = true;
                if (event.error === 'not-allowed' || event.error === 'permission-denied') {
                    setIsMicrophoneAvailable(false);
                    showError("Microphone access denied. Please allow access in browser settings.");
                } else if (event.error === 'no-speech') {
                    console.log("No speech detected - recognition might restart or end.");
                    shouldSetListeningFalse = false;
                } else if (event.error === 'audio-capture') {
                    showError("Audio capture failed. Check your microphone.");
                } else {
                    showError(`Speech recognition error: ${event.error}`);
                }

                if (shouldSetListeningFalse) {
                    setListening(false);
                }
                
                lastProcessedResultIndexRef.current = 0;
            };

            recognitionRef.current.onend = () => {
                const wasThisSessionManuallyStopped = isManualStopRef.current;
                
                isListeningRef.current = false;
                interimRangeRef.current = null;

                if (wasThisSessionManuallyStopped) {
                    isManualStopRef.current = false;
                    if (listeningIntentRef.current) {
                        setListening(false);
                    }
                    lastProcessedResultIndexRef.current = 0;
                } else {
                    if (listeningIntentRef.current && recognitionRef.current) {
                        console.log("Recognition ended non-manually; user intent is still to listen. Attempting restart...");
                        
                        setTimeout(() => {
                            if (listeningIntentRef.current && recognitionRef.current) {
                                try {
                                    isManualStopRef.current = false;
                                    lastProcessedResultIndexRef.current = 0;
                                    recognitionRef.current.start();
                                } catch (e) {
                                    console.error("Error restarting recognition:", e);
                                    showError("Error restarting speech recognition. Please try again.");
                                    setListening(false);
                                    isListeningRef.current = false;
                                }
                            } else {
                                if (listeningIntentRef.current) {
                                     setListening(false);
                                }
                                isListeningRef.current = false;
                            }
                        }, 100);
                    } else {
                        if (listeningIntentRef.current) {
                            setListening(false);
                        }
                        isListeningRef.current = false;
                        lastProcessedResultIndexRef.current = 0;
                    }
                }
            };

            if (navigator.permissions && navigator.permissions.query) {
                navigator.permissions.query({ name: 'microphone' }).then((permissionStatus) => {
                    const micState = permissionStatus.state;
                    setIsMicrophoneAvailable(micState === 'granted' || micState === 'prompt');
                    permissionStatus.onchange = () => {
                        setIsMicrophoneAvailable(permissionStatus.state === 'granted' || permissionStatus.state === 'prompt');
                    };
                }).catch(err => {
                    console.error("Error querying microphone permission:", err);
                    setIsMicrophoneAvailable(false);
                });
            } else {
                setIsMicrophoneAvailable(true);
            }
        } else {
            setBrowserSupportsSpeechRecognition(false);
            showError("Speech recognition not supported in this browser. Use Chrome or Edge.");
        }

        return () => {
            if (recognitionRef.current) {
                isManualStopRef.current = true;
                try {
                    recognitionRef.current.onresult = null;
                    recognitionRef.current.onerror = null;
                    recognitionRef.current.onstart = null;
                    recognitionRef.current.onend = null;
                    recognitionRef.current.stop();
                } catch (e) { console.error("Error stopping recognition on cleanup:", e); }
                recognitionRef.current = null;
                isListeningRef.current = false;
                setListening(false);
            }
        };
    }, [dictationLang, showError, processSegmentForInsertion]);

    const startRecognition = useCallback(() => {
        isManualStopRef.current = false;
        lastProcessedResultIndexRef.current = 0;
        interimRangeRef.current = null;
        recognitionRef.current.lang = dictationLang;
        recognitionRef.current.start();
    }, [dictationLang]);

    const stopRecognition = useCallback(() => {
        isManualStopRef.current = true;
        if (recognitionRef.current) {
            try {
                recognitionRef.current.stop();
            } catch (e) {
                console.error("Error calling recognition.stop():", e);
            }
        }
    }, []);

    return {
        listening,
        setListening,
        browserSupportsSpeechRecognition,
        isMicrophoneAvailable,
        recognitionRef,
        isListeningRef,
        startRecognition,
        stopRecognition,
    };
};

export default useSpeechRecognition;