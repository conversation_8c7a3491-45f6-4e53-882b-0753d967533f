/* main.module.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --secondary-color: #10b981;
    --background-light: #f9fafb;
    --background-sidebar: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #ffffff;
    --white: #ffffff;
    --border-color: #e5e7eb;
    --error-color: #ef4444;
    --error-light: #fee2e2;
    --success-color: #10b981;
    --success-light: #d1fae5;
    --font-sans: 'Poppins', sans-serif;
}

.pageWrapper {
    display: flex;
    flex-direction: column;
    height: 100vh;
    font-family: var(--font-sans);
    background-color: var(--background-light);
}

.mainContent {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    /* Changed from hidden to auto to allow scrolling */
    overflow-x: hidden;
    /* Prevent horizontal scrolling */
}

.actionsToolbar {
    flex-shrink: 0;
    background-color: var(--background-sidebar);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
}

.actionsToolbarContent {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

.editorWrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    overflow: hidden;
    position: relative;
    /* Added for positioning FAB */
}

.quillEditor {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.quillEditor :global(.ql-toolbar) {
    border-bottom: 1px solid var(--border-color);
    border-radius: 0.5rem 0.5rem 0 0;
    background-color: var(--background-light);
}

.quillEditor :global(.ql-container) {
    flex-grow: 1;
    border-radius: 0 0 0.5rem 0.5rem;
    overflow-y: auto;
}

.quillEditor :global(.ql-editor) {
    font-size: 1.1rem;
    /* Slightly bigger font size for the editor content */
}

.sidebar {
    flex-shrink: 0;
    background-color: var(--background-sidebar);
    border-top: 1px solid var(--border-color);
    padding: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: stretch;
    align-items: flex-start;
    width: 100%;
}

.sidebarCard {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem;
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.sidebarCardNarrow {
    flex: 1;
    min-width: 0;
}

.speechInputCard,
.translationCard {
    flex-grow: 1;
}

.actionsCard {
    flex-grow: 2;
    min-height: 200px;
    max-height: 250px;
}

.sidebarTitle {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.controlGroup {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.controlGroup label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
}

.formControlSm {
    width: 100%;
    padding: 0.25rem 0.6rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    font-size: 0.8rem;
    background-color: #fff;
}

.formControlNarrow {
    width: 70%;
}

.actionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.3rem 0.75rem;
    border: 2px solid var(--primary-color);
    border-radius: 0.375rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.actionButtonPrimary {
    background-color: var(--primary-color);
    color: var(--white);
}

.actionButtonSecondary {
    background-color: var(--white);
    color: var(--text-secondary);
    border-color: var(--primary-color);
}

.actionButtonDanger {
    background-color: var(--error-light);
    color: var(--error-color);
    border-color: var(--primary-color);
}

.actionButtonHighlight {
    background-color: var(--secondary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.actionButtonHighlight:hover:not(:disabled) {
    background-color: #0f9d71;
    border-color: var(--primary-color);
}

.actionButtonsGroup {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.4rem;
    height: fit-content;
    overflow-y: auto;
    max-height: 180px;
}

.fabButton {
    position: absolute;
    bottom: 20px;
    right: 20px;
    /* Changed from left to right */
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s ease;
    z-index: 1000;
    /* Ensure it's above other content */
}

.fabButton:hover {
    background-color: var(--primary-hover);
}

.fabButton:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    box-shadow: none;
}

.fontConversionButtons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
}

.translationOutput {
    background-color: var(--primary-light);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 1rem;
    /* Increased font size */
    margin-top: 0.5rem;
    /* Removed max-height and overflow-y to allow textarea to expand */
}

.topNavbar {
    flex-shrink: 0;
    background-color: var(--background-sidebar);
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbarBrand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.navbarInfo {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.logoutButton {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 0.375rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
}

.alertContainer {
    padding: 0 1.5rem;
}

.alert {
    padding: 0.75rem 1rem;
    margin-top: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alertDanger {
    color: var(--error-color);
    background-color: var(--error-light);
}

.alertSuccess {
    color: var(--success-color);
    background-color: var(--success-light);
}

.modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1040;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modalContentWrapper {
    background: var(--white);
    color: var(--text-primary);
    width: 800px;
    /* Set a fixed width to test if it expands */
    max-width: 90%;
    max-height: 90%;
    /* Keep a max-height */
    border-radius: 0.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modalContentWrapper.smallLoading {
    width: auto;
    max-width: 300px;
    /* Smaller max-width */
    padding: 20px;
    text-align: center;
    border-radius: 10px;
}

.modalHeader {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modalHeader.hidden {
    display: none;
    /* Hide header for small loading modal */
}

.modalHeader h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modalCloseBtn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.modalBody {
    padding: 1.5rem;
    overflow-y: auto;
    flex-grow: 1;
    /* Allow modal body to grow */
}

.modalBody.noPadding {
    padding: 0;
    /* No padding for small loading modal */
}

.modalTabs {
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.modalTabs button {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    margin-right: 1rem;
    transition: color 0.2s ease, border-color 0.2s ease;
}

.modalTabs button:hover {
    color: var(--primary-color);
}

.modalTabs button.active {
    /* Target active class */
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.modalTable {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

/* Styles for ContentHistoryPage */
.contentPageContainer {
    padding: 20px;
    background-color: var(--white);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.pageHeader {
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.8rem;
    color: var(--text-primary);
    font-weight: 600;
}

.emptyState {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-secondary);
    padding: 50px 20px;
}

.contentGrid {
    display: flex;
    /* Change to flex for horizontal layout */
    flex-direction: column;
    /* Stack cards vertically */
    gap: 15px;
    /* Adjust gap between horizontal cards */
    margin-bottom: 30px;
    flex-grow: 1;
}

.contentCard {
    background-color: var(--background-light);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 15px 20px;
    /* Adjust padding for horizontal layout */
    display: flex;
    align-items: center;
    /* Align items vertically in the center */
    justify-content: space-between;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    /* Ensure cards take full width */
}

.contentCard:hover {
    transform: translateY(-3px);
    /* Slight lift on hover */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contentInfo {
    /* New container for title, description, date */
    flex-grow: 1;
    margin-right: 15px;
    /* Space between info and actions */
}

.contentCard h3 {
    font-size: 1.1rem;
    /* Slightly smaller for horizontal layout */
    color: var(--primary-color);
    margin: 0;
    /* Remove default margins */
    margin-bottom: 5px;
    /* Space below title */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contentDescription {
    font-size: 0.85rem;
    /* Slightly smaller */
    color: var(--text-secondary);
    margin-bottom: 8px;
    /* Space below description */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* Limit to 2 lines for horizontal card */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contentDate {
    font-size: 0.75rem;
    /* Smaller date font */
    color: var(--text-secondary);
    margin: 0;
    /* Remove default margins */
}

.contentCardActions {
    display: flex;
    align-items: center;
    /* Align buttons vertically */
    gap: 8px;
    /* Smaller gap between buttons */
    flex-shrink: 0;
    /* Prevent actions from shrinking */
}

.actionButtonSmall {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
}

.modalTable th,
.modalTable td {
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    text-align: left;
}

.modalTable th {
    background-color: var(--background-light);
    font-weight: 600;
}

.modalFooter {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

.controlGroupTextarea {
    min-height: 400px;
    /* Further increased min-height for more vertical space */
    resize: vertical;
    font-family: monospace;
    background-color: var(--background-light);
    width: 100%;
    box-sizing: border-box;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
}

.wordHighlight {
    background-color: rgba(79, 70, 229, 0.3);
    transition: background-color 0.1s ease-in-out;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    border-radius: 0.375rem;
    overflow: hidden;
    /* Ensures borders are rounded correctly */
}

.page-item {
    margin: 0;
}

.page-link {
    display: block;
    padding: 0.5rem 0.75rem;
    color: var(--primary-color);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
}

.page-item:first-child .page-link {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-link:hover:not(:disabled) {
    background-color: var(--primary-light);
}

.page-item.disabled .page-link {
    color: #b0b0b0;
    cursor: not-allowed;
    background-color: var(--background-light);
}

/* Font Converter Specific Styles */
.converterWrapper {
    flex-grow: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
    overflow: hidden;
}

.converterSection {
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.converterLabel {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.converterLabel::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.converterTextarea {
    flex-grow: 1;
    min-height: 400px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    resize: vertical;
    background-color: var(--background-light);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.converterTextarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background-color: var(--white);
}

.converterTextarea:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
}

.converterTextarea::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

/* Radio Group Styles */
.radioGroup {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.radioLabel {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.radioLabel:hover {
    background-color: var(--primary-light);
}

.radioLabel input[type="radio"] {
    margin: 0;
    accent-color: var(--primary-color);
}

/* Success button variant */
.actionButtonSuccess {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.actionButtonSuccess:hover:not(:disabled) {
    background-color: #059669;
    border-color: var(--primary-color);
}

/* Block button utility */
.btnBlock {
    width: 100%;
}

/* Sidebar brand for converter */
.sidebarBrand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

/* Card header and body */
.cardHeader {
    margin-bottom: 0.75rem;
}

.cardBody {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Alert close button */
.alertClose {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: inherit;
    cursor: pointer;
    padding: 0;
    margin-left: 1rem;
    opacity: 0.7;
}

.alertClose:hover {
    opacity: 1;
}

/* Converter Page Layout */
.converterPageWrapper {
    flex-direction: column;
}

.converterPageWrapper .topNavbar {
    width: 100%;
    flex-shrink: 0;
}

.converterContentWrapper {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    overflow: hidden;
}

.converterSidebar {
    flex-direction: column;
    width: 320px;
    min-width: 320px;
    max-width: 320px;
    background-color: var(--background-sidebar);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    gap: 1rem;
    overflow-y: auto;
    display: flex;
}

.converterMainContent {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.pageWrapper:has(.converterWrapper) .sidebarCard {
    flex: none;
    max-width: none;
    min-width: auto;
    width: 100%;
}

/* Improved control group spacing for converter */
.pageWrapper:has(.converterWrapper) .controlGroup {
    gap: 0.6rem;
}

.pageWrapper:has(.converterWrapper) .controlGroup label {
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pageWrapper:has(.converterWrapper) .formControlSm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.pageWrapper:has(.converterWrapper) .actionButton {
    padding: 0.6rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.pageWrapper:has(.converterWrapper) .radioGroup {
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.pageWrapper:has(.converterWrapper) .radioLabel {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pageWrapper:has(.converterWrapper) .radioLabel:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.pageWrapper:has(.converterWrapper) .radioLabel input[type="radio"]:checked+* {
    color: var(--primary-color);
}

/* Responsive design for converter */
@media (max-width: 768px) {
    .converterContentWrapper {
        flex-direction: column;
    }

    .converterSidebar {
        width: 100%;
        min-width: auto;
        max-width: none;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        order: 1;
    }

    .converterMainContent {
        order: 2;
    }

    .converterWrapper {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .converterTextarea {
        min-height: 300px;
    }
}
/* 
Draft Categories Modal Styles */
.draftCategoriesModal {
    max-width: 800px;
    width: 90vw;
    max-height: 80vh;
}

.draftCategoriesGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    padding: 0.5rem 0;
}

.draftCategoryCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    background-color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 120px;
}

.draftCategoryCard:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.draftCategoryIcon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.draftCategoryCard:hover .draftCategoryIcon {
    color: var(--primary-hover);
    transform: scale(1.1);
}

.draftCategoryTitle {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.3;
    transition: color 0.3s ease;
}

.draftCategoryCard:hover .draftCategoryTitle {
    color: var(--primary-hover);
    font-weight: 600;
}

/* Category Details Modal Styles */
.categoryDetailsModal {
    max-width: 900px;
    width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.templateDescription {
    background-color: var(--primary-light);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.templateDescription p {
    margin: 0;
    color: var(--text-primary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.formSection {
    margin-bottom: 2rem;
}

.formSection h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.formGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.formGroup {
    display: flex;
    flex-direction: column;
}

.formGroup label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.formControl {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    font-family: var(--font-sans);
}

.formControl:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.formControl::placeholder {
    color: var(--text-secondary);
}

/* Responsive adjustments for draft categories */
@media (max-width: 768px) {
    .draftCategoriesModal {
        width: 95vw;
        max-height: 85vh;
    }
    
    .draftCategoriesGrid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.75rem;
    }
    
    .draftCategoryCard {
        padding: 1rem 0.75rem;
        min-height: 100px;
    }
    
    .draftCategoryIcon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .draftCategoryTitle {
        font-size: 0.8rem;
    }

    .categoryDetailsModal {
        width: 95vw;
        max-height: 95vh;
    }
    
    .formGrid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .formSection {
        margin-bottom: 1.5rem;
    }
}