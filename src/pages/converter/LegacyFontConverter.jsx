import React, { useState, useCallback, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';

// --- LEGACY -> UNICODE Conversion Functions ---
import krutiunicode from '../../font-conv/krutiuni';
import { preetiunicode } from '../../font-conv/preetiuni';
import { preeti_to_unicode } from '../../font-conv/unicode_to_preeti';
import { ShreeLipi_to_Unicode } from '../../font-conv/shreeuni';
import Shivaji_to_Unicode from '../../font-conv/shivajiuni';
import { shivaji_to_unicode } from '../../font-conv/unicode_to_shivaji';

// --- UNICODE -> LEGACY Conversion Functions ---
import { unicode_to_kruti } from '../../font-conv/unicode_to_kruti';
import { unicode_to_preeti } from '../../font-conv/unicode_to_preeti';
import { unicode_to_shree } from '../../font-conv/unicode_to_shree';
import { unicode_to_shivaji } from '../../font-conv/unicode_to_shivaji';

// Import styles and components
import styles from '../editor/main.module.css';

// Import hooks and utils needed for Navbar (copied from editor)
import { useAuth } from '../../context/AuthContext';
import { parseISO, formatDistanceStrict, isAfter, isValid } from 'date-fns';
import LoadingSpinner from '../../components/common/LoadingSpinner';

function LegacyFontConverter() {
    // --- Converter State ---
    const [inputText, setInputText] = useState('');
    const [outputText, setOutputText] = useState('');
    const [selectedFont, setSelectedFont] = useState('kruti'); // Represents the *Legacy* font (either source or target)
    const [conversionDirection, setConversionDirection] = useState('legacyToUnicode'); // 'legacyToUnicode' or 'unicodeToLegacy'
    const [isConverting, setIsConverting] = useState(false);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);

    // --- Navbar State & Hooks (Copied from Editor) ---
    const { user, logoutUser } = useAuth();
    const navigate = useNavigate();
    const [actualExpiryDate, setActualExpiryDate] = useState(null);
    const [expiryDisplayText, setExpiryDisplayText] = useState('Loading...');

    // --- Effects for Navbar (Copied from Editor) ---
    useEffect(() => {
        if (user?.expiry_date) {
            setActualExpiryDate(user.expiry_date);
        } else {
            setActualExpiryDate(null);
            setExpiryDisplayText('N/A');
        }
    }, [user]);

    useEffect(() => {
        if (!actualExpiryDate) {
            setExpiryDisplayText('N/A'); return;
        }
        try {
            const today = new Date(); today.setHours(0, 0, 0, 0);
            const expiryDateObj = parseISO(actualExpiryDate); expiryDateObj.setHours(0, 0, 0, 0);
            if (!isValid(expiryDateObj)) { setExpiryDisplayText('Invalid Date'); return; }
            if (isAfter(today, expiryDateObj)) { setExpiryDisplayText('Expired'); }
            else { setExpiryDisplayText(`${formatDistanceStrict(expiryDateObj, today)} Remaining`); }
        } catch (error) { setExpiryDisplayText('Error'); }
    }, [actualExpiryDate]);

    // --- Helper Functions ---
    const clearAlerts = () => {
        setError(null);
        setSuccessMessage(null);
    };

    const showSuccess = (message, duration = 3000) => {
        clearAlerts();
        setSuccessMessage(message);
        setTimeout(() => setSuccessMessage(null), duration);
    };

    const showError = (message) => {
        clearAlerts();
        setError(message);
    };

    // --- Converter Logic ---
    const handleConvert = useCallback(() => {
        clearAlerts();
        if (!inputText.trim()) {
            showError("Please paste text into the input area first.");
            return;
        }
        setIsConverting(true);
        setOutputText('');

        // Use setTimeout to allow UI update before potentially long conversion
        setTimeout(() => {
            try {
                let converted = '';
                const fontName = selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1); // e.g., "Kruti"

                if (conversionDirection === 'legacyToUnicode') {
                    // Convert FROM Legacy TO Unicode using your existing functions
                    switch (selectedFont) {
                        case 'kruti': converted = krutiunicode(inputText); break;
                        case 'preeti': converted = preeti_to_unicode(inputText); break; // Use NEW reverse function
                        case 'shree': converted = ShreeLipi_to_Unicode(inputText); break;
                        case 'shivaji': converted = shivaji_to_unicode(inputText); break; // Use NEW reverse function
                        // Add cases for other legacy fonts (APS, Yogesh) if needed
                        default: throw new Error("Invalid source legacy font selected.");
                    }
                    if (converted.trim() === inputText.trim() && inputText.length > 10) {
                        // Basic check if conversion likely didn't happen (output === input)
                        showError(`Conversion from ${fontName} might not have worked. Ensure input is valid ${fontName} text.`);
                    } else if (converted.trim()) {
                        showSuccess(`Conversion from ${fontName} to Unicode successful!`);
                    } else if (!inputText.trim()) {
                        showError("Input text is empty.");
                    } else {
                        showError(`Conversion from ${fontName} resulted in empty output. Check input text.`);
                    }

                } else { // unicodeToLegacy
                    // Convert FROM Unicode TO Legacy using the NEW functions
                    switch (selectedFont) {
                        case 'kruti': converted = unicode_to_kruti(inputText); break; // Use NEW function
                        case 'preeti': converted = unicode_to_preeti(inputText); break; // Use NEW function
                        case 'shree': converted = unicode_to_shree(inputText); break; // Use NEW function
                        case 'shivaji': converted = unicode_to_shivaji(inputText); break;
                        // Add cases for other legacy fonts (APS, Yogesh) if needed
                        default: throw new Error("Invalid target legacy font selected.");
                    }
                    if (converted.trim() === inputText.trim() && inputText.length > 10) {
                        showError(`Conversion to ${fontName} might not have worked. Ensure input is valid Unicode text.`);
                    } else if (converted.trim()) {
                        showSuccess(`Conversion from Unicode to ${fontName} successful!`);
                    } else if (!inputText.trim()) {
                        showError("Input text is empty.");
                    } else {
                        showError(`Conversion to ${fontName} resulted in empty output. Check input text.`);
                    }
                }
                setOutputText(converted);
            } catch (e) {
                console.error("Conversion Error:", e);
                showError(`Conversion failed: ${e.message || 'Unknown error'}`);
                setOutputText('');
            } finally {
                setIsConverting(false);
            }
        }, 50); // 50ms delay
    }, [inputText, selectedFont, conversionDirection]); // Dependencies updated

    const handleCopy = useCallback(() => {
        clearAlerts();
        if (!outputText) {
            showError("Nothing to copy.");
            return;
        }
        navigator.clipboard.writeText(outputText)
            .then(() => showSuccess("Converted text copied to clipboard!"))
            .catch(err => {
                console.error("Copy failed:", err);
                showError("Failed to copy text.");
            });
    }, [outputText]);

    const handleLogout = () => {
        logoutUser();
        navigate('/user/login');
    };

    // Determine labels based on direction
    const inputLabel = conversionDirection === 'legacyToUnicode'
        ? `Input Text (${selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)} Legacy)`
        : 'Input Text (Unicode)';
    const outputLabel = conversionDirection === 'legacyToUnicode'
        ? 'Output Text (Unicode)'
        : `Output Text (${selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)} Legacy)`;
    const convertButtonText = conversionDirection === 'legacyToUnicode'
        ? `Convert ${selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)} to Unicode`
        : `Convert Unicode to ${selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)}`;
    const sidebarTitle = conversionDirection === 'legacyToUnicode'
        ? 'Legacy Font to Unicode'
        : 'Unicode to Legacy Font';


    return (
        <div className={`${styles.pageWrapper} ${styles.converterPageWrapper}`}>
            {/* Top Navbar - Full Width */}
            <header className={styles.topNavbar}>
                <Link to="/user/editor" className={styles.navbarBrand}>Daily Speaking</Link>
                <div className={styles.navbarInfo}>
                    <span title={actualExpiryDate || ''}><i className="far fa-calendar-alt mr-1"></i> License: {expiryDisplayText}</span>
                    <span><i className="fas fa-phone-alt mr-1"></i> Enquiry: +91-**********</span>
                    <button onClick={() => navigate('/user/content-history')} className={styles.logoutButton}>
                        <i className="fas fa-history mr-1"></i> Content History
                    </button>
                    <button onClick={() => navigate('/user/chat-with-ai')} className={styles.logoutButton}>
                        <i className="fas fa-comments mr-1"></i> Chat with AI
                    </button>
                    <button onClick={() => navigate('/user/editor')} className={styles.logoutButton}>
                        <i className="fas fa-edit mr-1"></i> Back to Editor
                    </button>
                    <button onClick={handleLogout} className={styles.logoutButton}>
                        <i className="fas fa-sign-out-alt mr-1"></i> Logout
                    </button>
                </div>
            </header>

            {/* Alerts - Full Width */}
            <div className={styles.alertContainer}>
                {error && <div className={`${styles.alert} ${styles.alertDanger}`}>{error} <button onClick={clearAlerts} className={styles.alertClose}>×</button></div>}
                {successMessage && <div className={`${styles.alert} ${styles.alertSuccess}`}>{successMessage} <button onClick={clearAlerts} className={styles.alertClose}>×</button></div>}
            </div>

            {/* Content Area with Sidebar and Main */}
            <div className={styles.converterContentWrapper}>
                {/* Sidebar */}
                <aside className={styles.converterSidebar}>
                    <div className={styles.sidebarCard}>
                        <h2 className={styles.sidebarBrand}>
                            <i className="fas fa-font mr-2"></i>
                            Font Converter
                        </h2>
                    </div>

                    {/* Conversion Controls Card */}
                    <div className={styles.sidebarCard}>
                        <div className={styles.cardHeader}>
                            <h3 className={styles.sidebarTitle}>
                                <i className="fas fa-exchange-alt mr-2"></i>
                                {sidebarTitle}
                            </h3>
                        </div>
                        <div className={styles.cardBody}>
                            {/* Conversion Direction Selector */}
                            <div className={styles.controlGroup}>
                                <label>Conversion Direction</label>
                                <div className={styles.radioGroup}>
                                    <label className={styles.radioLabel}>
                                        <input
                                            type="radio"
                                            name="conversionDirection"
                                            value="legacyToUnicode"
                                            checked={conversionDirection === 'legacyToUnicode'}
                                            onChange={(e) => setConversionDirection(e.target.value)}
                                            disabled={isConverting}
                                        />
                                        <i className="fas fa-arrow-right mr-1"></i>
                                        Legacy → Unicode
                                    </label>
                                    <label className={styles.radioLabel}>
                                        <input
                                            type="radio"
                                            name="conversionDirection"
                                            value="unicodeToLegacy"
                                            checked={conversionDirection === 'unicodeToLegacy'}
                                            onChange={(e) => setConversionDirection(e.target.value)}
                                            disabled={isConverting}
                                        />
                                        <i className="fas fa-arrow-right mr-1"></i>
                                        Unicode → Legacy
                                    </label>
                                </div>
                            </div>

                            {/* Font Selector */}
                            <div className={styles.controlGroup}>
                                <label htmlFor="selectedFont">
                                    <i className="fas fa-font mr-1"></i>
                                    {conversionDirection === 'legacyToUnicode' ? 'Source Legacy Font' : 'Target Legacy Font'}
                                </label>
                                <select
                                    id="selectedFont"
                                    className={styles.formControlSm}
                                    value={selectedFont}
                                    onChange={(e) => setSelectedFont(e.target.value)}
                                    disabled={isConverting}
                                >
                                    <option value="kruti">Krutidev</option>
                                    <option value="preeti">Preeti</option>
                                    <option value="shree">Shreelipi</option>
                                    <option value="shivaji">Shivaji</option>
                                </select>
                            </div>

                            {/* Convert Button */}
                            <button
                                onClick={handleConvert}
                                className={`${styles.actionButton} ${styles.actionButtonPrimary} ${styles.btnBlock}`}
                                disabled={isConverting || !inputText.trim()}
                                title={convertButtonText}
                            >
                                {isConverting ? (
                                    <LoadingSpinner small centered={false} />
                                ) : (
                                    <>
                                        <i className="fas fa-sync-alt mr-1"></i>
                                        Convert Text
                                    </>
                                )}
                            </button>
                        </div>
                    </div>

                    {/* Actions Card */}
                    <div className={styles.sidebarCard}>
                        <div className={styles.cardHeader}>
                            <h3 className={styles.sidebarTitle}>
                                <i className="fas fa-tasks mr-2"></i>
                                Actions
                            </h3>
                        </div>
                        <div className={styles.cardBody}>
                            <button
                                onClick={handleCopy}
                                className={`${styles.actionButton} ${styles.actionButtonSuccess} ${styles.btnBlock}`}
                                disabled={!outputText || isConverting}
                                title="Copy converted text to clipboard"
                            >
                                <i className="fas fa-copy mr-1"></i>
                                Copy Output
                            </button>
                            <button
                                onClick={() => { setInputText(''); setOutputText(''); clearAlerts(); }}
                                className={`${styles.actionButton} ${styles.actionButtonDanger} ${styles.btnBlock}`}
                                disabled={isConverting}
                                title="Clear both input and output areas"
                            >
                                <i className="fas fa-trash mr-1"></i>
                                Clear All
                            </button>
                        </div>
                    </div>
                </aside>

                {/* Main Content Area */}
                <main className={styles.converterMainContent}>
                    {/* Converter Text Areas */}
                    <div className={styles.converterWrapper}>
                        {/* Input Area */}
                        <div className={styles.converterSection}>
                            <label htmlFor="inputText" className={styles.converterLabel}>
                                <i className="fas fa-file-import"></i>
                                {inputLabel}
                            </label>
                            <textarea
                                id="inputText"
                                className={styles.converterTextarea}
                                placeholder={`Paste your ${inputLabel.includes('Unicode') ? 'Unicode' : selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)} text here...`}
                                value={inputText}
                                onChange={(e) => setInputText(e.target.value)}
                                disabled={isConverting}
                            />
                        </div>

                        {/* Output Area */}
                        <div className={styles.converterSection}>
                            <label htmlFor="outputText" className={styles.converterLabel}>
                                <i className="fas fa-file-export"></i>
                                {outputLabel}
                            </label>
                            <textarea
                                id="outputText"
                                className={styles.converterTextarea}
                                placeholder={`Converted ${outputLabel.includes('Unicode') ? 'Unicode' : selectedFont.charAt(0).toUpperCase() + selectedFont.slice(1)} text will appear here...`}
                                value={outputText}
                                readOnly
                            />
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
}

export default LegacyFontConverter;