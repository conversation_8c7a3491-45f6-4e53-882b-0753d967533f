.chatPageContainer {
    display: flex;
    height: calc(100vh - var(--navbar-height, 60px) - var(--footer-height, 40px)); /* Adjust based on your layout's header/footer */
    width: 100%;
    max-width: 100%;
    margin: 0; /* Remove auto margins to ensure it takes full width */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sidebar {
    width: 250px;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    flex-grow: 0; /* Prevent sidebar from growing */
    background-color: #f8f8f8;
    border-right: 1px solid #e0e0e0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.sidebar h3 {
    font-size: 1.2em;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
}

.newChatButton {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
}

.newChatButton:hover {
    background-color: #0056b3;
}

.chatHistoryList {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
}

.chatHistoryList li {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    font-size: 0.95em;
    color: #555;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.chatHistoryList li:hover {
    background-color: #e9e9e9;
    color: #333;
}

.chatHistoryList li.activeChat {
    background-color: #e0f2ff;
    color: #007bff;
    font-weight: bold;
}

.chatArea {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: #fdfdfd;
}

.messagesContainer {
    flex-grow: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.welcomeMessage {
    text-align: center;
    color: #888;
    font-style: italic;
    margin-top: 50px;
}

.message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 20px;
    word-wrap: break-word;
    line-height: 1.4; /* Slightly reduced line height for compactness */
    font-size: 0.9em; /* Slightly smaller font size */
}

.message.user {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 5px;
}

.message.ai {
    background-color: #e2e6ea;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

.typingIndicator {
    font-style: italic;
    color: #888;
    background-color: #f0f0f0;
    padding: 8px 12px;
    border-radius: 15px;
    max-width: fit-content;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.inputForm {
    display: flex;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f0f0f0;
    gap: 10px;
}

.chatInput {
    flex-grow: 1;
    padding: 12px 15px;
    border: 1px solid #ccc;
    border-radius: 25px;
    font-size: 1em;
    outline: none;
    transition: border-color 0.2s ease;
}

.chatInput:focus {
    border-color: #007bff;
}

.sendButton {
    padding: 12px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
}

.sendButton:hover {
    background-color: #218838;
}