// src/pages/DashboardPage.js
import React from 'react';
import { Link } from 'react-router-dom';

function DashboardPage() {
    return (
        <div>
            {/* Content Header (Page header) */}
            <div className="content-header">
                <div className="container-fluid">
                    <div className="row mb-2">
                        <div className="col-sm-6"><h1 className="m-0">Dashboard</h1></div>
                        <div className="col-sm-6">
                            <ol className="breadcrumb float-sm-right">
                                <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                <li className="breadcrumb-item active">Dashboard</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            {/* /.content-header */}

            {/* Main content */}
            <section className="content">
                <div className="container-fluid">
                    {/* Info boxes */}
                    <div className="row">
                        <div className="col-lg-3 col-6">
                            <div className="small-box bg-info">
                                <div className="inner"><h3>Users</h3><p>Manage Users</p></div>
                                <div className="icon"><i className="fas fa-users"></i></div>
                                <Link to="/users" className="small-box-footer">More info <i className="fas fa-arrow-circle-right"></i></Link>
                            </div>
                        </div>
                         <div className="col-lg-3 col-6">
                            <div className="small-box bg-warning">
                                <div className="inner"><h3>Plans</h3><p>Manage Plans</p></div>
                                <div className="icon"><i className="fas fa-list-alt"></i></div>
                                <Link to="/plans" className="small-box-footer">More info <i className="fas fa-arrow-circle-right"></i></Link>
                            </div>
                        </div>
                         <div className="col-lg-3 col-6">
                            <div className="small-box bg-success">
                                <div className="inner"><h3>Admins</h3><p>Manage Admins</p></div>
                                <div className="icon"><i className="fas fa-user-shield"></i></div>
                                <Link to="/admins" className="small-box-footer">More info <i className="fas fa-arrow-circle-right"></i></Link>
                            </div>
                        </div>
                        {/* Add more boxes if needed */}
                    </div>
                    {/* /.row */}
                    {/* Add other dashboard content here */}
                </div>
            </section>
            {/* /.content */}
        </div>
    );
}

export default DashboardPage;