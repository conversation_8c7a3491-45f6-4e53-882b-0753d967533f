/* --- ModernLandingPage.css --- */

/* 1. Font Import (Example - Place at the top) */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500&display=swap');

/* 2. CSS Variables */
:root {
    --primary-color: #ffa88d;    /* Electric Blue */
    --secondary-color: #6c7ff9;  /* Bright Green */
    --dark-bg: #111827;          /* Cool Dark Gray/Blue */
    --light-bg: #F9FAFB;         /* Very Light Gray */
    --text-dark: #1F2937;        /* Dark Gray for text */
    --text-light: #F9FAFB;       /* Light Gray/Off-white for text on dark bg */
    --text-muted-light: rgba(249, 250, 251, 0.7); /* Adjusted muted light text */
    --font-heading: 'Poppins', sans-serif; /* Keep or change fonts if desired */
    --font-body: 'Roboto', sans-serif;    /* Keep or change fonts if desired */
    --border-light: rgba(249, 250, 251, 0.15); /* Adjusted light border */
  }

/* 3. Base Styles */
body {
  font-family: var(--font-body);
  background-color: var(--light-bg);
  color: var(--text-dark);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6, .navbar-brand, .fw-bolder /* Target specific elements */ {
  font-family: var(--font-heading);
  font-weight: 600;
}

/* 4. Navbar Enhancements */
.custom-navbar.fixed-top {
  background-color: rgba(26, 26, 26, 0.85); /* Dark semi-transparent */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); /* Safari */
  border-bottom: 1px solid var(--border-light);
  transition: background-color 0.3s ease;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.custom-navbar .navbar-brand {
  color: var(--text-light) !important;
  font-weight: 700;
}

.custom-navbar .nav-link {
  color: var(--text-muted-light) !important;
  transition: color 0.2s ease;
  font-weight: 500; /* Slightly bolder nav links */
}

.custom-navbar .nav-link:hover,
.custom-navbar .nav-link.active {
  color: var(--text-light) !important;
}

/* 5. Header Section */
.header-section {
  position: relative;
  background-size: cover;
  background-position: center center;
  color: var(--text-light);
  /* min-height: 100vh; */ /* Optional: Make header full viewport height */
  /* display: flex; */ /* Use flex if centering vertically */
  /* align-items: center; */
  padding-top: 10rem; /* Adjust padding considering fixed navbar */
  padding-bottom: 6rem;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(26, 26, 26, 0.6), rgba(26, 26, 26, 0.9)); /* Gradient overlay */
  z-index: 1;
}

.header-content {
  position: relative;
  z-index: 2;
}

/* Benefit Icons in Header */
.benefit-icon-item i {
  color: var(--secondary-color); /* Accent color for icons */
}
.benefit-icon-item span {
   color: var(--text-muted-light);
   font-size: 0.9rem;
}


/* Header Buttons */
.header-section .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}
.header-section .btn-primary:hover {
  background-color: darken(var(--primary-color), 10%);
  border-color: darken(var(--primary-color), 10%);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.header-section .btn-outline-light {
   border-width: 2px;
   transition: all 0.3s ease;
}
.header-section .btn-outline-light:hover {
  background-color: var(--text-light);
  color: var(--dark-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Optional: Header SVG Divider (Add styles if you implement one) */
/* .custom-shape-divider-bottom-xxxxx { ... } */


/* 6. Features Section */
#features {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

.features-section .section-title {
    margin-bottom: 4rem;
}

.feature-card {
  border: none; /* Remove default border */
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  background-color: #fff; /* Ensure cards are white or light */
  border-radius: 0.5rem; /* Slightly rounded corners */
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1) !important; /* More prominent hover shadow */
}

/* Feature Icon Styling (Replace with your custom icon styles) */
.feature-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%; /* Circular background */
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  margin-bottom: 1.5rem; /* Space below icon */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon-wrapper i {
  font-size: 1.75rem; /* Adjust icon size */
}
/* If using SVG icons */
/* .feature-icon-wrapper img,
.feature-icon-wrapper svg {
   width: 2rem;
   height: 2rem;
   fill: white;
} */

.feature-card .card-title {
    color: var(--primary-color); /* Use primary color for titles */
}

.feature-card .card-text {
    font-size: 0.9rem;
    color: #6c757d; /* Standard muted text */
}

/* Special CTA card */
.cta-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}
.cta-card .card-title {
    color: white;
}
.cta-card .card-text {
    color: rgba(255, 255, 255, 0.8);
}
.cta-card .btn-light {
    font-weight: 600;
    color: var(--primary-color);
}


/* 7. How it Works Section */
.how-it-works-section {
    padding-top: 6rem;
    padding-bottom: 6rem;
    background-color: var(--light-bg); /* Or a slightly different light color */
}

.how-it-works-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)); /* Reversed gradient or different */
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.how-it-works-icon-wrapper i {
    font-size: 1.75rem;
}


/* 9. Testimonials Section */
.testimonials-section {
    padding-top: 6rem;
    padding-bottom: 6rem;
    background-color: #fff; /* White background */
}

.testimonial-card {
    border: none;
    border-radius: 0.5rem;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1) !important;
}

.testimonial-card img {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

.testimonial-card .card-text {
    font-style: italic;
    font-size: 1rem;
}


/* 10. Contact Section */
.contact-section {
  background-color: #fff; /* White background for contrast */
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.contact-section .section-icon {
    /* Style the envelope icon wrapper if needed */
    color: var(--primary-color);
}

.contact-form-card {
  border-radius: 0.75rem !important; /* More pronounced rounding */
}

.contact-section .form-control {
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.contact-section .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25); /* Use Bootstrap's variable if available */
  /* box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25); */ /* Fallback */
}

.contact-section .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
   transition: all 0.3s ease;
}
.contact-section .btn-primary:hover {
   background-color: darken(var(--primary-color), 10%);
   border-color: darken(var(--primary-color), 10%);
   transform: translateY(-2px);
}

/* 8. Footer */
.custom-footer {
  background-color: var(--dark-bg);
  color: var(--text-muted-light);
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.custom-footer p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.footer-social-links a {
  color: var(--text-muted-light);
  transition: color 0.2s ease;
  font-size: 1.25rem; /* Larger social icons */
}

.footer-social-links a:hover {
  color: var(--text-light);
}