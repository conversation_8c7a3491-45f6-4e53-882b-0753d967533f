import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext'; // Import useAuth
import LoadingSpinner from '../components/common/LoadingSpinner';
import { getUserProfile } from '../services/api';
import mainStyles from './editor/main.module.css'; // Reusing styles from editor page for consistent header
import chatStyles from './chat.module.css'; // Styles for the chat page
import { askGemini } from '../services/gemini'; // Reusing existing Gemini service
import { parseISO, isAfter, isValid, formatDistanceStrict } from 'date-fns'; // For expiry date
import localforage from 'localforage'; // Import localforage for IndexedDB

const ChatWithAIPage = () => {
    const { user, logoutUser, userIsLoading } = useAuth(); // Get userIsLoading from AuthContext
    const navigate = useNavigate();

    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [chatHistory, setChatHistory] = useState([]); // Stores an array of {id, title} for chat sessions
    const [currentChatId, setCurrentChatId] = useState(null); // ID of the currently active chat session
    const [actualExpiryDate, setActualExpiryDate] = useState(null); // License expiry date
    const [expiryDisplayText, setExpiryDisplayText] = useState('Loading...');
    const [error, setError] = useState(null); // For displaying errors on the page
    const [successMessage, setSuccessMessage] = useState(null); // For displaying success messages
    const [isAILoading, setIsAILoading] = useState(false); // New state for AI typing indicator

    // Clear alerts function
    const clearAlerts = useCallback(() => {
        setError(null);
        setSuccessMessage(null);
    }, []);

    // Show success message
    const showSuccess = useCallback((message, duration = 3000) => {
        clearAlerts();
        setSuccessMessage(message);
        setTimeout(() => setSuccessMessage(null), duration);
    }, [clearAlerts]);

    // Show error message
    const showError = useCallback((message) => {
        clearAlerts();
        setError(message);
    }, [clearAlerts]);

    // Function to get a summarized version of the chat history
    const getSummarizedChatHistory = useCallback((history) => {
        const MAX_HISTORY_MESSAGES = 5; // Send only the last 5 messages for context
        if (history.length <= MAX_HISTORY_MESSAGES) {
            return history;
        }
        return history.slice(-MAX_HISTORY_MESSAGES);
    }, []);

    // Fetch user profile to ensure token validity
    useEffect(() => {
        const verifyToken = async () => {
            try {
                await getUserProfile();
            } catch (err) {
                console.error("Session verification failed:", err);
                logoutUser();
                navigate('/user/login');
            }
        };
        verifyToken();
    }, [logoutUser, navigate]);

    // Expiry date calculation effect
    useEffect(() => {
        if (user?.expiry_date) {
            setActualExpiryDate(user.expiry_date);
        } else {
            setActualExpiryDate(null);
            setExpiryDisplayText('N/A');
        }
    }, [user]);

    useEffect(() => {
        if (!actualExpiryDate) {
            setExpiryDisplayText('N/A');
            return;
        }
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const expiryDateObj = parseISO(actualExpiryDate);
            expiryDateObj.setHours(0, 0, 0, 0);

            if (!isValid(expiryDateObj)) {
                setExpiryDisplayText('Invalid Date');
                return;
            }

            if (isAfter(today, expiryDateObj)) {
                setExpiryDisplayText('Expired');
            } else {
                setExpiryDisplayText(`${formatDistanceStrict(expiryDateObj, today)} Remaining`);
            }
        } catch (error) {
            console.error("Error calculating expiry text:", error);
            setExpiryDisplayText('Error');
        }
    }, [actualExpiryDate]);

    // Effect to load chat history (list of chat metadata) and current chat ID, and messages
    useEffect(() => {
        const loadInitialChatData = async () => {
            console.log("Main useEffect triggered. user:", user?.id, "userIsLoading:", userIsLoading);
            if (!userIsLoading) {
                if (user && user.id) {
                    const chatIdsKey = `chatids_${user.id}`;
                    let storedChatHistory = [];
                    try {
                        storedChatHistory = await localforage.getItem(chatIdsKey) || [];
                        if (!Array.isArray(storedChatHistory)) {
                            console.warn("Stored chat history (metadata) is not an array. Resetting.");
                            storedChatHistory = [];
                        }
                    } catch (e) {
                        console.error("Error parsing chat history metadata from localforage:", e);
                        storedChatHistory = [];
                    }
                    setChatHistory(storedChatHistory);
                    console.log("Main useEffect: Loaded chat history metadata:", storedChatHistory);

                    let initialCurrentChatId = null;
                    const storedCurrentChatId = await localforage.getItem(`currentChatId_${user.id}`);
                    if (storedCurrentChatId) {
                        initialCurrentChatId = parseInt(storedCurrentChatId, 10);
                    } else if (storedChatHistory.length > 0) {
                        initialCurrentChatId = storedChatHistory[0].id;
                    }
                    setCurrentChatId(initialCurrentChatId);
                    console.log("Main useEffect: Initial current chat ID:", initialCurrentChatId);

                    // Now load messages for the determined initialCurrentChatId
                    if (initialCurrentChatId !== null) {
                        const chatDataKey = `chat_${initialCurrentChatId}`;
                        let storedChatData = null;
                        try {
                            storedChatData = await localforage.getItem(chatDataKey);
                        } catch (e) {
                            console.error(`Main useEffect: Error parsing chat data for chat ID ${initialCurrentChatId} from localforage:`, e);
                        }

                        if (storedChatData && storedChatData.messages) {
                            setMessages(storedChatData.messages);
                            console.log("Main useEffect: Messages loaded for initial chat:", storedChatData.messages);
                        } else {
                            setMessages([]);
                            console.log("Main useEffect: No messages found or corrupted for initial chat ID:", initialCurrentChatId);
                        }
                    } else {
                        setMessages([]);
                        console.log("Main useEffect: No initial chat ID, clearing messages.");
                    }

                } else {
                    // User logged out or not available after loading, clear states
                    console.log("Main useEffect: User not available or logged out, clearing all chat states.");
                    setChatHistory([]);
                    setCurrentChatId(null);
                    setMessages([]);
                }
            }
        };
        loadInitialChatData();
    }, [user, userIsLoading]); // Depend on user and userIsLoading


    // Effect to save chat history (metadata) and current chat ID
    useEffect(() => {
        const saveChatMetadata = async () => {
            console.log("Save metadata useEffect triggered. chatHistory length:", chatHistory.length, "currentChatId:", currentChatId);
            if (!userIsLoading && user && user.id) {
                const chatIdsKey = `chatids_${user.id}`;
                console.log("Save metadata useEffect: chatHistory content being saved:", chatHistory); // <-- NEW LOG
                await localforage.setItem(chatIdsKey, chatHistory); // Save directly as object/array
                console.log("Save metadata useEffect: Saved chat history metadata.");

                if (currentChatId !== null) {
                    await localforage.setItem(`currentChatId_${user.id}`, currentChatId.toString());
                    console.log("Save metadata useEffect: Saved current chat ID:", currentChatId);
                } else {
                    await localforage.removeItem(`currentChatId_${user.id}`);
                    console.log("Save metadata useEffect: Removed current chat ID.");
                }
            }
        };
        saveChatMetadata();
    }, [chatHistory, currentChatId, user, userIsLoading]);

    // Effect to save the current chat's messages
    useEffect(() => {
        const saveChatMessages = async () => {
            console.log("Save messages useEffect triggered. messages length:", messages.length, "currentChatId:", currentChatId);
            if (!userIsLoading && user && user.id && currentChatId !== null && messages.length > 0) {
                const chatDataKey = `chat_${currentChatId}`;
                // Find the title for the current chat from chatHistory
                const currentChatMetadata = chatHistory.find(chat => chat.id === currentChatId);
                const chatTitle = currentChatMetadata ? currentChatMetadata.title : `Chat ${currentChatId}`;
                
                const chatDataToSave = {
                    id: currentChatId,
                    title: chatTitle,
                    messages: messages,
                };
                await localforage.setItem(chatDataKey, chatDataToSave); // Save directly as object/array
                console.log("Save messages useEffect: Saved messages for chat ID:", currentChatId);
            }
        };
        saveChatMessages();
    }, [messages, currentChatId, user, userIsLoading, chatHistory]); // Include chatHistory to get title

    const handleSendMessage = async (e) => {
        e.preventDefault();
        if (input.trim() === '') return;

        const userMessage = { text: input, sender: 'user' };
        console.log("handleSendMessage: User message sent:", userMessage);

        // Optimistically update messages
        setMessages(prevMessages => {
            console.log("handleSendMessage: Setting messages (optimistic user message)");
            return [...prevMessages, userMessage];
        });
        setInput('');

        let newChatId = currentChatId;
        let newChatTitle = '';
        if (!currentChatId) {
            newChatId = Date.now(); // Corrected from Date.Date.now()
            newChatTitle = userMessage.text.substring(0, 30) + '...'; // Use user's first message as title
            console.log("handleSendMessage: New chat initiated. New ID:", newChatId, "Title:", newChatTitle);
            // Update chat history metadata right away for new chat, and ensure it's at the top
            setChatHistory(prevHistory => {
                const updated = [{ id: newChatId, title: newChatTitle }, ...prevHistory];
                console.log("handleSendMessage: setChatHistory (new chat metadata):", updated);
                return updated;
            });
            setCurrentChatId(newChatId); // Set current chat ID immediately
        }

        // Simulate AI response with the existing Gemini service
        setIsAILoading(true); // Set AI loading to true
        // Create the full conversation history including the new user message
        // without waiting for the state update to complete.
        const fullConversationHistory = [...messages, userMessage];

        try {
            console.log("handleSendMessage: Asking Gemini for response with summarized history...");
            const summarizedHistory = getSummarizedChatHistory(fullConversationHistory);
            // Pass the explicitly constructed summarized conversation history
            const aiResponse = await askGemini(summarizedHistory);
            const aiMessage = { text: aiResponse, sender: 'ai' };
            console.log("handleSendMessage: AI response received:", aiMessage);

            // Update messages with AI response
            setMessages(prevMessages => {
                console.log("handleSendMessage: Setting messages (AI response)");
                return [...prevMessages, aiMessage];
            });

        } catch (error) {
            console.error("handleSendMessage: Error getting AI response:", error);
            const errorMessage = { text: "Sorry, I couldn't get a response. Please try again.", sender: 'ai' };
            setMessages(prevMessages => [...prevMessages, errorMessage]);
        } finally {
            setIsAILoading(false); // Set AI loading to false after response or error
        }
    };

    const loadChat = useCallback(async (chatId) => { // Made async
        console.log("loadChat: Loading chat with ID:", chatId);
        setCurrentChatId(chatId);
        // Load messages for the selected chat immediately
        const chatDataKey = `chat_${chatId}`;
        let storedChatData = null;
        try {
            storedChatData = await localforage.getItem(chatDataKey); // Use localforage
        } catch (e) {
            console.error(`loadChat: Error parsing chat data for chat ID ${chatId} from localforage:`, e);
        }

        if (storedChatData && storedChatData.messages) {
            console.log("loadChat: Messages loaded:", storedChatData.messages);
            setMessages(storedChatData.messages);
        } else {
            console.log("loadChat: No messages found or corrupted for chat ID:", chatId);
            setMessages([]);
        }

        // Reorder chatHistory to bring the loaded chat to the top (most recent)
        setChatHistory(prevHistory => {
            const chatToMove = prevHistory.find(chat => chat.id === chatId);
            if (chatToMove) {
                const updatedHistory = [chatToMove, ...prevHistory.filter(chat => chat.id !== chatId)];
                console.log("loadChat: Reordered chatHistory:", updatedHistory);
                return updatedHistory;
            }
            return prevHistory;
        });
    }, []);

    const startNewChat = useCallback(() => {
        console.log("startNewChat: Starting new chat.");
        setMessages([]);
        setInput('');
        setCurrentChatId(null); // This will trigger a new chat creation on next message
    }, []);

    const handleLogout = useCallback(() => {
        clearAlerts();
        logoutUser();
        navigate('/user/login');
    }, [logoutUser, navigate, clearAlerts]);

    return (
        <div className={mainStyles.pageWrapper}>
            <main className={mainStyles.mainContent}>
                <header className={mainStyles.topNavbar}>
                    <Link to="/user/editor" className={mainStyles.navbarBrand}>Daily Speaking</Link>
                    <div className={mainStyles.navbarInfo}>
                        <span title={actualExpiryDate || ''}><i className="far fa-calendar-alt mr-1"></i> License: {expiryDisplayText}</span>
                        <span><i className="fas fa-phone-alt mr-1"></i> Enquiry: +91-**********</span>
                        <button onClick={() => navigate('/user/content-history')} className={mainStyles.logoutButton}>
                            <i className="fas fa-history mr-1"></i> Content History
                        </button>
                        <button onClick={handleLogout} className={mainStyles.logoutButton}>
                            <i className="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </div>
                </header>

                <div className={mainStyles.alertContainer}>
                    {error && <div className={`${mainStyles.alert} ${mainStyles.alertDanger}`}>{error} <button onClick={clearAlerts} className={mainStyles.alertClose}>×</button></div>}
                    {successMessage && <div className={`${mainStyles.alert} ${mainStyles.alertSuccess}`}>{successMessage} <button onClick={clearAlerts} className={mainStyles.alertClose}>×</button></div>}
                </div>

                <div className={chatStyles.chatPageContainer}>
                    <div className={chatStyles.sidebar}>
                        <h3>Chat History</h3>
                        <button onClick={startNewChat} className={chatStyles.newChatButton}>
                            <i className="fas fa-plus-circle" style={{ marginRight: '8px' }}></i> New Chat
                        </button>
                        <ul className={chatStyles.chatHistoryList}>
                            {chatHistory.map((chat) => (
                                <li key={chat.id} onClick={() => loadChat(chat.id)} className={chat.id === currentChatId ? chatStyles.activeChat : ''}>
                                    {chat.title}
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div className={chatStyles.chatArea}>
                        <div className={chatStyles.messagesContainer}>
                            {messages.length === 0 && !isAILoading && <div className={chatStyles.welcomeMessage}>Ask me anything about Indian Law!</div>}
                            {messages.map((msg, index) => (
                                <div key={index} className={`${chatStyles.message} ${chatStyles[msg.sender]}`}>
                                    {msg.sender === 'ai' ? (
                                        <div dangerouslySetInnerHTML={{ __html: msg.text }} />
                                    ) : (
                                        msg.text
                                    )}
                                </div>
                            ))}
                            {isAILoading && (
                                <div className={`${chatStyles.message} ${chatStyles.ai} ${chatStyles.typingIndicator}`}>
                                    Typing...
                                </div>
                            )}
                        </div>
                        <form onSubmit={handleSendMessage} className={chatStyles.inputForm}>
                            <input
                                type="text"
                                value={input}
                                onChange={(e) => setInput(e.target.value)}
                                placeholder="Type your query about Indian Law..."
                                className={chatStyles.chatInput}
                            />
                            <button type="submit" className={chatStyles.sendButton}>
                                <i className="fas fa-paper-plane" style={{ marginRight: '5px' }}></i> Send
                            </button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default ChatWithAIPage;