// src/pages/auth/UserRegisterPage.js
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
// **** Import the states list ****
import { indianStates } from '../../data/indianStates'; // Adjust path if needed

function UserRegisterPage() {
    const { registerUser, userIsLoading, userAuthError, userRegisterSuccess, userToken, adminToken } = useAuth();
    const navigate = useNavigate();

    const [formData, setFormData] = useState({
        name: '',
        email: '',
        state: '', // <-- Keep initial state as empty string for dropdown placeholder
        city: '',
        phone: '',
        password: '',
        password_confirmation: '',
        executive: '',
    });
    const [formErrors, setFormErrors] = useState({});

    // Redirect if already logged in
    useEffect(() => {
        if (userToken || adminToken) {
            navigate('/user/dashboard'); // Or appropriate redirect
        }
    }, [userToken, adminToken, navigate]);

    // Add/Remove body class
    useEffect(() => {
        document.body.classList.add('register-page');
        return () => { document.body.classList.remove('register-page'); };
    }, []);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        // Handle checkbox inputs differently
        const inputValue = type === 'checkbox' ? checked : value;
        setFormData(prev => ({ ...prev, [name]: inputValue }));
        if (formErrors[name]) {
            setFormErrors(prev => ({ ...prev, [name]: null }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (userIsLoading) return;
        setFormErrors({});
        if (formData.password !== formData.password_confirmation) {
            setFormErrors({ password_confirmation: ["Password confirmation does not match."] });
            return;
        }
        const success = await registerUser(formData);
        // Handle success/error (already implemented)
    };

    const displayError = (field) => {
        const errorToShow = formErrors?.[field] || (userAuthError && userAuthError.includes(field) ? userAuthError : null);
        return errorToShow ? <span className="invalid-feedback d-block">{Array.isArray(errorToShow) ? errorToShow.join(', '): errorToShow}</span> : null;
    }

    return (
        <div className="register-box">
            <div className="register-logo">
                <a href="#"><b>User</b> Registration</a>
            </div>
            <div className="card">
                <div className="card-body register-card-body">
                    <p className="login-box-msg">Register a new membership</p>

                    {/* Status Messages */}
                    {userRegisterSuccess && <div className="alert alert-success text-center mb-3">{userRegisterSuccess} <Link to="/user/login">Login here</Link></div>}
                    {userAuthError && !userRegisterSuccess && <div className="alert alert-danger text-center mb-3">{userAuthError}</div>}

                    <form onSubmit={handleSubmit} noValidate>
                        {/* Name Input */}
                        <div className="input-group mb-3">
                            <input type="text" name="name" className={`form-control ${displayError('name') ? 'is-invalid' : ''}`} placeholder="Full name" value={formData.name} onChange={handleChange} required disabled={userIsLoading} />
                            <div className="input-group-append"><div className="input-group-text"><span className="fas fa-user"></span></div></div>
                            {displayError('name')}
                        </div>
                        {/* Email Input */}
                        <div className="input-group mb-3">
                            <input type="email" name="email" className={`form-control ${displayError('email') ? 'is-invalid' : ''}`} placeholder="Email" value={formData.email} onChange={handleChange} required disabled={userIsLoading}/>
                            <div className="input-group-append"><div className="input-group-text"><span className="fas fa-envelope"></span></div></div>
                            {displayError('email')}
                        </div>

                        {/* **** STATE DROPDOWN (Replaced Input) **** */}
                        <div className="input-group mb-3">
                            <select
                                name="state"
                                id="state" // Keep ID for label association if needed
                                className={`form-control ${displayError('state') ? 'is-invalid' : ''}`}
                                value={formData.state} // Bind to state
                                onChange={handleChange}
                                required // Make state selection mandatory
                                disabled={userIsLoading}
                            >
                                <option value="" disabled>-- Select State --</option> {/* Placeholder */}
                                {indianStates.map(stateName => (
                                    <option key={stateName} value={stateName}>
                                        {stateName}
                                    </option>
                                ))}
                            </select>
                            {/* Optional: Add icon if desired, might need slight style adjustments */}
                            <div className="input-group-append"><div className="input-group-text"><span className="fas fa-map-marker-alt"></span></div></div>
                            {displayError('state')}
                        </div>
                        {/* ****************************************** */}

                        {/* City Input */}
                        <div className="input-group mb-3">
                           <input type="text" name="city" className={`form-control ${displayError('city') ? 'is-invalid' : ''}`} placeholder="City" value={formData.city} onChange={handleChange} required disabled={userIsLoading}/>
                           <div className="input-group-append"><div className="input-group-text"><span className="fas fa-city"></span></div></div>
                           {displayError('city')}
                       </div>
                        {/* Phone Input */}
                       <div className="input-group mb-3">
                           <input type="tel" name="phone" className={`form-control ${displayError('phone') ? 'is-invalid' : ''}`} placeholder="Phone" value={formData.phone} onChange={handleChange} required disabled={userIsLoading}/>
                           <div className="input-group-append"><div className="input-group-text"><span className="fas fa-phone"></span></div></div>
                           {displayError('phone')}
                       </div>

                       {/* Executive Input */}
                       <div className="input-group mb-3">
                           <input
                               type="text"
                               name="executive"
                               className={`form-control ${displayError('executive') ? 'is-invalid' : ''}`}
                               placeholder="Executive (Optional)"
                               value={formData.executive}
                               onChange={handleChange}
                               disabled={userIsLoading}
                           />
                           <div className="input-group-append"><div className="input-group-text"><span className="fas fa-user-tie"></span></div></div>
                           {displayError('executive')}
                       </div>
                        {/* Password Input */}
                       <div className="input-group mb-3">
                           <input type="password" name="password" className={`form-control ${displayError('password') ? 'is-invalid' : ''}`} placeholder="Password" value={formData.password} onChange={handleChange} required disabled={userIsLoading}/>
                           <div className="input-group-append"><div className="input-group-text"><span className="fas fa-lock"></span></div></div>
                           {displayError('password')}
                       </div>
                        {/* Password Confirmation Input */}
                       <div className="input-group mb-3">
                           <input type="password" name="password_confirmation" className={`form-control ${displayError('password_confirmation') ? 'is-invalid' : ''}`} placeholder="Retype password" value={formData.password_confirmation} onChange={handleChange} required disabled={userIsLoading}/>
                           <div className="input-group-append"><div className="input-group-text"><span className="fas fa-lock"></span></div></div>
                           {displayError('password_confirmation')}
                       </div>

                        <div className="row">
                            <div className="col-12">
                                <button type="submit" className="btn btn-primary btn-block" disabled={userIsLoading}>
                                    {userIsLoading ? 'Registering...' : 'Register'}
                                </button>
                            </div>
                        </div>
                    </form>

                    <p className="mt-3 mb-0 text-center">
                        <Link to="/user/login" className="text-center">I already have a membership</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}

export default UserRegisterPage;