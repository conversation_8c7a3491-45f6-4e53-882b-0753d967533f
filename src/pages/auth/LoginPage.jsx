// src/pages/auth/LoginPage.js
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
// Import the updated Auth hook
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext'; // Adjust path if needed

function AdminLoginPage() { // Renamed component for clarity (Optional)
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    // **** FIX: Destructure the CORRECT functions/state for ADMIN ****
    const {
        loginAdmin,        // <-- Use loginAdmin
        adminIsLoading,    // <-- Use adminIsLoading
        adminAuthError,    // <-- Use adminAuthError
        adminToken,        // <-- Use adminToken
        userToken          // Also get userToken to check if user is logged in
     } = useAuth();
    // ***************************************************************

    const navigate = useNavigate();
    const location = useLocation();
    const from = location.state?.from?.pathname || "/dashboard"; // Redirect to admin dashboard

    // Redirect if already logged in (as admin OR user - might adjust later)
    useEffect(() => {
        if (adminToken || userToken) {
            console.log("Already logged in, redirecting from Admin Login page");
            navigate(from, { replace: true });
        }
    }, [adminToken, userToken, navigate, from]);

    // Add/Remove AdminLTE body class
    useEffect(() => {
        document.body.classList.add('login-page');
        return () => { document.body.classList.remove('login-page'); };
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (adminIsLoading) return; // Use adminIsLoading

        // **** FIX: Call the correct admin login function ****
        const success = await loginAdmin(username, password); // <-- Use loginAdmin
        // ****************************************************

        if (success) {
            navigate('/dashboard', { replace: true }); // Navigate to admin dashboard
        }
        // adminAuthError will be set by the context if login fails
    };

    return (
        <div className="login-box">
            <div className="login-logo">
                <a href="#"><b>Admin</b> Panel</a>
            </div>
            <div className="card">
                <div className="card-body login-card-body">
                    <p className="login-box-msg">Admin Sign In</p> {/* Updated message */}

                    {/* **** FIX: Use adminAuthError **** */}
                    {adminAuthError && (
                        <div className="alert alert-danger text-center mb-3" role="alert">
                            {adminAuthError}
                        </div>
                    )}
                    {/* ********************************* */}

                    <form onSubmit={handleSubmit}>
                        {/* Username Input */}
                        <div className="input-group mb-3">
                            <input
                                type="text"
                                // **** FIX: Use adminAuthError for styling ****
                                className={`form-control ${adminAuthError ? 'is-invalid' : ''}`}
                                placeholder="Username"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                required
                                // **** FIX: Use adminIsLoading ****
                                disabled={adminIsLoading}
                            />
                            <div className="input-group-append">
                                <div className="input-group-text"><span className="fas fa-user"></span></div>
                            </div>
                        </div>

                        {/* Password Input */}
                        <div className="input-group mb-3">
                            <input
                                type="password"
                                // **** FIX: Use adminAuthError for styling ****
                                className={`form-control ${adminAuthError ? 'is-invalid' : ''}`}
                                placeholder="Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                // **** FIX: Use adminIsLoading ****
                                disabled={adminIsLoading}
                            />
                            <div className="input-group-append">
                                <div className="input-group-text"><span className="fas fa-lock"></span></div>
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="row">
                            <div className="col-12">
                                <button
                                    type="submit"
                                    className="btn btn-primary btn-block"
                                    // **** FIX: Use adminIsLoading ****
                                    disabled={adminIsLoading}
                                >
                                    {adminIsLoading ? 'Signing In...' : 'Sign In'}
                                </button>
                            </div>
                        </div>
                    </form>
                    <p className="mb-0 mt-3 text-center">
                         {/* Link to User Login */}
                        <Link to="/user/login" className="text-center text-sm text-muted">User Login</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}

// Update export if you renamed the component
export default AdminLoginPage; // Or LoginPage if you didn't rename