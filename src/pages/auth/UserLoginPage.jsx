// src/pages/auth/UserLoginPage.js
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom'; // Added Link
import { useAuth } from '../../context/AuthContext'; // Use the updated context

function UserLoginPage() {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');

    // Get USER specific states/functions from the updated context
    const { loginUser, userIsLoading, userAuthError, userToken, adminToken } = useAuth();

    const navigate = useNavigate();
    const location = useLocation();
    // Redirect logged-in users to a general dashboard (or user-specific page)
    const from = location.state?.from?.pathname || "/user/dashboard"; // Example user dashboard route

    // Redirect if already logged in (as user OR admin - adjust if needed)
    useEffect(() => {
        // Only redirect if the USER is logged in
        if (userToken) {
            console.log("User already logged in, redirecting from User Login page");
            navigate('/user/editor', { replace: true }); // Redirect logged-in users to editor
        }
        // If adminToken exists but userToken doesn't, this page WILL now render
    }, [userToken, navigate]); // Only need userToken and navigate as 
    // Add/Remove AdminLTE body class
    useEffect(() => {
        document.body.classList.add('login-page');
        return () => {
            document.body.classList.remove('login-page');
        };
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (userIsLoading) return;
        const success = await loginUser(email, password); // Use loginUser
        if (success) {
            // Navigate to user dashboard on successful login
            console.log("User Login Success: Navigating to /user/editor"); // DEBUG
            navigate('/user/editor', { replace: true }); // Navigate to the editor
        }
    };

    return (
        <div className="login-box">
            <div className="login-logo">
                <a href="#"><b>User</b> Login</a> {/* Changed title */}
            </div>
            <div className="card">
                <div className="card-body login-card-body">
                    <p className="login-box-msg">Sign in to your account</p>

                    {userAuthError && (
                        <div className="alert alert-danger text-center mb-3" role="alert">
                            {userAuthError}
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        {/* Email Input */}
                        <div className="input-group mb-3">
                            <input
                                type="email" // Email for users
                                className={`form-control ${userAuthError ? 'is-invalid' : ''}`}
                                placeholder="Email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                disabled={userIsLoading}
                            />
                            <div className="input-group-append">
                                <div className="input-group-text"><span className="fas fa-envelope"></span></div>
                            </div>
                        </div>

                        {/* Password Input */}
                        <div className="input-group mb-3">
                            <input
                                type="password"
                                className={`form-control ${userAuthError ? 'is-invalid' : ''}`}
                                placeholder="Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                disabled={userIsLoading}
                            />
                            <div className="input-group-append">
                                <div className="input-group-text"><span className="fas fa-lock"></span></div>
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="row">
                            <div className="col-12">
                                <button type="submit" className="btn btn-primary btn-block" disabled={userIsLoading}>
                                    {userIsLoading ? 'Signing In...' : 'Sign In'}
                                </button>
                            </div>
                        </div>
                    </form>

                    <p className="mb-0 mt-3 text-center">
                        {/* Link to Registration Page */}
                        <Link to="/user/register" className="text-center">Register a new membership</Link>
                    </p>
                     <p className="mb-0 mt-1 text-center">
                         {/* Link to Admin Login */}
                        <Link to="/login" className="text-center text-sm text-muted">Admin Login</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}

export default UserLoginPage;