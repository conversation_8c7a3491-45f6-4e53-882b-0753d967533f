// src/pages/LandingPage.js
import React, { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom'; // Use RouterLink for internal navigation
import './landing-page.css'; // Import the custom CSS (We'll create this next)

// NOTE: Assumes Bootstrap 4 & Font Awesome CSS are loaded globally via CDN

function LandingPage() {
    // --- Contact Form State (remains the same) ---
    const [formData, setFormData] = useState({ name: '', email: '', phone: '', message: '' });
    const [formStatus, setFormStatus] = useState({ submitted: false, error: false, message: '' });

    const handleInputChange = (e) => { /* ... same as before ... */
        const { id, value } = e.target;
        setFormData(prevData => ({ ...prevData, [id]: value, }));
    };
    const handleFormSubmit = async (e) => { /* ... same as before, uses simulation ... */
        e.preventDefault();
        setFormStatus({ submitted: false, error: false, message: 'Submitting...' });
        console.log("Form data submitted (replace with actual API call):", formData);
        if (formData.name && formData.email && formData.phone && formData.message) {
             setTimeout(() => {
                setFormStatus({ submitted: true, error: false, message: 'Form submission successful! (Simulation)' });
                setFormData({ name: '', email: '', phone: '', message: '' });
             }, 1000);
        } else {
            setTimeout(() => {
                 setFormStatus({ submitted: false, error: true, message: 'Error! Please fill all fields. (Simulation)' });
            }, 1000);
        }
     };
    // --- End Contact Form Logic ---

    // Style for header background image (Image is in public/)
    const headerStyle = {
        backgroundImage: `url(${process.env.PUBLIC_URL}/header-image.png)`,
    };

    return (
        <>
            {/* Responsive navbar - Adapted for BS4 */}
            <nav className="navbar custom-navbar fixed-top navbar-expand-lg navbar-dark">
                <div className="container px-lg-5 px-3"> {/* Adjusted padding */}
                    <a className="navbar-brand" href="#!">Daily Speaking</a>
                    {/* BS4 Toggle */}
                    <button className="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span className="navbar-toggler-icon"></span>
                    </button>
                    <div className="collapse navbar-collapse" id="navbarSupportedContent">
                        {/* BS4 margin: ml-auto */}
                        <ul className="navbar-nav ml-auto mb-2 mb-lg-0">
                            <li className="nav-item"><a className="nav-link active" aria-current="page" href="#header">Home</a></li>
                            <li className="nav-item"><a className="nav-link" href="#features">Features</a></li>
                            <li className="nav-item"><a className="nav-link" href="#how-it-works">How it Works</a></li>
                            <li className="nav-item"><a className="nav-link" href="#contact">Contact Us</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            {/* Header - Adapted for BS4 */}
            <header className="header-section" id="header" style={headerStyle}>
                 <div className="header-overlay"></div> {/* Overlay Div */}
                <div className="container px-lg-5 px-3 header-content"> {/* Content Container */}
                    <div className="row justify-content-center">
                        <div className="col-lg-10 col-xl-9"> {/* Adjusted column width */}
                            <div className="text-center my-5">
                                {/* BS4 font weight: font-weight-bold */}
                                <h1 className="display-4 font-weight-bold text-white mb-2">SPEECH TO TEXT, SIMPLIFIED</h1>
                                {/* BS4 text color class */}
                                <p className="lead text-white-50 mb-4">Instantly transcribe spoken words into accurate text across multiple languages. Boost your productivity effortlessly.</p>
                                {/* Benefit Icons using Font Awesome */}
                                <div className="row justify-content-center text-center mt-4 mb-5"> {/* Removed gy-3, handle spacing with margin/padding */}
                                    <div className="col-6 col-md-auto d-flex align-items-center justify-content-center benefit-icon-item mb-3 mb-md-0">
                                        <i className="fas fa-bolt mr-2" style={{fontSize: '1.4rem'}}></i> {/* Changed icon & margin */}
                                        <span>Faster Than Typing</span>
                                    </div>
                                    <div className="col-6 col-md-auto d-flex align-items-center justify-content-center benefit-icon-item mb-3 mb-md-0">
                                        <i className="fas fa-language mr-2" style={{fontSize: '1.4rem'}}></i> {/* Changed icon & margin */}
                                        <span>11+ Languages</span>
                                    </div>
                                    <div className="col-6 col-md-auto d-flex align-items-center justify-content-center benefit-icon-item mb-3 mb-md-0">
                                        <i className="fas fa-check-circle mr-2" style={{fontSize: '1.4rem'}}></i> {/* Changed icon & margin */}
                                        <span>High Accuracy</span>
                                    </div>
                                    <div className="col-6 col-md-auto d-flex align-items-center justify-content-center benefit-icon-item mb-3 mb-md-0">
                                        <i className="fas fa-share-alt mr-2" style={{fontSize: '1.4rem'}}></i> {/* Changed icon & margin */}
                                        <span>Easy Sharing</span>
                                    </div>
                                </div>
                                {/* Buttons - Use RouterLink for internal navigation */}
                                <div className="d-grid gap-3 d-sm-flex justify-content-sm-center">
                                    {/* BS4 margin: mr-sm-3 */}
                                    <RouterLink className="btn btn-primary btn-lg px-4 mr-sm-3 mb-3 mb-sm-0" to="/user/login">Sign In</RouterLink> {/* Link to USER login */}
                                    <RouterLink className="btn btn-outline-light btn-lg px-4" to="/user/register">Register</RouterLink> {/* Link to USER register */}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Features section - Using BS4 Cards */}
            <section className="py-5 features-section" id="features">
                <div className="container px-lg-5 px-3 my-5">
                    <div className="text-center section-title">
                        <h2 className="font-weight-bold">Core Features</h2>
                        {/* BS4 text color */}
                        <p className="lead text-muted mb-5">Everything you need for seamless transcription.</p> {/* Added margin bottom */}
                    </div>
                    <div className="row justify-content-center"> {/* Removed gx-5, use padding/margin */}
                        {/* Feature 1 */}
                        <div className="col-lg-4 mb-5">
                            <div className="card h-100 shadow-sm feature-card text-center p-4">
                                <div className="feature-icon-wrapper mx-auto">
                                    <i className="fas fa-cog"></i> {/* Font Awesome Icon */}
                                </div>
                                {/* BS4 font weight */}
                                <h3 className="h5 font-weight-bold mb-2 card-title">Well Programmed</h3>
                                <p className="card-text text-muted small">Accurate drafting with 15+ built-in commands including punctuation (comma, full stop, slash, etc.) for perfect recognition.</p>
                            </div>
                        </div>
                        {/* Feature 2 */}
                        <div className="col-lg-4 mb-5">
                            <div className="card h-100 shadow-sm feature-card text-center p-4">
                               <div className="feature-icon-wrapper mx-auto">
                                   <i className="fas fa-tachometer-alt"></i> {/* Font Awesome Icon */}
                               </div>
                               <h3 className="h5 font-weight-bold mb-2 card-title">Boosts Productivity</h3>
                               <p className="card-text text-muted small">Quickly document project details. Enables professionals to work independently and maintain records efficiently.</p>
                            </div>
                       </div>
                        {/* Feature 3 */}
                        <div className="col-lg-4 mb-5">
                            <div className="card h-100 shadow-sm feature-card text-center p-4">
                                <div className="feature-icon-wrapper mx-auto">
                                     <i className="fas fa-history"></i> {/* Font Awesome Icon */}
                                </div>
                                <h3 className="h5 font-weight-bold mb-2 card-title">Faster Process</h3>
                                <p className="card-text text-muted small">Up to 3x faster than typing with 99% accuracy (120+ wpm vs 40 wpm). Saves time and reduces grammatical errors.</p>
                            </div>
                        </div>
                         {/* Feature 4 */}
                         <div className="col-lg-4 mb-5 mb-lg-0">
                            <div className="card h-100 shadow-sm feature-card text-center p-4">
                                <div className="feature-icon-wrapper mx-auto">
                                     <i className="fas fa-language"></i> {/* Font Awesome Icon */}
                                </div>
                                <h3 className="h5 font-weight-bold mb-2 card-title">Multi-Language Support</h3>
                                <p className="card-text text-muted small">Transcribe data in 11 languages: Hindi, English, Bengali, Gujarati, Kannada, Malayalam, Marathi, Punjabi, Tamil, Telugu, Urdu.</p>
                            </div>
                        </div>
                         {/* Feature 5 */}
                         <div className="col-lg-4 mb-5 mb-lg-0">
                             <div className="card h-100 shadow-sm feature-card text-center p-4">
                                <div className="feature-icon-wrapper mx-auto">
                                     <i className="fas fa-clipboard-check"></i> {/* Font Awesome Icon */}
                                </div>
                                <h3 className="h5 font-weight-bold mb-2 card-title">Copy & Email</h3>
                                <p className="card-text text-muted small">Easily copy or email transcribed content for saving and sharing, improving team coordination.</p>
                            </div>
                        </div>
                        {/* Feature 6 - CTA Card */}
                        <div className="col-lg-4">
                           <div className="card h-100 shadow-sm feature-card text-center p-4 cta-card">
                                <div className="feature-icon-wrapper mx-auto bg-light">
                                     <i className="fas fa-check-circle" style={{ color: 'var(--primary-color)'}}></i> {/* Font Awesome Icon */}
                                </div>
                                <h3 className="h5 font-weight-bold mb-2 card-title">Get Started Today</h3>
                                <p className="card-text small">Ready to speed up your documentation?</p>
                                {/* Use RouterLink */}
                                <RouterLink className="btn btn-light mt-auto" to="/user/register">Register Now</RouterLink>
                           </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* How it Works Section */}
            <section className="py-5 how-it-works-section" id="how-it-works">
                <div className="container px-lg-5 px-3 my-5">
                    <div className="text-center section-title mb-5">
                        <h2 className="font-weight-bold">How It Works</h2>
                        <p className="lead text-muted">Simple steps to get started with speech-to-text.</p>
                    </div>
                    <div className="row justify-content-center">
                        {/* Step 1 */}
                        <div className="col-lg-4 mb-4">
                            <div className="text-center">
                                <div className="how-it-works-icon-wrapper mx-auto mb-3">
                                    <i className="fas fa-microphone"></i>
                                </div>
                                <h3 className="h5 font-weight-bold mb-2">1. Speak</h3>
                                <p className="text-muted">Click the microphone button and start speaking clearly.</p>
                            </div>
                        </div>
                        {/* Step 2 */}
                        <div className="col-lg-4 mb-4">
                            <div className="text-center">
                                <div className="how-it-works-icon-wrapper mx-auto mb-3">
                                    <i className="fas fa-sync-alt"></i>
                                </div>
                                <h3 className="h5 font-weight-bold mb-2">2. Transcribe</h3>
                                <p className="text-muted">Our system processes your speech in real-time.</p>
                            </div>
                        </div>
                        {/* Step 3 */}
                        <div className="col-lg-4 mb-4">
                            <div className="text-center">
                                <div className="how-it-works-icon-wrapper mx-auto mb-3">
                                    <i className="fas fa-file-alt"></i>
                                </div>
                                <h3 className="h5 font-weight-bold mb-2">3. Get Text</h3>
                                <p className="text-muted">Receive accurate, editable text instantly.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Testimonials Section */}
            <section className="py-5 testimonials-section" id="testimonials">
                <div className="container px-lg-5 px-3 my-5">
                    <div className="text-center section-title mb-5">
                        <h2 className="font-weight-bold">What Our Users Say</h2>
                        <p className="lead text-muted">Hear from satisfied customers.</p>
                    </div>
                    <div className="row justify-content-center">
                        {/* Testimonial 1 */}
                        <div className="col-lg-6 mb-4">
                            <div className="card h-100 shadow-sm testimonial-card p-4">
                                <div className="d-flex align-items-center mb-3">
                                    {/* Placeholder for user image */}
                                    <img src="https://via.placeholder.com/60" alt="User" className="rounded-circle mr-3" />
                                    <div>
                                        <h5 className="mb-0 font-weight-bold">Jane Doe</h5>
                                        <p className="text-muted small mb-0">Freelance Writer</p>
                                    </div>
                                </div>
                                <p className="card-text text-muted">"Daily Speaking has revolutionized my workflow. The accuracy is incredible, and it saves me hours of typing every week!"</p>
                            </div>
                        </div>
                        {/* Testimonial 2 */}
                        <div className="col-lg-6 mb-4">
                            <div className="card h-100 shadow-sm testimonial-card p-4">
                                <div className="d-flex align-items-center mb-3">
                                    {/* Placeholder for user image */}
                                    <img src="https://via.placeholder.com/60" alt="User" className="rounded-circle mr-3" />
                                    <div>
                                        <h5 className="mb-0 font-weight-bold">John Smith</h5>
                                        <p className="text-muted small mb-0">Medical Professional</p>
                                    </div>
                                </div>
                                <p className="card-text text-muted">"The multi-language support is a game-changer. I can easily transcribe notes in different languages without any hassle."</p>
                            </div>
                        </div>
                        {/* Add more testimonials as needed */}
                    </div>
                </div>
            </section>

            {/* Contact section - Adapted for BS4 */}
            <section className="contact-section bg-light py-5" id="contact"> {/* Changed bg */}
                <div className="container px-lg-5 px-3 my-5">
                    <div className="text-center mb-5">
                        <div className="section-icon mb-3">
                             <i className="fas fa-envelope-open-text" style={{fontSize: '2.5rem', color: 'var(--primary-color)'}}></i> {/* Changed icon */}
                        </div>
                        <h2 className="font-weight-bold">Get in Touch</h2>
                        <p className="lead text-muted mb-0">We'd love to hear from you. Contact us at +91 7678073260</p>
                    </div>
                    <div className="row justify-content-center">
                        <div className="col-lg-8 col-xl-7"> {/* Adjusted column width */}
                           <div className="card p-4 p-lg-5 border-0 shadow-lg contact-form-card">
                                <form id="contactForm" onSubmit={handleFormSubmit}>
                                    {/* BS4 requires form-group usually, but floating labels work differently */}
                                    {/* BS4 Floating Labels require specific structure */}
                                    <div className="form-label-group mb-3"> {/* BS4 Floating Label Structure */}
                                        <input className="form-control" id="name" type="text" placeholder="Enter your name..." required value={formData.name} onChange={handleInputChange} />
                                        <label htmlFor="name">Full name</label>
                                    </div>
                                    <div className="form-label-group mb-3">
                                        <input className="form-control" id="email" type="email" placeholder="<EMAIL>" required value={formData.email} onChange={handleInputChange} />
                                        <label htmlFor="email">Email address</label>
                                    </div>
                                   <div className="form-label-group mb-3">
                                        <input className="form-control" id="phone" type="tel" placeholder="(*************" required value={formData.phone} onChange={handleInputChange} />
                                        <label htmlFor="phone">Phone number</label>
                                    </div>
                                    <div className="form-label-group mb-3">
                                        <textarea className="form-control" id="message" placeholder="Enter your message here..." style={{ height: '10rem' }} required value={formData.message} onChange={handleInputChange}></textarea>
                                        <label htmlFor="message">Message</label>
                                    </div>

                                    {/* Submit status messages */}
                                    {formStatus.submitted && !formStatus.error && ( <div className="text-center text-success mb-3"><div className="font-weight-bold">{formStatus.message}</div></div> )}
                                    {formStatus.error && ( <div className="text-center text-danger mb-3">{formStatus.message}</div> )}
                                    {!formStatus.submitted && !formStatus.error && formStatus.message && ( <div className="text-center text-info mb-3">{formStatus.message}</div> )}

                                    {/* Submit Button */}
                                    <div className="d-grid"> {/* BS4 doesn't have d-grid, use button block */}
                                        <button className="btn btn-primary btn-lg btn-block" id="submitButton" type="submit" disabled={formStatus.message === 'Submitting...'}> Submit </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Footer - Adapted for BS4 */}
            <footer className="custom-footer py-4"> {/* Added padding */}
                <div className="container px-lg-5 px-3">
                    <div className="row align-items-center justify-content-between flex-column flex-sm-row">
                        <div className="col-auto mb-2 mb-sm-0"><p className="mb-0">Copyright © Daily Speaking 2023</p></div> {/* Removed margin bottom from p */}
                        <div className="col-auto footer-social-links">
                            {/* Use Font Awesome */}
                            <a href="#!" className="text-white mr-3"><i className="fab fa-twitter"></i></a>
                            <a href="#!" className="text-white mr-3"><i className="fab fa-facebook-f"></i></a>
                            <a href="#!" className="text-white mr-3"><i className="fab fa-linkedin-in"></i></a>
                            <a href="#!" className="text-white"><i className="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
            </footer>
        </>
    );
}

export default LandingPage; // Renamed component