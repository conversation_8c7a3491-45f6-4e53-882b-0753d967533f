// src/pages/admin/AdminsListPage.js
import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { getAdmins } from '../../services/api'; // Assuming getAdmins is defined in api.js
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';

function AdminsListPage() {
    const [admins, setAdmins] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paginationMeta, setPaginationMeta] = useState(null);

    // Fetch admin list
    const fetchAdmins = useCallback(async (page = 1) => {
        setLoading(true);
        setError(null);
        try {
            const response = await getAdmins({ page }); // Use getAdmins function from api.js
            setAdmins(response.data.data);
            setPaginationMeta(response.data.meta);
        } catch (err) {
            console.error("Failed to fetch admins:", err);
            setError('Failed to load admins.');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchAdmins(paginationMeta?.current_page || 1);
    }, [fetchAdmins]);

    const handlePageChange = (newPage) => {
        fetchAdmins(newPage);
    };

    // Note: Add/Edit/Delete functionality for admins is not included here
    // but could be added following the pattern of Users/Plans if needed.

    return (
        <div>
            {/* Content Header */}
            <div className="content-header">
                <div className="container-fluid">
                    <div className="row mb-2">
                        <div className="col-sm-6"><h1 className="m-0">Administrators</h1></div>
                        <div className="col-sm-6">
                            <ol className="breadcrumb float-sm-right">
                                <li className="breadcrumb-item"><Link to="/">Home</Link></li>
                                <li className="breadcrumb-item active">Admins</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <section className="content">
                <div className="card">
                    <div className="card-header">
                        <h3 className="card-title">Admin List</h3>
                        {/* Optional: Add 'Add New Admin' button if implementing create functionality */}
                        {/* <div className="card-tools">
                            <Link to="/admins/create" className="btn btn-success btn-sm">
                                <i className="fas fa-plus mr-1"></i> Add New Admin
                            </Link>
                        </div> */}
                    </div>
                    <div className="card-body">
                        {error && <div className="alert alert-danger">{error}</div>}

                        {loading ? (
                            <LoadingSpinner />
                        ) : (
                            <>
                                <div className="table-responsive">
                                    <table className="table table-bordered table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Username</th>
                                                <th>Created At</th>
                                                {/* Add Actions column if implementing edit/delete */}
                                                {/* <th>Actions</th> */}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {admins.length > 0 ? admins.map(admin => (
                                                <tr key={admin.id}>
                                                    <td>{admin.id}</td>
                                                    <td>{admin.username}</td>
                                                    <td>{new Date(admin.created_at).toLocaleString()}</td>
                                                    {/* Add action buttons here if needed */}
                                                    {/* <td>...</td> */}
                                                </tr>
                                            )) : (
                                                <tr>
                                                    <td colSpan="3" className="text-center text-muted">No admins found.</td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-3">
                                    <Pagination meta={paginationMeta} onPageChange={handlePageChange} />
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </section>
        </div>
    );
}

export default AdminsListPage;