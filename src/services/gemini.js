import { GoogleGenerativeAI } from "@google/generative-ai";

const API_KEY = process.env.REACT_APP_GEMINI_API_KEY;

const getGenAI = () => {
    if (!API_KEY) {
        throw new Error("REACT_APP_GEMINI_API_KEY is not set. Please create a .env file in the project root with the API key.");
    }
    return new GoogleGenerativeAI(API_KEY);
}

const getModel = () => {
    const genAI = getGenAI();
    return genAI.getGenerativeModel({
        model: "gemma-3-27b-it",
    });
}

const modelResponseExample = '```json\n' +
    '{\n' +
    '  "source_language": "en",\n' +
    '  "translated_text": "क्या आप कृपया इस दस्तावेज़ का हिंदी में अनुवाद कर सकते हैं? इसमें सॉफ्टवेयर इंजीनियरिंग से संबंधित तकनीकी शब्द शामिल हैं।",\n' +
    '  "transliteration": null,\n' +
    '  "alternative_phrasings": [\n' +
    '    "क्या आप इस दस्तावेज़ का हिंदी अनुवाद कर सकते हैं? इसमें सॉफ्टवेयर इंजीनियरिंग के तकनीकी शब्द हैं।",\n' +
    '    "कृपया इस दस्तावेज़ को हिंदी में अनुवादित करें, इसमें सॉफ्टवेयर इंजीनियरिंग से जुड़े तकनीकी शब्दावली है।"\n' +
    '  ]\n' +
    '}\n' +
    '```';

const cleanHtmlResponse = (responseText) => {
    if (responseText.startsWith("```html")) {
        return responseText.substring(7, responseText.length - 3).trim();
    }
    return responseText;
};



export async function translateText(text, targetLanguageName, targetLanguageCode) {
    const model = getModel();

    const escapedText = text.replace(/"/g, '\\"').replace(/\n/g, "\\n"); // Escape double quotes and newlines for JSON

    try {
        const result = await model.generateContent({
            contents: [{ role: "user", parts: [{ text: `Translate the following text into ${targetLanguageName}. Provide only the translated text, without any additional comments or conversational filler.\n\nText to translate: "${escapedText}"` }] }],
            generationConfig: {
                maxOutputTokens: 8192,
                temperature: 0.2,
            },
        });
        const response = result.response;
        return response.candidates[0].content.parts[0].text;

    } catch (error) {
        console.error("Error translating text with Gemini:", error);
        throw new Error("Translation failed. Please try again.");
    }
}
export async function fixSpellingAndGrammar(text) {
    const genAI = getGenAI();
    const model = getModel(); // Use the model from getModel() which is now gemini-2.0-flash-lite

    try {
        const result = await model.generateContent({
            contents: [{ role: "user", parts: [{ text: `Please correct the spelling and grammar of the following HTML content. Keep the original meaning, tone, and all HTML tags (like <p>, <b>, <h1>, etc.) and their structure intact. Only return the corrected HTML content, without any additional comments or explanations.\n\nOriginal HTML:\n${text}\n\nCorrected HTML:` }] }],
            generationConfig: {
                maxOutputTokens: 8192,
                temperature: 0.2,
            },
        });
        const response = result.response;
        return cleanHtmlResponse(response.candidates[0].content.parts[0].text);
    } catch (error) {
        console.error("Error fixing spelling and grammar:", error);
        throw new Error("Failed to fix spelling and grammar. Please try again.");
    }
}

export async function expandContent(text) {
    const model = getModel(); // Use gemini-1.5-flash model

    try {
        const result = await model.generateContent(`Expand on the following text, providing more detail and explanation. Generate detailed paragraphs or explanations based on the input. The output should be a single block of professionally formatted HTML, styled as a document. Use inline CSS for styling. Paragraphs (<p>) should have a bottom margin (e.g., 'margin-bottom: 12px;'). Use other tags like <b>, <i>, and <ul> as appropriate for clear, readable formatting. Only return the expanded HTML content, without any additional comments or conversational filler.\n\nOriginal Text:\n\"${text}\"\n\nExpanded HTML Content:`);
        const response = result.response;
        return cleanHtmlResponse(response.candidates[0].content.parts[0].text);
    } catch (error) {
        console.error("Error expanding content:", error);
        throw new Error("Failed to expand content. Please try again.");
    }
}

export async function generateDraft(userPrompt) {
    const model = getModel(); // Use gemini-1.5-flash model

    try {
        const result = await model.generateContent(`Generate a detailed and comprehensive draft based on the following idea or topic. Provide a well-structured and coherent text in a single block of professionally formatted HTML, styled as a document. Use inline CSS for styling. Use tags like <h1>, <h2>, and <p> with appropriate margins (e.g., 'margin-bottom: 12px;' for paragraphs). Use other tags like <ul>, <li>, <b>, and <i> for clear, readable formatting. Only return the generated HTML draft, without any additional comments or conversational filler.\n\nIdea/Topic:\n"${userPrompt}"\n\nDetailed HTML Draft:`);
        const response = result.response;
        return cleanHtmlResponse(response.candidates[0].content.parts[0].text);
    } catch (error) {
        console.error("Error generating draft:", error);
        throw new Error("Failed to generate draft. Please try again.");
    }
}

export async function askGemini(conversationHistory) {
    const genAI = getGenAI();
    const model = genAI.getGenerativeModel({ model: "gemma-3-27b-it" }); // Explicitly use gemma-3-27b-it for chatbot

    // Define the persistent AI persona and directives
    const AI_PROMPT_PREFIX = `
# PERSONA
You are 'NyayaAI', an AI legal analyst specializing in the laws of India. Your expertise covers statutory law, case law from the Supreme Court and High Courts, and general legal principles.

# DIRECTIVES
1.  **Language Adaptation:** ALWAYS detect the language of the user's query and respond ONLY in that same language. For example, if the user asks in Marathi, respond in Marathi; if in Hindi, respond in Hindi; if in English, respond in English.
2.  **Analyze Thoroughly:** Provide a detailed and well-structured analysis of the user's query. Prioritize accuracy and comprehensive reasoning over simple conciseness.
2.  **Cite Sources:** Where possible, cite the relevant section of a statute (e.g., Section 420 of the Indian Penal Code, 1860) or a key Supreme Court precedent that establishes the legal principle.
3.  **Clarify Ambiguity:** If the user's query is ambiguous, incomplete, or lacks critical context, your **FIRST and ONLY** step is to ask specific, targeted clarifying questions to get the necessary information. **Do NOT provide any analysis until you have sufficient information.** For example, if asked about a real estate dispute, you might ask:
    *   Is the project registered under the Real Estate (Regulation and Development) Act, 2016 (RERA)?
    *   Does your 'Agreement for Sale' explicitly mention a penalty or interest for delayed possession?
    *   Have you sent any formal communication (like a letter or email) to the builder about the delay?
4.  **Structure Your Response (Only after clarification, if needed):** Once you have gathered sufficient information, organize your answer into the following sections:
    *   **A. Core Legal Principles:** Briefly explain the fundamental laws and legal principles that govern the query.
    *   **B. Application to the Query:** Apply these principles to the user's specific situation.
    *   **C. Nuances & Considerations:** Discuss any important exceptions, "it depends on" factors, or practical considerations.

# BOUNDARIES
Always conclude your response with the following mandatory disclaimer: "This analysis is for informational and educational purposes only, based on the information provided. It does not constitute legal advice. You should consult with a qualified lawyer licensed in India for advice on your specific situation."

# CONVERSATION START
`;

    // Map the conversation history to the Gemini API's 'contents' format
    const contents = conversationHistory.map((msg, index) => {
        let textContent = msg.text;
        // Prepend the AI_PROMPT_PREFIX to the very first user message only
        if (index === 0 && msg.sender === 'user') {
            textContent = AI_PROMPT_PREFIX + "Analyze the following query: " + textContent + "\n\nProvide the response as a single block of professionally formatted HTML, styled with inline CSS for a beautiful UI. Do not include any external CSS or JavaScript. Ensure paragraphs (<p>) have a bottom margin (e.g., 'margin-bottom: 12px;'). Use other tags like <b>, <i>, <ul>, and <li> as appropriate for clear, readable formatting. Only return the HTML content, without any additional comments or conversational filler.";
        }
        return {
            role: msg.sender === 'user' ? 'user' : 'model', // 'ai' sender becomes 'model' role
            parts: [{ text: textContent }],
        };
    });

    try {
        const result = await model.generateContent({
            contents: contents, // Pass the full conversation history
            generationConfig: {
                maxOutputTokens: 2048, // Adjust as needed
                temperature: 0.1, // Lower temperature for more factual responses
            },
        });
        const response = result.response;
        return cleanHtmlResponse(response.candidates[0].content.parts[0].text); // Clean the HTML response
    } catch (error) {
        console.error("Error asking Gemini:", error);
        throw new Error("Failed to get a response from the AI. Please try again.");
    }
}