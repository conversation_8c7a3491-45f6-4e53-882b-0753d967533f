// src/services/api.js
import axios from 'axios';

// ***** IMPORTANT: SET YOUR LARAVEL API URL HERE *****
const API_BASE_URL = 'https://api.dailyspeaking.in/api'; // Adjust domain/port if needed

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// --- Request Interceptor (Inject Token) ---
// src/services/api.js - Request Interceptor Idea
apiClient.interceptors.request.use(
  (config) => {
    let token = null;
    // Determine if it's an admin or user route being called
    const fullUrl = config.url.startsWith('http') ? config.url : `${config.baseURL}${config.url}`;

    if (fullUrl.includes('/admin/')) {
      token = localStorage.getItem('adminToken');
      console.log("Interceptor: Attaching ADMIN token");
    } else if (fullUrl.includes('/user/')) {
      // Add exceptions for /user/login, /user/register which don't need a token
      if (!fullUrl.endsWith('/user/login') && !fullUrl.endsWith('/user/register')) {
        token = localStorage.getItem('userToken');
        console.log("Interceptor: Attaching USER token");
      }
    }
    // Add more specific logic if needed

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    } else {
      // Ensure no old/incorrect Authorization header persists
      delete config.headers['Authorization'];
      console.log("Interceptor: No token attached for URL:", config.url);
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// --- Response Interceptor (Handle 401) ---
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      console.error(`API Error Response: Status=${status}`, data);

      if (status === 401) {
        // Check for the specific single-device logout code
        if (data?.code === 'SESSION_EXPIRED_NEW_LOGIN') {
          console.warn("API Interceptor: Session expired due to new login (401).");
          localStorage.removeItem('userToken'); // Clear user token/data
          localStorage.removeItem('user');
          // Let AuthContext handle the dialog display
          // The AuthContext interceptor will show the appropriate dialog
        } else {
          // Handle other generic 401 errors
          console.error("API Interceptor: Unauthorized (401). Logging out.");
          localStorage.removeItem('adminToken'); localStorage.removeItem('adminUser');
          localStorage.removeItem('userToken'); localStorage.removeItem('user');
          if (window.location.pathname !== '/login' && window.location.pathname !== '/user/login') {
            window.location.href = '/user/login'; // Default redirect
          }
        }
      } else if (status === 403 && data?.code && (data.code === 'ACCOUNT_INACTIVE' || data.code === 'PLAN_EXPIRED')) {
        // Handle inactive/expired - let AuthContext handle the dialog
        console.error(`API Interceptor: Forbidden (403) - Code: ${data.code}`);
        localStorage.removeItem('userToken'); localStorage.removeItem('user');
        // AuthContext interceptor will handle showing the dialog
      } else if (status >= 500) {
        // Let AuthContext handle server error dialogs
        console.error("API Interceptor: Server Error (5xx)");
      }
      // Handle other non-critical errors silently or with specific UI feedback

    } else {
      console.error("Network or other error:", error);
      // Let AuthContext handle network error dialogs
    }
    return Promise.reject(error); // Still reject the promise for component handling
  }
);

// --- API Function Definitions ---

// Auth
export const loginAdmin = (credentials) => apiClient.post('/admin/login', credentials);
export const getAdminProfile = () => apiClient.get('/admin/me');
export const logoutAdmin = () => apiClient.post('/admin/logout');

// Users (Admin perspective)
export const getUsers = (params = {}) => apiClient.get('/admin/users', { params });
export const updateUserStatus = (id, isActive) => { // Or (id, status)
  // Use PATCH (or PUT) as defined in your route
  return apiClient.patch(`/admin/users/${id}/status`, { is_active: isActive }); // or { status: status }
};
export const getUser = (id) => apiClient.get(`/admin/users/${id}`);
export const createUser = (userData) => apiClient.post('/admin/users', userData);
export const updateUser = (id, userData) => apiClient.put(`/admin/users/${id}`, userData);
export const deleteUser = (id) => apiClient.delete(`/admin/users/${id}`);

// Plans (Admin perspective)
export const getPlans = (params = {}) => apiClient.get('/admin/plans', { params });
export const getPlan = (id) => apiClient.get(`/admin/plans/${id}`);
export const createPlan = (planData) => apiClient.post('/admin/plans', planData);
export const updatePlan = (id, planData) => apiClient.put(`/admin/plans/${id}`, planData);
export const deletePlan = (id) => apiClient.delete(`/admin/plans/${id}`);

// Admins (Admin perspective)
export const getAdmins = (params = {}) => apiClient.get('/admin/admins', { params });
// Add create/update/delete for Admins later if needed

// --- User Auth --- NEW FUNCTIONS ---
export const loginUser = (credentials) => {
  // Login doesn't need a token initially
  return apiClient.post('/user/login', credentials);
};

export const forceLoginUser = (credentials) => {
  // Force login with session override
  return apiClient.post('/user/login', { ...credentials, force_login: true });
};

export const registerUser = (userData) => {
  // Registration doesn't need a token initially
  return apiClient.post('/user/register', userData);
};

export const getUserProfile = () => {
  console.warn("getUserProfile API call assumes interceptor sends USER token correctly!");
  return apiClient.get('/user/me');
};

// --- User Content History ---
export const saveContent = (contentData) => {
  return apiClient.post('/user/content-history', contentData);
};

export const getContentHistory = (url = null) => {
  // If a URL is provided, use it directly (for pagination links)
  // Otherwise, use the base endpoint
  const endpoint = url || '/user/content-history';
  return apiClient.get(endpoint);
};

export const getContentById = (id) => {
  return apiClient.get(`/user/content-history/${id}`);
};

export const updateContent = (id, contentData) => {
  return apiClient.put(`/user/content-history/${id}`, contentData);
};

export const deleteContent = (id) => {
  return apiClient.delete(`/user/content-history/${id}`);
};


export default apiClient; // Export configured instance if needed elsewhere