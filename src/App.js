// src/App.js
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import { AuthProvider } from './context/AuthContext';

// Layouts and Guards
import AdminProtectedRoute from './components/ProtectedRoute'; // Renamed for clarity
import UserProtectedRoute from './components/UserProtectedRoute'; // Import User guard

// --- Pages ---
// Public Pages
import LandingPage from './pages/LandingPage';
import AdminLoginPage from './pages/auth/LoginPage';
import UserLoginPage from './pages/auth/UserLoginPage';
import UserRegisterPage from './pages/auth/UserRegisterPage';

// Admin Protected Pages
import DashboardPage from './pages/DashboardPage'; // Admin Dashboard
import UsersListPage from './pages/user/UsersListPage';
import UserCreatePage from './pages/user/UserCreatePage';
import UserEditPage from './pages/user/UserEditPage';
import PlansListPage from './pages/plan/PlansListPage';
import PlanCreatePage from './pages/plan/PlanCreatePage';
import PlanEditPage from './pages/plan/PlanEditPage';
import AdminsListPage from './pages/admin/AdminsListPage';

// User Protected Pages
import ModernSpeechEditor from './pages/editor/ModernSpeechEditor'; // Import the editor
import LegacyFontConverter from './pages/converter/LegacyFontConverter'; // Import the new converter page
import ContentHistoryPage from './pages/user/ContentHistoryPage'; // Import the new content history page
import ChatWithAIPage from './pages/ChatWithAIPage'; // Import the new Chat with AI page

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* --- Public Routes --- */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<AdminLoginPage />} /> {/* Admin login */}
          <Route path="/user/login" element={<UserLoginPage />} />
          <Route path="/user/register" element={<UserRegisterPage />} />

          {/* --- Protected Admin Routes --- */}
          {/* These require admin login and use AdminLTE layout via AdminProtectedRoute */}
          <Route element={<AdminProtectedRoute />}>
             <Route path="/dashboard" element={<DashboardPage />} /> {/* Admin Dashboard */}
             <Route path="/users" element={<UsersListPage />} />
             <Route path="/users/create" element={<UserCreatePage />} />
             <Route path="/users/edit/:userId" element={<UserEditPage />} />
             <Route path="/plans" element={<PlansListPage />} />
             <Route path="/plans/create" element={<PlanCreatePage />} />
             <Route path="/plans/edit/:planId" element={<PlanEditPage />} />
             <Route path="/admins" element={<AdminsListPage />} />
          </Route>

          {/* --- Protected User Routes --- */}
          {/* These require user login */}
          <Route element={<UserProtectedRoute />}>
              {/* Main route for logged-in users */}
              <Route path="/user/editor" element={<ModernSpeechEditor />} />
              <Route path="/user/converter" element={<LegacyFontConverter />} /> {/* Add route for converter */}
              <Route path="/user/content-history" element={<ContentHistoryPage />} /> {/* New route for content history */}
              <Route path="/user/chat-with-ai" element={<ChatWithAIPage />} /> {/* New route for Chat with AI */}
              {/* Add other user-specific routes here if needed later */}
              {/* e.g., <Route path="/user/profile" element={<UserProfilePage />} /> */}
          </Route>


          {/* --- Fallback Route --- */}
          {/* Redirect unknown paths to the landing page */}
          <Route path="*" element={<Navigate to="/" replace />} />

        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;