// src/layouts/AdminLayout.js
import React, { useEffect } from 'react';
import { Outlet } from 'react-router-dom';

// Import the layout components
import Navbar from '../components/layout/Navbar';
import Sidebar from '../components/layout/Sidebar';
import Footer from '../components/layout/Footer';

function AdminLayout() {

    // Effect to manage body classes for the main layout
    useEffect(() => {
        document.body.classList.remove('login-page');
        document.body.classList.add('sidebar-mini');
        // Add other classes like layout-fixed if needed

        // Cleanup function
        return () => {
            document.body.classList.remove('sidebar-mini');
            // Remove other classes you added
        };
    }, []);

    return (
        // The 'wrapper' class is on #root in index.html
        <>
            <Navbar />
            <Sidebar />
            <div className="content-wrapper">
                <Outlet /> {/* Child route component renders here */}
            </div>
            <Footer />
            {/* <aside className="control-sidebar control-sidebar-dark"></aside> */}
        </>
    );
}

export default AdminLayout;