<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\User\AuthController as UserAuthController;
use App\Http\Controllers\Api\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\Api\Admin\UserController as AdminUserController;
use App\Http\Controllers\Api\Admin\PlanController as AdminPlanController;
use App\Http\Controllers\Api\User\ContentHistoryController;
// Use App\Http\Controllers\Api\User\ForgotPasswordController etc. if using password reset

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });


// --- User Authentication and Profile ---
Route::prefix('user')->name('api.user.')->group(function () {
    Route::post('/register', [UserAuthController::class, 'register'])->name('register');
    Route::post('/login', [UserAuthController::class, 'login'])->name('login');

    // Routes requiring authentication AND single device check
    Route::middleware(['auth:api','auth.user.status', 'auth.single_device:api'])->group(function () {
        Route::post('/logout', [UserAuthController::class, 'logout'])->name('logout');
        Route::post('/refresh', [UserAuthController::class, 'refresh'])->name('refresh');
        Route::get('/me', [UserAuthController::class, 'me'])->name('me');
        Route::put('/profile', [UserAuthController::class, 'updateProfile'])->name('profile.update');
        Route::apiResource('content-history', ContentHistoryController::class);
        
        // Session management routes
        Route::get('/sessions', [\App\Http\Controllers\Api\User\SessionController::class, 'index'])->name('sessions.index');
        Route::delete('/sessions/{sessionId}', [\App\Http\Controllers\Api\User\SessionController::class, 'destroy'])->name('sessions.destroy');
        Route::delete('/sessions/others/terminate', [\App\Http\Controllers\Api\User\SessionController::class, 'destroyOthers'])->name('sessions.destroyOthers');
    });

     // Password Reset Routes (Example - uses standard Laravel flow names)
    // Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    // Route::post('/password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');
});


// --- Admin Authentication and Management ---
Route::prefix('admin')->name('api.admin.')->group(function () {
    Route::post('/login', [AdminAuthController::class, 'login'])->name('login');

    // Routes requiring admin authentication
    Route::middleware(['auth:admin_api'])->group(function () { // Add 'auth.single_device:admin_api' if needed
        Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');
        Route::patch('/users/{user}/status', [AdminUserController::class, 'updateStatus'])->name('users.updateStatus');
        Route::post('/refresh', [AdminAuthController::class, 'refresh'])->name('refresh');
        Route::get('/me', [AdminAuthController::class, 'me'])->name('me');

        // Resource routes for managing users and plans
        Route::apiResource('users', AdminUserController::class);
        Route::apiResource('plans', AdminPlanController::class);
    });
});
